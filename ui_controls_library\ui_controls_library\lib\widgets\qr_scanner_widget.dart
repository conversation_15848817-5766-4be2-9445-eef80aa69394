import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension QrScannerColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

enum ScannerOverlayShape { rectangle, square, circle, none }

class QrScannerWidget extends StatefulWidget {
  /// Whether to show a flash/torch button
  final bool showFlash;

  /// Whether to start with the flash/torch on
  final bool initialFlashState;

  /// Whether to show a guide overlay
  final bool showGuide;

  /// The shape of the scanner overlay
  final ScannerOverlayShape overlayShape;

  /// The color of the scanner overlay
  final Color overlayColor;

  /// The color of the scan area
  final Color scanAreaColor;

  /// The border color of the scan area
  final Color borderColor;

  /// The width of the border
  final double borderWidth;

  /// The border radius of the scan area (when not using circle shape)
  final double borderRadius;

  /// The size of the scan area
  final double scanAreaSize;

  /// Whether to show scan history
  final bool showHistory;

  /// Maximum number of history items to keep
  final int maxHistoryItems;

  /// Whether to vibrate on successful scan
  final bool vibrateOnScan;

  /// Whether to play a sound on successful scan
  final bool playSoundOnScan;

  /// Whether to automatically stop scanning after first result
  final bool stopAfterFirstScan;

  /// Callback when a code is scanned
  final Function(String)? onScan;

  /// Callback when an error occurs
  final Function(String)? onError;

  /// Text to display when no camera permission is granted
  final String permissionDeniedText;

  /// Text to display on the flash button when flash is off
  final String flashOffText;

  /// Text to display on the flash button when flash is on
  final String flashOnText;

  /// Icon to display on the flash button when flash is off
  final IconData flashOffIcon;

  /// Icon to display on the flash button when flash is on
  final IconData flashOnIcon;

  /// Text to display on the history button
  final String historyText;

  /// Icon to display on the history button
  final IconData historyIcon;

  /// Text to display when no scan history is available
  final String noHistoryText;

  /// Text to display as the title of the history dialog
  final String historyDialogTitle;

  /// Text to display on the close button of the history dialog
  final String closeButtonText;

  /// Text to display on the clear button of the history dialog
  final String clearButtonText;

  /// Whether to show a cancel button
  final bool showCancelButton;

  /// Text to display on the cancel button
  final String cancelButtonText;

  /// Icon to display on the cancel button
  final IconData cancelButtonIcon;

  /// Callback when the cancel button is pressed
  final VoidCallback? onCancel;

  /// Whether to use the front camera
  final bool useFrontCamera;

  /// Whether to show a camera switch button
  final bool showCameraSwitch;

  /// Text to display on the camera switch button
  final String cameraSwitchText;

  /// Icon to display on the camera switch button when using back camera
  final IconData backCameraIcon;

  /// Icon to display on the camera switch button when using front camera
  final IconData frontCameraIcon;

  /// Whether to show a scanning animation
  final bool showScanningAnimation;

  /// The color of the scanning animation
  final Color scanningAnimationColor;

  /// The duration of the scanning animation
  final Duration scanningAnimationDuration;

  /// Whether to show a result dialog after scanning
  final bool showResultDialog;

  /// Text to display as the title of the result dialog
  final String resultDialogTitle;

  /// Text to display on the copy button of the result dialog
  final String copyButtonText;

  /// Text to display on the open button of the result dialog
  final String openButtonText;

  /// Text to display on the close button of the result dialog
  final String resultCloseButtonText;

  /// Whether to show a copy button in the result dialog
  final bool showCopyButton;

  /// Whether to show an open button in the result dialog
  final bool showOpenButton;

  /// Whether to show a close button in the result dialog
  final bool showCloseButton;

  /// Whether to show a help button
  final bool showHelpButton;

  /// Text to display on the help button
  final String helpButtonText;

  /// Icon to display on the help button
  final IconData helpButtonIcon;

  /// Text to display in the help dialog
  final String helpText;

  /// Text to display as the title of the help dialog
  final String helpDialogTitle;

  /// Text to display on the close button of the help dialog
  final String helpCloseButtonText;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Scanner-specific JSON configuration
  /// Whether to use JSON scanner configuration
  final bool useJsonScannerConfig;

  /// Scanner-specific JSON configuration
  final Map<String, dynamic>? scannerConfig;

  const QrScannerWidget({
    super.key,
    this.showFlash = false,
    this.initialFlashState = false,
    this.showGuide = true,
    this.overlayShape = ScannerOverlayShape.rectangle,
    this.overlayColor = const Color(0x80000000),
    this.scanAreaColor = Colors.transparent,
    this.borderColor = Colors.white,
    this.borderWidth = 3.0,
    this.borderRadius = 10.0,
    this.scanAreaSize = 250.0,
    this.showHistory = false,
    this.maxHistoryItems = 10,
    this.vibrateOnScan = false,
    this.playSoundOnScan = false,
    this.stopAfterFirstScan = false,
    this.onScan,
    this.onError,
    this.permissionDeniedText =
        'Camera permission is required to scan QR codes',
    this.flashOffText = 'Flash',
    this.flashOnText = 'Flash',
    this.flashOffIcon = Icons.flash_off,
    this.flashOnIcon = Icons.flash_on,
    this.historyText = 'History',
    this.historyIcon = Icons.history,
    this.noHistoryText = 'No scan history',
    this.historyDialogTitle = 'Scan History',
    this.closeButtonText = 'Close',
    this.clearButtonText = 'Clear',
    this.showCancelButton = true,
    this.cancelButtonText = 'Cancel',
    this.cancelButtonIcon = Icons.close,
    this.onCancel,
    this.useFrontCamera = false,
    this.showCameraSwitch = false,
    this.cameraSwitchText = 'Switch Camera',
    this.backCameraIcon = Icons.camera_rear,
    this.frontCameraIcon = Icons.camera_front,
    this.showScanningAnimation = true,
    this.scanningAnimationColor = Colors.white,
    this.scanningAnimationDuration = const Duration(seconds: 2),
    this.showResultDialog = false,
    this.resultDialogTitle = 'Scan Result',
    this.copyButtonText = 'Copy',
    this.openButtonText = 'Open',
    this.resultCloseButtonText = 'Close',
    this.showCopyButton = true,
    this.showOpenButton = true,
    this.showCloseButton = true,
    this.showHelpButton = false,
    this.helpButtonText = 'Help',
    this.helpButtonIcon = Icons.help,
    this.helpText = 'Position the QR code within the scan area to scan it.',
    this.helpDialogTitle = 'Help',
    this.helpCloseButtonText = 'Got it',
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Scanner-specific JSON configuration
    this.useJsonScannerConfig = false,
    this.scannerConfig,
  });

  /// Creates a QrScannerWidget from a JSON map
  ///
  /// This factory constructor allows for creating a QrScannerWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory QrScannerWidget.fromJson(Map<String, dynamic> json) {
    // Parse overlay shape
    ScannerOverlayShape overlayShape = ScannerOverlayShape.rectangle;
    if (json.containsKey('overlayShape')) {
      final String shapeStr = json['overlayShape'].toString().toLowerCase();
      if (shapeStr == 'square') {
        overlayShape = ScannerOverlayShape.square;
      } else if (shapeStr == 'circle') {
        overlayShape = ScannerOverlayShape.circle;
      } else if (shapeStr == 'none') {
        overlayShape = ScannerOverlayShape.none;
      }
    }

    // Parse colors
    Color overlayColor = const Color(0x80000000);
    if (json.containsKey('overlayColor')) {
      overlayColor = _parseColor(json['overlayColor']);
    }

    Color scanAreaColor = Colors.transparent;
    if (json.containsKey('scanAreaColor')) {
      scanAreaColor = _parseColor(json['scanAreaColor']);
    }

    Color borderColor = Colors.white;
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    Color scanningAnimationColor = Colors.white;
    if (json.containsKey('scanningAnimationColor')) {
      scanningAnimationColor = _parseColor(json['scanningAnimationColor']);
    }

    Color? hoverColor;
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse icons
    IconData flashOffIcon = Icons.flash_off;
    if (json.containsKey('flashOffIcon')) {
      flashOffIcon = _parseIconData(json['flashOffIcon']) ?? Icons.flash_off;
    }

    IconData flashOnIcon = Icons.flash_on;
    if (json.containsKey('flashOnIcon')) {
      flashOnIcon = _parseIconData(json['flashOnIcon']) ?? Icons.flash_on;
    }

    IconData historyIcon = Icons.history;
    if (json.containsKey('historyIcon')) {
      historyIcon = _parseIconData(json['historyIcon']) ?? Icons.history;
    }

    IconData cancelButtonIcon = Icons.close;
    if (json.containsKey('cancelButtonIcon')) {
      cancelButtonIcon =
          _parseIconData(json['cancelButtonIcon']) ?? Icons.close;
    }

    IconData backCameraIcon = Icons.camera_rear;
    if (json.containsKey('backCameraIcon')) {
      backCameraIcon =
          _parseIconData(json['backCameraIcon']) ?? Icons.camera_rear;
    }

    IconData frontCameraIcon = Icons.camera_front;
    if (json.containsKey('frontCameraIcon')) {
      frontCameraIcon =
          _parseIconData(json['frontCameraIcon']) ?? Icons.camera_front;
    }

    IconData helpButtonIcon = Icons.help;
    if (json.containsKey('helpButtonIcon')) {
      helpButtonIcon = _parseIconData(json['helpButtonIcon']) ?? Icons.help;
    }

    // Parse animation duration
    Duration scanningAnimationDuration = const Duration(seconds: 2);
    if (json.containsKey('scanningAnimationDuration')) {
      scanningAnimationDuration = Duration(
        milliseconds: json['scanningAnimationDuration'] as int? ?? 2000,
      );
    }

    return QrScannerWidget(
      // Display options
      showFlash: json['showFlash'] as bool? ?? false,
      initialFlashState: json['initialFlashState'] as bool? ?? false,
      showGuide: json['showGuide'] as bool? ?? true,
      overlayShape: overlayShape,
      showHistory: json['showHistory'] as bool? ?? false,
      maxHistoryItems: json['maxHistoryItems'] as int? ?? 10,
      vibrateOnScan: json['vibrateOnScan'] as bool? ?? false,
      playSoundOnScan: json['playSoundOnScan'] as bool? ?? false,
      stopAfterFirstScan: json['stopAfterFirstScan'] as bool? ?? false,
      showCancelButton: json['showCancelButton'] as bool? ?? true,
      useFrontCamera: json['useFrontCamera'] as bool? ?? false,
      showCameraSwitch: json['showCameraSwitch'] as bool? ?? false,
      showScanningAnimation: json['showScanningAnimation'] as bool? ?? true,
      showResultDialog: json['showResultDialog'] as bool? ?? false,
      showCopyButton: json['showCopyButton'] as bool? ?? true,
      showOpenButton: json['showOpenButton'] as bool? ?? true,
      showCloseButton: json['showCloseButton'] as bool? ?? true,
      showHelpButton: json['showHelpButton'] as bool? ?? false,

      // Colors
      overlayColor: overlayColor,
      scanAreaColor: scanAreaColor,
      borderColor: borderColor,
      scanningAnimationColor: scanningAnimationColor,

      // Dimensions
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 3.0,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 10.0,
      scanAreaSize:
          json['scanAreaSize'] != null
              ? (json['scanAreaSize'] as num).toDouble()
              : 250.0,

      // Text labels
      permissionDeniedText:
          json['permissionDeniedText'] as String? ??
          'Camera permission is required to scan QR codes',
      flashOffText: json['flashOffText'] as String? ?? 'Flash',
      flashOnText: json['flashOnText'] as String? ?? 'Flash',
      historyText: json['historyText'] as String? ?? 'History',
      noHistoryText: json['noHistoryText'] as String? ?? 'No scan history',
      historyDialogTitle:
          json['historyDialogTitle'] as String? ?? 'Scan History',
      closeButtonText: json['closeButtonText'] as String? ?? 'Close',
      clearButtonText: json['clearButtonText'] as String? ?? 'Clear',
      cancelButtonText: json['cancelButtonText'] as String? ?? 'Cancel',
      cameraSwitchText: json['cameraSwitchText'] as String? ?? 'Switch Camera',
      resultDialogTitle: json['resultDialogTitle'] as String? ?? 'Scan Result',
      copyButtonText: json['copyButtonText'] as String? ?? 'Copy',
      openButtonText: json['openButtonText'] as String? ?? 'Open',
      resultCloseButtonText:
          json['resultCloseButtonText'] as String? ?? 'Close',
      helpButtonText: json['helpButtonText'] as String? ?? 'Help',
      helpText:
          json['helpText'] as String? ??
          'Position the QR code within the scan area to scan it.',
      helpDialogTitle: json['helpDialogTitle'] as String? ?? 'Help',
      helpCloseButtonText: json['helpCloseButtonText'] as String? ?? 'Got it',

      // Icons
      flashOffIcon: flashOffIcon,
      flashOnIcon: flashOnIcon,
      historyIcon: historyIcon,
      cancelButtonIcon: cancelButtonIcon,
      backCameraIcon: backCameraIcon,
      frontCameraIcon: frontCameraIcon,
      helpButtonIcon: helpButtonIcon,

      // Animation
      scanningAnimationDuration: scanningAnimationDuration,

      // Advanced interaction properties
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: hoverColor,
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonScannerConfig: json['useJsonScannerConfig'] as bool? ?? false,
      jsonConfig: json,
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'transparent':
            return Colors.transparent;
          default:
            return Color(0xFF0058FF);
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Color(0xFF0058FF); // Default color
  }

  /// Parses icon data from a string
  static IconData? _parseIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'flash_off':
        return Icons.flash_off;
      case 'flash_on':
        return Icons.flash_on;
      case 'history':
        return Icons.history;
      case 'close':
        return Icons.close;
      case 'camera_rear':
        return Icons.camera_rear;
      case 'camera_front':
        return Icons.camera_front;
      case 'help':
        return Icons.help;
      case 'qr_code':
        return Icons.qr_code;
      case 'qr_code_scanner':
        return Icons.qr_code_scanner;
      case 'camera':
        return Icons.camera;
      case 'camera_alt':
        return Icons.camera_alt;
      case 'photo_camera':
        return Icons.photo_camera;
      case 'photo':
        return Icons.photo;
      case 'copy':
        return Icons.copy;
      case 'open_in_new':
        return Icons.open_in_new;
      case 'delete':
        return Icons.delete;
      case 'refresh':
        return Icons.refresh;
      case 'save':
        return Icons.save;
      case 'share':
        return Icons.share;
      case 'info':
        return Icons.info;
      case 'info_outline':
        return Icons.info_outline;
      case 'settings':
        return Icons.settings;
      case 'search':
        return Icons.search;
      default:
        return null;
    }
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Display options
      'showFlash': showFlash,
      'initialFlashState': initialFlashState,
      'showGuide': showGuide,
      'overlayShape': overlayShape.toString().split('.').last,
      'showHistory': showHistory,
      'maxHistoryItems': maxHistoryItems,
      'vibrateOnScan': vibrateOnScan,
      'playSoundOnScan': playSoundOnScan,
      'stopAfterFirstScan': stopAfterFirstScan,
      'showCancelButton': showCancelButton,
      'useFrontCamera': useFrontCamera,
      'showCameraSwitch': showCameraSwitch,
      'showScanningAnimation': showScanningAnimation,
      'showResultDialog': showResultDialog,
      'showCopyButton': showCopyButton,
      'showOpenButton': showOpenButton,
      'showCloseButton': showCloseButton,
      'showHelpButton': showHelpButton,

      // Colors
      'overlayColor': '#${overlayColor.toHexString()}',
      'scanAreaColor': '#${scanAreaColor.toHexString()}',
      'borderColor': '#${borderColor.toHexString()}',
      'scanningAnimationColor': '#${scanningAnimationColor.toHexString()}',

      // Dimensions
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'scanAreaSize': scanAreaSize,

      // Text labels
      'permissionDeniedText': permissionDeniedText,
      'flashOffText': flashOffText,
      'flashOnText': flashOnText,
      'historyText': historyText,
      'noHistoryText': noHistoryText,
      'historyDialogTitle': historyDialogTitle,
      'closeButtonText': closeButtonText,
      'clearButtonText': clearButtonText,
      'cancelButtonText': cancelButtonText,
      'cameraSwitchText': cameraSwitchText,
      'resultDialogTitle': resultDialogTitle,
      'copyButtonText': copyButtonText,
      'openButtonText': openButtonText,
      'resultCloseButtonText': resultCloseButtonText,
      'helpButtonText': helpButtonText,
      'helpText': helpText,
      'helpDialogTitle': helpDialogTitle,
      'helpCloseButtonText': helpCloseButtonText,

      // Icons
      'flashOffIcon': _iconDataToString(flashOffIcon),
      'flashOnIcon': _iconDataToString(flashOnIcon),
      'historyIcon': _iconDataToString(historyIcon),
      'cancelButtonIcon': _iconDataToString(cancelButtonIcon),
      'backCameraIcon': _iconDataToString(backCameraIcon),
      'frontCameraIcon': _iconDataToString(frontCameraIcon),
      'helpButtonIcon': _iconDataToString(helpButtonIcon),

      // Animation
      'scanningAnimationDuration': scanningAnimationDuration.inMilliseconds,

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonScannerConfig': useJsonScannerConfig,
    };
  }

  /// Converts IconData to a string representation
  static String _iconDataToString(IconData icon) {
    if (icon == Icons.flash_off) return 'flash_off';
    if (icon == Icons.flash_on) return 'flash_on';
    if (icon == Icons.history) return 'history';
    if (icon == Icons.close) return 'close';
    if (icon == Icons.camera_rear) return 'camera_rear';
    if (icon == Icons.camera_front) return 'camera_front';
    if (icon == Icons.help) return 'help';
    if (icon == Icons.qr_code) return 'qr_code';
    if (icon == Icons.qr_code_scanner) return 'qr_code_scanner';
    if (icon == Icons.camera) return 'camera';
    if (icon == Icons.camera_alt) return 'camera_alt';
    if (icon == Icons.photo_camera) return 'photo_camera';
    if (icon == Icons.photo) return 'photo';
    if (icon == Icons.copy) return 'copy';
    if (icon == Icons.open_in_new) return 'open_in_new';
    if (icon == Icons.delete) return 'delete';
    if (icon == Icons.refresh) return 'refresh';
    if (icon == Icons.save) return 'save';
    if (icon == Icons.share) return 'share';
    if (icon == Icons.info) return 'info';
    if (icon == Icons.info_outline) return 'info_outline';
    if (icon == Icons.settings) return 'settings';
    if (icon == Icons.search) return 'search';
    return 'unknown';
  }

  @override
  State<QrScannerWidget> createState() => _QrScannerWidgetState();
}

class _QrScannerWidgetState extends State<QrScannerWidget>
    with SingleTickerProviderStateMixin {
  bool _hasPermission = false;
  bool _isFlashOn = false;
  bool _isFrontCamera = false;
  final List<String> _scanHistory = [];
  late MobileScannerController _controller;
  AnimationController? _animationController;
  Animation<double>? _animation;

  // Interaction state
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _isFlashOn = widget.initialFlashState;
    _isFrontCamera = widget.useFrontCamera;
    _checkPermission();
    _initializeScanner();

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    if (widget.showScanningAnimation) {
      _animationController = AnimationController(
        duration: widget.scanningAnimationDuration,
        vsync: this,
      );

      _animation =
          Tween<double>(begin: 0, end: 1).animate(_animationController!)
            ..addListener(() {
              setState(() {});
            })
            ..addStatusListener((status) {
              if (status == AnimationStatus.completed) {
                _animationController!.repeat(reverse: true);
              }
            });

      _animationController!.forward();
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(QrScannerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController?.dispose();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  Future<void> _checkPermission() async {
    final status = await Permission.camera.status;
    setState(() {
      _hasPermission = status.isGranted;
    });

    if (!status.isGranted) {
      final result = await Permission.camera.request();
      setState(() {
        _hasPermission = result.isGranted;
      });
    }
  }

  void _initializeScanner() {
    _controller = MobileScannerController(
      facing: _isFrontCamera ? CameraFacing.front : CameraFacing.back,
      torchEnabled: _isFlashOn,
    );
  }

  void _toggleFlash() {
    setState(() {
      _isFlashOn = !_isFlashOn;
      _controller.toggleTorch();
    });
  }

  void _toggleCamera() {
    setState(() {
      _isFrontCamera = !_isFrontCamera;
      _controller.switchCamera();
    });
  }

  void _showHistory() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(widget.historyDialogTitle),
            content: SizedBox(
              width: double.maxFinite,
              child:
                  _scanHistory.isEmpty
                      ? Center(child: Text(widget.noHistoryText))
                      : ListView.builder(
                        shrinkWrap: true,
                        itemCount: _scanHistory.length,
                        itemBuilder: (context, index) {
                          return ListTile(
                            title: Text(
                              _scanHistory[index],
                              style: const TextStyle(fontSize: 14),
                            ),
                            onTap: () {
                              Navigator.pop(context);
                              if (widget.showResultDialog) {
                                _showResultDialog(_scanHistory[index]);
                              }
                            },
                          );
                        },
                      ),
            ),
            actions: [
              if (_scanHistory.isNotEmpty)
                TextButton(
                  onPressed: () {
                    setState(() {
                      _scanHistory.clear();
                    });
                    Navigator.pop(context);
                  },
                  child: Text(widget.clearButtonText),
                ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(widget.closeButtonText),
              ),
            ],
          ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(widget.helpDialogTitle),
            content: Text(widget.helpText),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(widget.helpCloseButtonText),
              ),
            ],
          ),
    );
  }

  void _showResultDialog(String result) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(widget.resultDialogTitle),
            content: SelectableText(result),
            actions: [
              if (widget.showCopyButton)
                TextButton(
                  onPressed: () {
                    // Copy to clipboard functionality would go here
                    Navigator.pop(context);
                  },
                  child: Text(widget.copyButtonText),
                ),
              if (widget.showOpenButton)
                TextButton(
                  onPressed: () {
                    // Open URL functionality would go here
                    Navigator.pop(context);
                  },
                  child: Text(widget.openButtonText),
                ),
              if (widget.showCloseButton)
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(widget.resultCloseButtonText),
                ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    if (!_hasPermission) {
      content = Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.camera_alt, size: 50, color: Colors.grey),
            const SizedBox(height: 16),
            Text(widget.permissionDeniedText, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                await _checkPermission();
                if (!_hasPermission) {
                  openAppSettings();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF0058FF),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              child: const Text('Grant Permission'),
            ),
          ],
        ),
      );
    } else {
      content = Stack(
        children: [
          // Camera preview
          MobileScanner(
            controller: _controller,
            onDetect: (capture) {
              final List<Barcode> barcodes = capture.barcodes;
              if (barcodes.isNotEmpty && barcodes[0].rawValue != null) {
                final String code = barcodes[0].rawValue!;

                // Add to history if enabled
                if (widget.showHistory && !_scanHistory.contains(code)) {
                  setState(() {
                    _scanHistory.insert(0, code);
                    if (_scanHistory.length > widget.maxHistoryItems) {
                      _scanHistory.removeLast();
                    }
                  });
                }

                // Call onScan callback
                if (widget.onScan != null) {
                  widget.onScan!(code);
                }

                // Execute JSON callback if defined
                if (widget.useJsonCallbacks &&
                    widget.jsonCallbacks != null &&
                    widget.jsonCallbacks!.containsKey('onScan')) {
                  _executeJsonCallback('onScan', code);
                }

                // Show result dialog if enabled
                if (widget.showResultDialog) {
                  _showResultDialog(code);
                }

                // Stop scanning if configured to do so
                if (widget.stopAfterFirstScan) {
                  _controller.stop();
                }
              }
            },
            errorBuilder: (context, error, child) {
              final errorStr = error.toString();

              // Call standard callback
              if (widget.onError != null) {
                widget.onError!(errorStr);
              }

              // Execute JSON callback if defined
              if (widget.useJsonCallbacks &&
                  widget.jsonCallbacks != null &&
                  widget.jsonCallbacks!.containsKey('onError')) {
                _executeJsonCallback('onError', errorStr);
              }

              return Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.error, size: 50, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(
                      'Scanner error: ${error.toString()}',
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            },
          ),

          // Overlay
          if (widget.showGuide &&
              widget.overlayShape != ScannerOverlayShape.none)
            CustomPaint(
              size: Size.infinite,
              painter: ScannerOverlayPainter(
                scanAreaSize: widget.scanAreaSize,
                overlayColor: widget.overlayColor,
                scanAreaColor: widget.scanAreaColor,
                borderColor: widget.borderColor,
                borderWidth: widget.borderWidth,
                borderRadius: widget.borderRadius,
                overlayShape: widget.overlayShape,
                animation: _animation?.value ?? 0,
                showAnimation: widget.showScanningAnimation,
                animationColor: widget.scanningAnimationColor,
              ),
            ),

          // Controls
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (widget.showFlash)
                  ElevatedButton.icon(
                    onPressed: _toggleFlash,
                    icon: Icon(
                      _isFlashOn ? widget.flashOnIcon : widget.flashOffIcon,
                    ),
                    label: Text(
                      _isFlashOn ? widget.flashOnText : widget.flashOffText,
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isFlashOn ? Colors.amber : null,
                    ),
                  ),

                if (widget.showCameraSwitch)
                  ElevatedButton.icon(
                    onPressed: _toggleCamera,
                    icon: Icon(
                      _isFrontCamera
                          ? widget.frontCameraIcon
                          : widget.backCameraIcon,
                    ),
                    label: Text(widget.cameraSwitchText),
                  ),

                if (widget.showHistory)
                  ElevatedButton.icon(
                    onPressed: _showHistory,
                    icon: Icon(widget.historyIcon),
                    label: Text(widget.historyText),
                  ),

                if (widget.showHelpButton)
                  ElevatedButton.icon(
                    onPressed: _showHelpDialog,
                    icon: Icon(widget.helpButtonIcon),
                    label: Text(widget.helpButtonText),
                  ),

                if (widget.showCancelButton)
                  ElevatedButton.icon(
                    onPressed: () {
                      // Call standard callback
                      if (widget.onCancel != null) {
                        widget.onCancel!();
                      }

                      // Execute JSON callback if defined
                      if (widget.useJsonCallbacks &&
                          widget.jsonCallbacks != null &&
                          widget.jsonCallbacks!.containsKey('onCancel')) {
                        _executeJsonCallback('onCancel');
                      }
                    },
                    icon: Icon(widget.cancelButtonIcon),
                    label: Text(widget.cancelButtonText),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade200,
                    ),
                  ),
              ],
            ),
          ),
        ],
      );
    }

    // Apply hover detection
    if (widget.onHover != null) {
      content = MouseRegion(
        onEnter: (event) {
          widget.onHover!(true);
        },
        onExit: (event) {
          widget.onHover!(false);
        },
        child: content,
      );
    }

    // Apply focus handling
    if (widget.autofocus || widget.onFocus != null) {
      content = Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        onFocusChange: widget.onFocus,
        child: content,
      );
    }

    // Apply advanced gesture detection
    if (widget.onDoubleTap != null || widget.onLongPress != null) {
      content = GestureDetector(
        onDoubleTap:
            widget.onDoubleTap != null
                ? () {
                  // Execute onDoubleTap callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                    _executeJsonCallback('onDoubleTap');
                  }

                  // Call standard callback
                  widget.onDoubleTap!();
                }
                : null,
        onLongPress:
            widget.onLongPress != null
                ? () {
                  // Execute onLongPress callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onLongPress')) {
                    _executeJsonCallback('onLongPress');
                  }

                  // Call standard callback
                  widget.onLongPress!();
                }
                : null,
        child: content,
      );
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      content = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: content,
      );
    }

    return content;
  }
}

class ScannerOverlayPainter extends CustomPainter {
  final double scanAreaSize;
  final Color overlayColor;
  final Color scanAreaColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final ScannerOverlayShape overlayShape;
  final double animation;
  final bool showAnimation;
  final Color animationColor;

  ScannerOverlayPainter({
    required this.scanAreaSize,
    required this.overlayColor,
    required this.scanAreaColor,
    required this.borderColor,
    required this.borderWidth,
    required this.borderRadius,
    required this.overlayShape,
    required this.animation,
    required this.showAnimation,
    required this.animationColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint overlayPaint =
        Paint()
          ..color = overlayColor
          ..style = PaintingStyle.fill;

    // We're not using scanAreaPaint directly, but keeping it for future use
    // final Paint scanAreaPaint = Paint()
    //   ..color = scanAreaColor
    //   ..style = PaintingStyle.fill;

    final Paint borderPaint =
        Paint()
          ..color = borderColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = borderWidth;

    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double halfSize = scanAreaSize / 2;

    // Draw full screen overlay
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), overlayPaint);

    // Draw scan area based on shape
    if (overlayShape == ScannerOverlayShape.circle) {
      // Draw circular hole
      final Path path =
          Path()
            ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
            ..addOval(
              Rect.fromCircle(
                center: Offset(centerX, centerY),
                radius: halfSize,
              ),
            )
            ..fillType = PathFillType.evenOdd;

      canvas.drawPath(path, overlayPaint);

      // Draw circle border
      canvas.drawCircle(Offset(centerX, centerY), halfSize, borderPaint);
    } else {
      // Calculate rectangle coordinates
      final Rect scanRect = Rect.fromCenter(
        center: Offset(centerX, centerY),
        width: scanAreaSize,
        height:
            overlayShape == ScannerOverlayShape.square
                ? scanAreaSize
                : scanAreaSize * 0.75,
      );

      // Draw rectangular hole
      final Path path =
          Path()
            ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
            ..addRRect(
              RRect.fromRectAndRadius(scanRect, Radius.circular(borderRadius)),
            )
            ..fillType = PathFillType.evenOdd;

      canvas.drawPath(path, overlayPaint);

      // Draw rectangle border
      canvas.drawRRect(
        RRect.fromRectAndRadius(scanRect, Radius.circular(borderRadius)),
        borderPaint,
      );

      // Draw scanning animation if enabled
      if (showAnimation) {
        final Paint animationPaint =
            Paint()
              ..color = animationColor.withAlpha(
                128,
              ) // Using withAlpha instead of withOpacity
              ..style = PaintingStyle.stroke
              ..strokeWidth = 2.0;

        final double animationHeight = scanRect.height * animation;
        final Rect animationRect = Rect.fromLTRB(
          scanRect.left,
          scanRect.top + (scanRect.height - animationHeight) / 2,
          scanRect.right,
          scanRect.top + (scanRect.height + animationHeight) / 2,
        );

        canvas.drawRect(animationRect, animationPaint);
      }
    }
  }

  @override
  bool shouldRepaint(ScannerOverlayPainter oldDelegate) {
    return oldDelegate.animation != animation ||
        oldDelegate.scanAreaSize != scanAreaSize ||
        oldDelegate.overlayColor != overlayColor ||
        oldDelegate.scanAreaColor != scanAreaColor ||
        oldDelegate.borderColor != borderColor ||
        oldDelegate.borderWidth != borderWidth ||
        oldDelegate.borderRadius != borderRadius ||
        oldDelegate.overlayShape != overlayShape;
  }
}
