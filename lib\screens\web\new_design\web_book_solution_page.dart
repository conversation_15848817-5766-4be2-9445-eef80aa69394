import 'dart:math' show min, max;
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_book_solution_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/agent_table.dart';
import 'package:nsl/screens/web/new_design/widgets/entity_details_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/entity_table.dart';
import 'package:nsl/screens/web/new_design/widgets/role_details_panel.dart';
import 'package:nsl/screens/web/new_design/widgets/workflow_table.dart';
import 'package:nsl/theme/app_theme_constants.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/widgets/resizable_panel.dart';
import 'package:provider/provider.dart';
import '../../../../../theme/app_colors.dart';

class WebBookSolutionPage extends StatefulWidget {
  final Map<String, dynamic>? initialData;

  const WebBookSolutionPage({super.key, this.initialData});

  @override
  State<WebBookSolutionPage> createState() => _WebBookSolutionPageState();
}

class _WebBookSolutionPageState extends State<WebBookSolutionPage> {
  bool _showRightPanel = false;
  bool _isExpanded = false;
  bool _showUploadModal = false;
  int selectedSectionIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Consumer<WebBookSolutionProvider>(
      builder: (context, provider, child) {
        return Consumer<ManualCreationProvider>(
          builder: (context, manualCreationprovider, child) {
            return Scaffold(
              backgroundColor: const Color(0xFFF5F7FA),
              body: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: SizedBox()),
                  Expanded(
                    flex: 8,
                    child: Row(
                      children: [
                        // Main content
                        Expanded(
                          child: Column(
                            children: [
                              _buildHeader(
                                  context, provider, manualCreationprovider),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: AppSpacing.lg),
                                  child: manualCreationprovider
                                              .showAgentTableForBook ||
                                          manualCreationprovider
                                              .showEntityTableForBook ||
                                          manualCreationprovider
                                              .showWorkflowTableForBook
                                      ? mainContent(
                                          context, manualCreationprovider)
                                      : provider.showAddModules
                                          ? _buildAddModulesContent(
                                              context, provider)
                                          : _buildSolutionsList(provider),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: _isExpanded ? 4 : 1,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _showRightPanel = !_showRightPanel;
                                _isExpanded = !_isExpanded;
                              });
                            },
                            child: Padding(
                              padding: EdgeInsets.only(
                                  top: 16, right: 12), // adjust value as needed
                              child: AnimatedRotation(
                                turns: _isExpanded
                                    ? 0.5
                                    : 0.0, // 0.5 turns = 180 degrees
                                duration: Duration(milliseconds: 300),
                                child: SvgPicture.asset(
                                  'assets/images/expand-arrow-left-new.svg',
                                  width: AppSpacing.md,
                                  height: AppSpacing.md,
                                  colorFilter: ColorFilter.mode(
                                    AppColors
                                        .textPrimaryLight, // Replaced Colors.black
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        // Right panel
                        if (_showRightPanel)
                          Container(
                            width: MediaQuery.of(context).size.width * 0.22,
                            decoration: BoxDecoration(
                              color: AppColors
                                  .surfaceLight, // Replaced Colors.white
                              border: Border(
                                left: BorderSide(
                                  color: AppColors
                                      .greyBg, // Replaced Colors.grey.shade200
                                  width: 1,
                                ),
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.textPrimaryLight.withOpacity(
                                      0.03), // Replaced Colors.black.withValues(alpha: 0.03)
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // Header
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20, vertical: 10),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border(
                                      bottom: BorderSide(
                                          color: Colors.grey.shade200,
                                          width: 1),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'All Documents/Artefacts',
                                        style: FontManager.getCustomStyle(
                                          fontWeight: FontManager.semiBold,
                                          fontSize: FontManager.s14,
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? AppColors.textPrimaryDark
                                              : Colors.black,
                                          fontFamily: FontManager.fontFamily,
                                        ),
                                      ),
                                      Container(
                                        width: AppSpacing.lg,
                                        height: AppSpacing.lg,
                                        decoration: BoxDecoration(
                                          color: _showUploadModal
                                              ? AppColors.textBlue2
                                              : Colors.transparent,
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                              color: _showUploadModal
                                                  ? AppColors.textBlue
                                                  : AppColors
                                                      .greyBg, // Replaced Color(0xFF0058FF)
                                              width: 1),
                                        ),
                                        child: IconButton(
                                          icon: Icon(
                                              _showUploadModal
                                                  ? Icons.close
                                                  : Icons.add,
                                              color: _showUploadModal
                                                  ? Colors.black
                                                  : Colors.black,
                                              size: AppSpacing.md),
                                          padding: EdgeInsets.zero,
                                          onPressed: () {
                                            setState(() {
                                              _showUploadModal =
                                                  !_showUploadModal;
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Scrollable content area
                                Expanded(
                                  child: Container(
                                    color: Colors.white,
                                    child: SingleChildScrollView(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 20, vertical: 8),
                                        child: _showUploadModal
                                            ? _buildUploadModal()
                                            : Container(), // Show upload modal when active
                                      ),
                                    ),
                                  ),
                                ),
                                // Bottom input
                                Container(
                                  height: MediaQuery.of(context).size.height *
                                      0.2, // Dynamic height based on screen size
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: Border.all(
                                      color: Colors.grey.shade200,
                                      width: 1,
                                    ),
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(16),
                                      topRight: Radius.circular(16),
                                      bottomLeft: Radius.circular(0),
                                      bottomRight: Radius.circular(0),
                                    ),
                                  ),
                                  child: TextField(
                                    maxLines: 6, // or null for infinite lines
                                    minLines: 4,
                                    keyboardType: TextInputType.multiline,
                                    decoration: InputDecoration(
                                      hintText: 'Add Instructions',
                                      hintStyle: TextStyle(
                                          fontSize: AppSpacing.sm,
                                          color: Colors.grey.shade500),
                                      filled: true,
                                      fillColor: AppColors
                                          .surfaceLight, // Replaced Color(0xFFFFFFFF)
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(4),
                                        borderSide: BorderSide(
                                            color: AppColors
                                                .greyBg, // Replaced Colors.grey.shade200
                                            width: 1),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(4),
                                        borderSide: BorderSide(
                                            color: AppColors
                                                .greyBg, // Replaced Colors.grey.shade200
                                            width: 1),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(4),
                                        borderSide: BorderSide(
                                            color: Colors.blue, width: 1),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (manualCreationprovider.showSidePanel)
                    ResizablePanel(
                      width: manualCreationprovider.sidePanelWidth,
                      minWidth: manualCreationprovider.minSidePanelWidth,
                      maxWidth: manualCreationprovider.maxSidePanelWidth,
                      handlePosition: ResizeHandlePosition.left,
                      onResize: (newWidth) {
                        manualCreationprovider.updateSidePanelWidth(newWidth);
                      },
                      child: RoleDetailsPanel(
                        role: manualCreationprovider.selectedRole!,
                        onClose: () {
                          manualCreationprovider.hideSidePanel();
                        },
                        showLegacySections: false,
                        users: manualCreationprovider.extractedUsers,
                      ),
                    )
                  else if (manualCreationprovider.showSideEntityPanel)
                    ResizablePanel(
                      width: manualCreationprovider.sidePanelWidth,
                      minWidth: manualCreationprovider.minSidePanelWidth,
                      maxWidth: manualCreationprovider.maxSidePanelWidth,
                      handlePosition: ResizeHandlePosition.left,
                      onResize: (newWidth) {
                        manualCreationprovider.updateSidePanelWidth(newWidth);
                      },
                      child: EntityDetailsPanel(
                        entity: manualCreationprovider.selectedEntity!,
                        onClose: () =>
                            manualCreationprovider.hideEntitySidePanel(),
                        chatController: null,
                        onSendMessage: null,
                        globalEntityElements: {},
                      ),
                    )
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebBookSolutionProvider provider,
      ManualCreationProvider manualCreationprovider) {
    return Container(
      height: AppSpacing.xxl,
      padding: const EdgeInsets.only(left: 0),
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left section - Back button and Book Name
          Row(
            children: [
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: InkWell(
                  borderRadius: BorderRadius.circular(4),
                  onTap: () {
                    if (manualCreationprovider.showAgentTableForBook ||
                        manualCreationprovider.showEntityTableForBook ||
                        manualCreationprovider.showWorkflowTableForBook) {
                      manualCreationprovider.clearBookResults();
                    } else {
                      Provider.of<WebHomeProvider>(context, listen: false)
                          .currentScreenIndex = ScreenConstants.webMyLibrary;
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.grey,
                      size: AppSpacing.md,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                'Book Name',
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontWeight: FontManager.medium,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamily,
                ),
              ),
            ],
          ),
          // Center section with count items grouped together
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildCountItem('assets/images/agent.svg', '3 Agent (V001)',
                    () {
                  manualCreationprovider.handleAgentValidationForBook();
                }),
                const SizedBox(width: AppSpacing.lg),
                _buildCountItem(
                    'assets/images/cube-box.svg', '12 Objects (V001)', () {
                  manualCreationprovider.handleEntityValidationForBook();
                }),
                const SizedBox(width: AppSpacing.lg),
                _buildCountItem(
                    'assets/images/square-box-uncheck.svg', '15 Solutions', () {
                  manualCreationprovider.handleWorkflowValidationForBook();
                }),
              ],
            ),
          ),

          // Right section - Add Modules button
          MouseRegion(
            cursor: SystemMouseCursors.click,
            onEnter: (_) {
              provider.isAddModulesHeaderButtonHovering = true;
            },
            onExit: (_) {
              provider.isAddModulesHeaderButtonHovering = false;
            },
            child: GestureDetector(
              onTap: provider.toggleAddModules,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.transparent,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      'assets/images/add-module.svg',
                      width: AppSpacing.size14,
                      height: AppSpacing.size14,
                      colorFilter: ColorFilter.mode(
                        Colors.grey.shade600,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Add Modules',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        color: Colors.black,
                        fontFamily: 'TiemposText',
                        fontWeight: FontManager.medium,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.xxs),
                    Icon(
                      Icons.arrow_drop_down,
                      size: AppSpacing.lg,
                      color: provider.isAddModulesHeaderButtonHovering ||
                              provider.showAddModules
                          ? const Color(0xff0058FF)
                          : Colors.grey.shade600,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCountItem(String iconPath, String text, Function()? onTap) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.transparent,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                iconPath,
                width: AppSpacing.size14,
                height: AppSpacing.size14,
                colorFilter: ColorFilter.mode(
                  Colors.black,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                text,
                style: FontManager.getCustomStyle(
                  fontSize: FontManager.s14,
                  fontFamily: 'TiemposText',
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.textPrimaryDark
                      : Colors.black,
                  fontWeight: FontManager.medium,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSolutionsList(WebBookSolutionProvider provider) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final dynamicHeight = constraints.maxHeight *
            0.8; // Calculate dynamic height based on parent constraints
        return SizedBox(
          height: dynamicHeight,
          child: Column(
            children: provider.solutionItems
                .take(12)
                .map((item) => _buildSolutionItem(item))
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildSolutionItem(SolutionItem item) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Container(
        margin: const EdgeInsets.only(
          bottom: 16,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Color(0xffD0D0D0)),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.title,
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.textPrimaryDark
                            : const Color(0xff000000),
                        fontWeight: FontManager.semiBold,
                        fontFamily: FontManager.fontFamily,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xxs),
                    Text(
                      'Last Message: ${item.lastMessageTime}',
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s14,
                        fontWeight: FontManager.regular,
                        color: Colors.grey.shade600,
                        fontFamily: FontManager.fontFamily,
                      ),
                    ),
                  ],
                ),
              ),

              // Right content
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      SvgPicture.asset(
                        'assets/images/folder.svg',
                        width: AppSpacing.md,
                        height: AppSpacing.md,
                        colorFilter: ColorFilter.mode(
                          Colors.grey,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.xxs),
                      Text(
                        item.versionId,
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s14,
                          fontWeight: FontManager.regular,
                          color: Colors.grey.shade600,
                          fontFamily: FontManager.fontFamily,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppSpacing.xxs),
                  Text(
                    item.date,
                    style: TextStyle(
                      fontSize: AppSpacing.sm,
                      fontFamily: 'TiemposText',
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddModulesContent(
      BuildContext context, WebBookSolutionProvider provider) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              // Header row
              Container(
                height: AppSpacing.xxl,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
                child: Row(
                  children: [
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(left: 0),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'Modules',
                              style: FontManager.getCustomStyle(
                                fontSize: FontManager.s14,
                                fontWeight: FontManager.semiBold,
                                color: Theme.of(context).brightness ==
                                        Brightness.dark
                                    ? AppColors.textPrimaryDark
                                    : Colors.black,
                                fontFamily: FontManager.fontFamily,
                              ),
                            ),
                            SizedBox(width: AppSpacing.sm),
                            CompositedTransformTarget(
                              link: provider.modulesButtonLink,
                              child: MouseRegion(
                                cursor: SystemMouseCursors.click,
                                onEnter: (_) {
                                  provider.isModulesButtonHovering = true;
                                },
                                onExit: (_) {
                                  provider.isModulesButtonHovering = false;
                                },
                                child: AnimatedContainer(
                                  duration: Duration(milliseconds: 120),
                                  key: provider.modulesButtonKey,
                                  margin: EdgeInsets.symmetric(horizontal: 0),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: provider.isModulesButtonHovering
                                            ? Color(0xFFBDBDBD)
                                            : Colors.grey.shade900),
                                    borderRadius: BorderRadius.circular(4),
                                    color: provider.isModulesButtonHovering
                                        ? Color(0xFFF5F7FA)
                                        : Colors.white,
                                  ),
                                  child: GestureDetector(
                                    onTap: () {
                                      if (provider.showModulesPopup) {
                                        provider.hideModulesPopupMenu();
                                      } else {
                                        provider.showModulesPopupMenu(context);
                                      }
                                    },
                                    child: Row(
                                      children: [
                                        Icon(Icons.add,
                                            size: AppSpacing.md,
                                            color: Colors.grey.shade700),
                                        SizedBox(width: AppSpacing.xxs),
                                        Text('Modules',
                                            style: TextStyle(
                                                fontSize: AppSpacing.sm)),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        alignment: Alignment.centerLeft,
                        padding: EdgeInsets.only(left: 10),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Text(
                          'Drag Your Solutions',
                          style: FontManager.getCustomStyle(
                            fontSize: FontManager.s14,
                            fontWeight: FontManager.semiBold,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? AppColors.textPrimaryDark
                                    : Colors.black,
                            fontFamily: FontManager.fontFamily,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Content row
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Middle panel - Modules
                    Flexible(
                      flex: 1,
                      child: Container(
                        key: provider.modulesColumnKey,
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: _buildModulesSection(provider),
                      ),
                    ), // Right panel - Submodules
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: _buildSubmodulesSection(provider),
                      ),
                    ), // Empty panel on the right
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                          color: Colors.white,
                        ),
                      ),
                    ),
                    // Left panel - Solutions list
                    Flexible(
                      flex: 1,
                      child: Container(
                        constraints:
                            BoxConstraints(minWidth: AppSpacing.width180),
                        decoration: BoxDecoration(
                          border: Border(
                            right: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Stack(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 8),
                              child: LayoutBuilder(
                                  builder: (context, constraints) {
                                // Calculate how many items we can fit based on available height
                                // Subtract pagination bar height and some padding
                                final paginationHeight = min(
                                    MediaQuery.of(context).size.height * 0.05,
                                    40.0);
                                final availableHeight = constraints.maxHeight -
                                    paginationHeight -
                                    20; // Each item is 34px height + vertical padding (2px top + 2px bottom)
                                final itemHeight = 38.0;

                                // Get screen width to determine display count
                                final screenWidth =
                                    MediaQuery.of(context).size.width;

                                // Calculate max items that would fit in the available space
                                final maxPossibleItems =
                                    (availableHeight / itemHeight).floor();

                                int displayCount;

                                if (screenWidth <= 1560) {
                                  // For smaller screens (1366-1560px), stick with exactly 12 items
                                  displayCount = 12;
                                } else if (screenWidth >= 1920) {
                                  // For 1920px screens, show more items to fill the space better
                                  // Get maximum possible but ensure we don't show too many empty items
                                  // Add 2-3 extra items to ensure we fill space better
                                  displayCount = min(maxPossibleItems,
                                      provider.currentPageItems.length + 3);
                                  displayCount = max(
                                      12, displayCount); // At least 12 items
                                } else {
                                  // For screens between 1560 and 1920, scale accordingly
                                  displayCount = maxPossibleItems;
                                  displayCount = max(12, displayCount);
                                }
                                return ListView.builder(
                                  padding: EdgeInsets.zero,
                                  // Ensure we display enough items to fill the space
                                  itemCount: displayCount,
                                  itemBuilder: (context, index) {
                                    final itemIndex =
                                        (provider.currentPage - 1) *
                                                provider.itemsPerPage +
                                            index +
                                            1;

                                    // Calculate dynamic vertical padding for larger screens
                                    // This helps distribute items evenly when fewer items are shown
                                    double verticalPadding = 2.0;
                                    if (screenWidth >= 1920 &&
                                        displayCount < maxPossibleItems) {
                                      // Calculate extra space and distribute as padding
                                      final extraSpace = availableHeight -
                                          (displayCount * 38.0);
                                      verticalPadding =
                                          (extraSpace / displayCount / 2)
                                              .clamp(2.0, 8.0);
                                    }

                                    return Container(
                                      height: 34 +
                                          (verticalPadding *
                                              2), // Height + padding
                                      padding: EdgeInsets.symmetric(
                                          vertical: verticalPadding),
                                      child: index <
                                              provider.currentPageItems.length
                                          ? _HoverSolutionItem(
                                              itemIndex: itemIndex,
                                            )
                                          : SizedBox(
                                              height:
                                                  34, // Match the height of solution items
                                            ),
                                    );
                                  },
                                );
                              }),
                            ),
                            Positioned(
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: Container(
                                height: min(
                                    MediaQuery.of(context).size.height * 0.05,
                                    40), // Cap height at 40px
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  border: Border(
                                    top:
                                        BorderSide(color: Colors.grey.shade300),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    // Left chevron
                                    _PaginationChevronButton(isLeft: true),
                                    SizedBox(width: AppSpacing.xs),
                                    // Right chevron
                                    _PaginationChevronButton(isLeft: false),
                                    SizedBox(width: AppSpacing.xl),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Cross button in top right
          Positioned(
            top: 8,
            right: 8,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: provider.toggleAddModules,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.shade200,
                  ),
                  padding: EdgeInsets.all(4),
                  child: Icon(Icons.close,
                      size: AppSpacing.size20, color: Colors.grey.shade700),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModulesSection(WebBookSolutionProvider provider) {
    if (provider.modules.isEmpty) {
      return Container(); // Return empty container when no modules exist
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < provider.modules.length; i++)
          _buildModuleItem(provider.modules[i], i, provider),
      ],
    );
  }

  Widget _buildModuleItem(
      ModuleItem module, int index, WebBookSolutionProvider provider) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          provider.selectModule(index);
        },
        child: Container(
          color: Color(0xFFF8F9FA),
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              // Static dropdown arrow for visual consistency
              Icon(Icons.keyboard_arrow_down, size: AppSpacing.size18),
              SizedBox(width: AppSpacing.xs),
              Expanded(
                child: Text(
                  module.name,
                  style: FontManager.getCustomStyle(
                    fontWeight: FontManager.medium,
                    fontSize: FontManager.s16,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? AppColors.textPrimaryDark
                        : AppColors.textPrimaryLight,
                    fontFamily: FontManager.fontFamily,
                  ),
                ),
              ),
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  key: provider.submoduleButtonKeys[index],
                  onTap: () {
                    // Select the module first to show its submodules in the third column
                    provider.selectModule(index);
                    provider.setActiveModulePopupIndex(index);
                    provider.showSubmodulePopup(index);
                  },
                  child: Icon(Icons.add, size: AppSpacing.size18),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubmodulesSection(WebBookSolutionProvider provider) {
    if (provider.selectedModuleIndex == null ||
        provider.selectedModuleIndex! >= provider.modules.length) {
      return Container(); // Return empty container when no module is selected
    }

    final selectedModule = provider.modules[provider.selectedModuleIndex!];
    if (selectedModule.submodules.isEmpty) {
      return Container(); // Return empty container when no submodules exist
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (String submodule in selectedModule.submodules)
          _buildSubmoduleItem(submodule),
      ],
    );
  }

  Widget _buildSubmoduleItem(String submoduleName) {
    return Container(
      color: Color(0xFFF8F9FA),
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Static dropdown arrow for visual consistency
          Icon(Icons.keyboard_arrow_down, size: AppSpacing.size18),
          SizedBox(width: AppSpacing.xs),
          Expanded(
            child: Text(
              submoduleName,
              style: FontManager.getCustomStyle(
                fontWeight: FontManager.medium,
                fontSize: FontManager.s16,
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppColors.textPrimaryDark
                    : AppColors.textPrimaryLight,
                fontFamily: FontManager.fontFamily,
              ),
            ),
          ),
          // Add button for submodule (could be used for sub-submodules in future)
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                // Future: Add sub-submodule functionality
              },
              child: Icon(Icons.add, size: FontManager.s18),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadModal() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 1),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Color(0xFFFFFFFF),
            borderRadius:
                BorderRadius.circular(AppThemeConstants.borderRadiusM),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: IntrinsicWidth(
            // Makes width fit the content
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Upload a file option
                Container(
                  padding: EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200, width: 1),
                    ),
                  ),
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () {
                        // Handle file upload
                      },
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            'assets/images/attached-2.svg',
                            width: AppSpacing.size14,
                            height: AppSpacing.size14,
                          ),
                          const SizedBox(width: AppSpacing.sm),
                          Text(
                            'Upload a File',
                            style: FontManager.getCustomStyle(
                              fontWeight: FontManager.medium,
                              fontSize: FontManager.s12,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? AppColors.textPrimaryDark
                                  : AppColors.textPrimaryLight,
                              fontFamily: 'Inter',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                // Add from Google Drive option

                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      // Handle Google Drive integration
                    },
                    child: Row(
                      children: [
                        SvgPicture.asset(
                          'assets/images/G-Drive.svg',
                          width: AppSpacing.size14,
                          height: AppSpacing.size14,
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        Text('Add From Google Drive',
                            style: FontManager.getCustomStyle(
                              fontWeight: FontManager.medium,
                              fontSize: FontManager.s12,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? AppColors.textPrimaryDark
                                  : AppColors.textPrimaryLight,
                              fontFamily: 'Inter',
                            )),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget mainContent(context, ManualCreationProvider manualCreationprovider) {
    return manualCreationprovider.showAgentTableForBook
        ? AgentTable(
            provider: manualCreationprovider,
            onRoleSelected: (role) {
              manualCreationprovider.showRoleDetailsPanel(role);
            },
          )
        : manualCreationprovider.showEntityTableForBook
            ? EntityTable(
                provider: manualCreationprovider,
                onEntitySelected: (entity) {
                  manualCreationprovider.showEntityDetailsPanel(entity);
                },
                selectedSectionIndex: selectedSectionIndex,
                onSectionIndexChanged: (index) {
                  setState(() {
                    selectedSectionIndex = index;
                  });
                },
              )
            : manualCreationprovider.showWorkflowTableForBook
                ? WorkflowTable(
                    provider: manualCreationprovider,
                    onNodeSelected: () {
                      if (manualCreationprovider.workflowLoValidationResult !=
                          null) {
                        manualCreationprovider.showWorkflowLoDetailsPanel(
                            manualCreationprovider.workflowLoValidationResult!);
                      }
                    },
                  )
                : Container();
  }
}

class _PaginationChevronButton extends StatelessWidget {
  final bool isLeft;
  const _PaginationChevronButton({this.isLeft = false});

  @override
  Widget build(BuildContext context) {
    return Consumer<WebBookSolutionProvider>(
      builder: (context, provider, child) {
        final isHovering = isLeft
            ? provider.isPaginationLeftChevronHovering
            : provider.isPaginationRightChevronHovering;

        final bool isDisabled = isLeft
            ? provider.currentPage <= 1
            : provider.currentPage >= provider.totalPages;

        return MouseRegion(
          cursor:
              isDisabled ? SystemMouseCursors.basic : SystemMouseCursors.click,
          onEnter: (_) {
            if (!isDisabled) {
              if (isLeft) {
                provider.isPaginationLeftChevronHovering = true;
              } else {
                provider.isPaginationRightChevronHovering = true;
              }
            }
          },
          onExit: (_) {
            if (!isDisabled) {
              if (isLeft) {
                provider.isPaginationLeftChevronHovering = false;
              } else {
                provider.isPaginationRightChevronHovering = false;
              }
            }
          },
          child: GestureDetector(
            onTap: isDisabled
                ? null
                : () {
                    if (isLeft) {
                      provider.previousPage();
                    } else {
                      provider.nextPage();
                    }
                  },
            child: AnimatedContainer(
              duration: Duration(milliseconds: 120),
              decoration: BoxDecoration(
                color: isDisabled
                    ? Colors.grey.shade100
                    : (isHovering
                        ? (Theme.of(context).brightness == Brightness.dark
                            ? AppColors.textPrimaryDark
                            : Color(0xFFF5F7FA))
                        : Colors.transparent),
                border: isHovering && !isDisabled
                    ? Border.all(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? AppColors.textPrimaryDark
                            : Color(0xFFBDBDBD))
                    : Border.all(color: Colors.transparent),
                borderRadius:
                    BorderRadius.circular(AppThemeConstants.borderRadiusS),
              ),
              child: Icon(
                isLeft ? Icons.chevron_left : Icons.chevron_right,
                size: FontManager.s22,
                color: isDisabled ? Colors.grey.shade300 : Colors.grey.shade600,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Hover solution item widget that follows the consistent hover pattern
class _HoverSolutionItem extends StatefulWidget {
  final int itemIndex;

  const _HoverSolutionItem({
    required this.itemIndex,
  });

  @override
  State<_HoverSolutionItem> createState() => _HoverSolutionItemState();
}

class _HoverSolutionItemState extends State<_HoverSolutionItem> {
  bool isHovered = false;
  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: AnimatedContainer(
        duration: Duration(milliseconds: 120),
        height: 34, // Fixed height for consistent spacing
        padding: EdgeInsets.symmetric(vertical: 7, horizontal: 7),
        decoration: BoxDecoration(
          color: isHovered
              ? (Theme.of(context).brightness == Brightness.dark
                  ? AppColors.textPrimaryDark.withOpacity(0.08)
                  : AppColors.textPrimaryLight.withOpacity(0.08))
              : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          'Solutions-${widget.itemIndex.toString().padLeft(2, '0')}',
          style: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: isHovered ? FontManager.semiBold : FontManager.regular,
            color: isHovered
                ? (Theme.of(context).brightness == Brightness.dark
                    ? AppColors.textPrimaryLight
                    : AppColors.textBlue)
                : (Theme.of(context).brightness == Brightness.dark
                    ? AppColors.textPrimaryDark
                    : AppColors.textPrimaryLight),
            fontFamily: FontManager.fontFamily,
          ),
        ),
      ),
    );
  }
}
