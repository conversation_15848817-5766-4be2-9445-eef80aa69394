import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/models/role_info.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/build_role_card.dart';

/// A reusable agent table widget that displays agent data in a table format.
///
/// This widget shows agent information extracted from validation results
/// and provides interaction capabilities similar to the role cards.
class AgentTable extends StatelessWidget {
  /// The manual creation provider containing agent data
  final ManualCreationProvider provider;

  /// Callback when an agent/role is selected
  final Function(RoleInfo)? onRoleSelected;

  const AgentTable({
    super.key,
    required this.provider,
    this.onRoleSelected,
  });

  @override
  Widget build(BuildContext context) {
    if (provider.extractedAgentData == null ||
        provider.extractedAgentData!.agents == null ||
        provider.extractedAgentData!.agents!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No agent data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      padding: EdgeInsets.all(AppSpacing.xs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
                vertical: AppSpacing.xs, horizontal: AppSpacing.sm),
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xffD0D0D0)),
            ),
            child: Row(
              children: [
                Text(
                  'Roles: There are ${provider.extractedAgentData!.agents!.length} roles',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.xs, vertical: AppSpacing.xxs),
                  decoration: BoxDecoration(
                    color: Color(0xffFFE5B4),
                    borderRadius: BorderRadius.circular(AppSpacing.xxs),
                  ),
                  child: Text(
                    '${provider.extractedAgentData!.agents!.length} Agent${provider.extractedAgentData!.agents!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Agent rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: provider.extractedAgentData!.agents!.length,
              itemBuilder: (context, index) {
                final agent = provider.extractedAgentData!.agents![index];

                // Convert AgentInfo to RoleInfo for BuildRoleCard
                final role = RoleInfo(
                  id: agent.id,
                  title: agent.title,
                  description: agent.description,
                  version: agent.version,
                  createdBy: agent.createdBy,
                  createdDate: _formatDate(agent.createdDate ?? DateTime.now()),
                  modifiedBy: agent.modifiedBy,
                  modifiedDate:
                      _formatDate(agent.modifiedDate ?? DateTime.now()),
                  // Extract use cases from agent sections
                  coreResponsibilities: agent.sections
                      .where((section) => section.title
                          .toLowerCase()
                          .contains('responsibilities'))
                      .expand((section) {
                    List<String> temp = [];
                    for (var item in section.items) {
                      temp.add(item.toString());
                    }
                    return temp;
                  }).toList(),
                  // Extract permissions from agent sections
                  kpis: agent.sections
                      .where((section) =>
                          section.title.toLowerCase().contains('performance'))
                      .expand((section) => section.items)
                      .toList(),
                  decisionAuthority: agent.sections
                      .where((section) =>
                          section.title.toLowerCase().contains('authority'))
                      .expand((section) {
                    List<String> temp = [];
                    for (var item in section.items) {
                      temp.add(item.toString());
                    }
                    return temp;
                  }).toList(),
                );

                return Container(
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      right: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      bottom: BorderSide(color: Color(0xffD0D0D0), width: 1),
                    ),
                  ),
                  child: BuildRoleCard(
                    role: role,
                    isSelected: provider.selectedRole?.id == role.id,
                    isBorderRequired: false,
                    isHoverCardRequired: true,
                    selectedColor: Color(0xffF2F2F2),
                    onRoleTap: (selectedRole) {
                      onRoleSelected?.call(selectedRole);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
