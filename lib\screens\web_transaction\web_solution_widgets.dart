import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/theme/spacing.dart';

import 'package:flutter/services.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class WebSolutionWidgets extends StatefulWidget {
  const WebSolutionWidgets({super.key});

  @override
  State<WebSolutionWidgets> createState() => _WebSolutionWidgetsState();
}

class _WebSolutionWidgetsState extends State<WebSolutionWidgets> {
  final TextEditingController _textController = TextEditingController();
  final List<String> _messages = [];

  // JSON data variables
  Map<String, dynamic>? jsonData;
  List<Map<String, dynamic>> optionButtonsData = [];
  List<Map<String, dynamic>> recommendationData = [];
  List<Map<String, dynamic>> actionButtonsData = [];
  Map<String, dynamic>? userInterfaceData;

  @override
  void initState() {
    super.initState();
    _loadJsonData();
  }

  Future<void> _loadJsonData() async {
    try {
      final String jsonString =
          await rootBundle.loadString('assets/data/solution_components.json');
      final Map<String, dynamic> data = jsonDecode(jsonString);

      setState(() {
        jsonData = data;

        // Extract data from JSON
        final leaveAnalytics = data['leave_analytics_dashboard'];
        final uiComponents = leaveAnalytics?['user_interface_components'];
        final embeddedForm = uiComponents?['chat_interface']?['embedded_form'];

        userInterfaceData = uiComponents;

        // Map quick_actions to optionButtonsData
        final quickActions = embeddedForm?['quick_actions'] as List<dynamic>?;
        if (quickActions != null) {
          optionButtonsData = quickActions
              .map((action) => {
                    'type': 'card',
                    'icon': 'assets/images/my_business/box_add.svg',
                    'label': action.toString(),
                    'onTap': () {
                      print("$action clicked");
                    },
                  })
              .toList();
        }

        // Map info_notifications to recommendationData
        final infoNotifications =
            embeddedForm?['info_notifications'] as List<dynamic>?;
        if (infoNotifications != null) {
          recommendationData = infoNotifications
              .map((notification) => {
                    'type': 'text',
                    'icon': notification['icon'] ?? '',
                    'value': notification['message'] ?? '',
                    'notificationType': notification['type'] ?? 'info',
                  })
              .toList();
        }

        // Map action_buttons to actionButtonsData
        final actionButtons = embeddedForm?['action_buttons'] as List<dynamic>?;
        if (actionButtons != null) {
          actionButtonsData = actionButtons
              .map((button) => {
                    'text': button['text'] ?? '',
                    'type': button['type'] ?? 'secondary',
                    'action': button['action'] ?? '',
                    'image': button['image'] ?? '',
                  })
              .toList();
        }
      });

      print("JSON data loaded successfully");
    } catch (e) {
      print("Error loading JSON data: $e");
      // Fallback to default data
      _setDefaultData();
    }
  }

  void _setDefaultData() {
    optionButtonsData = [
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Check Policy',
        'onTap': () {
          print("Check Policy clicked");
        },
      },
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Add to Calendar',
        'onTap': () {
          print("Add to Calendar clicked");
        },
      },
      {
        'type': 'card',
        'icon': 'assets/images/my_business/box_add.svg',
        'label': 'Use AI Suggestion',
        'onTap': () {
          print("AI Suggestion clicked");
        },
      },
    ];

    recommendationData = [
      {'type': 'text', 'value': '2-Day Overlap With John (Lead Developer)'},
      {'type': 'text', 'value': 'Manager Is Available For Approval All Week'},
      {
        'type': 'text',
        'value': 'Youll Have 12 Days Remaining After This Request'
      },
    ];
  }

  void _handleSendMessage() {
    final text = _textController.text.trim();
    if (text.isNotEmpty) {
      setState(() {
        _messages.add(text);
        _textController.clear();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final String latestMessage = _messages.isNotEmpty ? _messages.last : "";

    return Scaffold(
      backgroundColor: const Color(0xffF7F9FB),
      body: Row(
        children: [
          Expanded(
            flex: 2,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.only(
                          left: AppSpacing.sm,
                          right: AppSpacing.sm,
                          top: AppSpacing.xxl),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            color: const Color(0xffF7F9FB),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Container(
                                constraints: BoxConstraints(
                                  maxWidth:
                                      MediaQuery.of(context).size.width * 0.7,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE9F2F7),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 10,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    CircleAvatar(
                                      backgroundColor: const Color(0xFF0058FF),
                                      radius: 12,
                                      child: Text(
                                        'D',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: MediaQuery.of(context)
                                                      .size
                                                      .width >
                                                  1600
                                              ? 17
                                              : 15,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Flexible(
                                      child: Text(
                                        latestMessage.isNotEmpty
                                            ? latestMessage
                                            : "Type a message below...",
                                        style: TextStyle(
                                          fontSize: MediaQuery.of(context)
                                                      .size
                                                      .width >
                                                  1600
                                              ? 17
                                              : 15,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: AppSpacing.xs),
                          WidgetComponent(
                            uiData: userInterfaceData,
                          ),
                          SizedBox(
                            height: AppSpacing.xxs,
                          ),
                          Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(AppSpacing
                                  .sm), // 👈 Adjust the radius as needed
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Padding(
                              padding:
                                  const EdgeInsets.only(left: AppSpacing.sm),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.only(bottom: 8),
                                    child: Text(
                                      "Recommendation in Context",
                                      style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  Wrap(
                                    spacing: 10,
                                    runSpacing: 10,
                                    children: optionButtonsData.map((data) {
                                      return OptionComponent(data: data);
                                    }).toList(),
                                  )
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: AppSpacing.sm),
                          RecommendationBox(data: recommendationData),
                          
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: AppSpacing.xs,
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    left: AppSpacing.sm,
                    right: AppSpacing.sm,
                  ),
                  child: ChatField(
                    isGeneralLoading: false,
                    onSendMessage: _handleSendMessage,
                    controller: _textController,
                    actionButtonsData: actionButtonsData,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              padding: EdgeInsets.only(top: AppSpacing.xxl),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  left: BorderSide(color: Colors.grey.shade300, width: 1),
                ),
              ),
              child: RightSectionWithTabs(jsonData: jsonData),
            ),
          )
        ],
      ),
    );
  }
}

class WidgetComponent extends StatelessWidget {
  final Map<String, dynamic>? uiData;

  const WidgetComponent({super.key, this.uiData});

  @override
  Widget build(BuildContext context) {
    // Extract data from user_interface_components
    final header = uiData?['header'];
    final chatInterface = uiData?['chat_interface'];
    final embeddedForm = chatInterface?['embedded_form'];
    final formHeader = embeddedForm?['form_header'];
    final formFields = embeddedForm?['form_fields'];

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.sm),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section from JSON
          // if (header != null) ...[
          //   Row(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     children: [
          //       Text(
          //         header['branding'] ?? '⚡ QuickChat AI Assistant',
          //         style: const TextStyle(
          //           fontSize: 16,
          //           fontWeight: FontWeight.bold,
          //           color: Color(0xFF0058FF),
          //         ),
          //       ),
          //       Text(
          //         header['user_info'] ?? '👤 User',
          //         style: const TextStyle(
          //           fontSize: 14,
          //           fontWeight: FontWeight.w500,
          //         ),
          //       ),
          //     ],
          //   ),
          //   const SizedBox(height: 16),
          // ],

          // Form header from JSON
          if (formHeader != null) ...[
            Text(
              formHeader,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TestTiemposText',
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Form fields from JSON
          if (formFields != null) ...[
            // Leave type dropdown
            if (formFields['leave_type'] != null) ...[
              const Text(
                'Leave Type',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  formFields['leave_type']['selected'] ?? 'Annual Leave',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Date range
            if (formFields['date_range'] != null) ...[
              const Text(
                'Date Range',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 10),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        formFields['date_range']['start_date'] ?? 'Start Date',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text('to'),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 10),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        formFields['date_range']['end_date'] ?? 'End Date',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],

            // Reason field
            if (formFields['reason'] != null) ...[
              const Text(
                'Reason',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  formFields['reason']['value'] ?? 'Enter reason...',
                  style: TextStyle(
                    fontSize: 14,
                    color: formFields['reason']['value'] != null
                        ? Colors.black
                        : Colors.grey.shade500,
                  ),
                ),
              ),
            ],
          ],

          // Fallback content if no JSON data
          if (uiData == null) ...[
            const Text(
              "LEAVE REQUEST",
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'TestTiemposText',
                fontWeight: FontWeight.w600,
              ),
            ),
            // const SizedBox(height: 12),
            const Text(
              "Form fields will be populated from JSON data",
              style: TextStyle(fontSize: 14),
            ),
          ],
        ],
      ),
    );
  }
}

class OptionComponent extends StatelessWidget {
  final Map<String, dynamic> data;

  const OptionComponent({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    switch (data['type']) {
      case 'card':
        return GestureDetector(
          onTap: data['onTap'],
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 1),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  data['icon'],
                  height: 14,
                  width: 14,
                  colorFilter: const ColorFilter.mode(
                      Color(0xFF0058FF), BlendMode.srcIn),
                ),
                const SizedBox(width: 8),
                Text(
                  data['label'],
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }
}

class RecommendationBox extends StatelessWidget {
  final List<Map<String, dynamic>> data;

  const RecommendationBox({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF7DA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: data.map((item) {
          switch (item['type']) {
            case 'text':
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    if (item['icon'] != null &&
                        item['icon'].toString().isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Text(
                          item['icon'],
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    Expanded(
                      child: Text(
                        item['value'],
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            default:
              return const SizedBox.shrink();
          }
        }).toList(),
      ),
    );
  }
}

class ChatField extends StatefulWidget {
  final double? width;
  final double? height;
  final bool isGeneralLoading;
  final Function() onSendMessage;
  final Function()? onCancelRequest;
  final TextEditingController? controller;
  final List<Map<String, dynamic>>? actionButtonsData;

  ChatField({
    super.key,
    this.width,
    this.height,
    required this.isGeneralLoading,
    required this.onSendMessage,
    this.onCancelRequest,
    this.controller,
    this.actionButtonsData,
  });

  @override
  State<ChatField> createState() => _ChatFieldState();
}

class _ChatFieldState extends State<ChatField> {
  late TextEditingController chatController;
  final FocusNode _focusNode = FocusNode();
  bool _isShiftPressed = false;

  @override
  void initState() {
    super.initState();
    chatController = widget.controller ?? TextEditingController();
    chatController.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      chatController.dispose();
    }
    chatController.removeListener(_onTextChanged);
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    setState(() {});
  }

  bool get hasTextInChatField => chatController.text.trim().isNotEmpty;

  void _sendMessage() {
    if (widget.isGeneralLoading) {
      return;
    }

    final cleanedText = chatController.text.trim();
    if (cleanedText.isNotEmpty) {
      chatController.text = cleanedText;
      widget.onSendMessage();
    }
  }

  void _cancelRequest() {
    chatController.clear();

    if (widget.onCancelRequest != null) {
      widget.onCancelRequest!();
    }
  }

  Future<void> _saveScreen() async {
    try {
      print("Save button pressed - saving screen data to local storage");

      final prefs = await SharedPreferences.getInstance();

      final screenData = {
        'currentText': chatController.text,
        'timestamp': DateTime.now().toIso8601String(),
        'screenType': 'web_solution_widgets',
      };

      final jsonString = jsonEncode(screenData);

      final key = 'screen_data_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString(key, jsonString);
      await prefs.setString('latest_screen_data', jsonString);

      print("Data saved to local storage: $jsonString");

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Screen data saved to local storage successfully!'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print("Error saving to local storage: $e");

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save screen data!'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildButton({
    required Widget icon,
    required Function() onPressed,
    Color? iconColor,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        hoverColor: const Color(0xFF0058FF),
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            // color: Colors.amber,
            borderRadius: BorderRadius.circular(8),
          ),
          child: icon,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        bottom: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.md),
        border: Border.all(color: Color(0xffD0D0D0), width: 0.5),
      ),
      height: 50,
      padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.xs, vertical: AppSpacing.xxs),
      child: Row(
        children: [
          Expanded(
            child: KeyboardListener(
              focusNode: _focusNode,
              onKeyEvent: (KeyEvent event) {
                if (event is KeyDownEvent) {
                  _isShiftPressed = HardwareKeyboard.instance.isShiftPressed;

                  if (event.logicalKey == LogicalKeyboardKey.enter) {
                    if (!_isShiftPressed && !widget.isGeneralLoading) {
                      _sendMessage();
                    }
                  }
                } else if (event is KeyUpEvent) {
                  _isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
                }
              },
              child: TextField(
                cursorHeight: 16,
                controller: chatController,
                maxLines: 1,
                enabled: !widget.isGeneralLoading,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.normal,
                ),
                decoration: InputDecoration(
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  hintStyle: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    color: Colors.grey.shade500,
                    fontFamily: "TiemposText",
                  ),
                  hintText: widget.isGeneralLoading
                      ? AppLocalizations.of(context)
                          .translate('home.sendingMessage')
                      : AppLocalizations.of(context).translate('home.askNSL'),
                  border: OutlineInputBorder(borderSide: BorderSide.none),
                  enabledBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                  focusedBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                  errorBorder: OutlineInputBorder(borderSide: BorderSide.none),
                  disabledBorder:
                      OutlineInputBorder(borderSide: BorderSide.none),
                ),
                onSubmitted: (value) {
                  if (!widget.isGeneralLoading) {
                    _sendMessage();
                  }
                },
              ),
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Row(
            children: [
              // Generate buttons from JSON data
              if (widget.actionButtonsData != null)
                ...widget.actionButtonsData!.map((buttonData) {
                  return _buildButton(
                    icon: buttonData['image'] != null &&
                            buttonData['image'].toString().isNotEmpty
                        ? SvgPicture.asset(
                            buttonData['image'],
                            width: 30,
                            height: 30,
                          )
                        : Icon(
                            buttonData['text'] == 'Submit Request'
                                ? Icons.send
                                : buttonData['text'] == 'Cancel'
                                    ? Icons.close
                                    : Icons.save,
                            size: 20,
                          ),
                    onPressed: () {
                      if (buttonData['text'] == 'Submit Request') {
                        _sendMessage();
                      } else if (buttonData['text'] == 'Cancel') {
                        _cancelRequest();
                      } else if (buttonData['text'] == 'Save as Draft') {
                        _saveScreen();
                      }
                      print("${buttonData['text']} clicked");
                    },
                  );
                }).toList(),

              // Fallback buttons if no JSON data
              if (widget.actionButtonsData == null ||
                  widget.actionButtonsData!.isEmpty) ...[
                _buildButton(
                  icon: SvgPicture.asset(
                    'assets/images/my_business/solutions/cancel_sol.svg',
                  ),
                  onPressed: _cancelRequest,
                ),
                const SizedBox(width: AppSpacing.xs),
                _buildButton(
                  icon: SvgPicture.asset(
                    'assets/images/my_business/solutions/save_solution.svg',
                  ),
                  onPressed: _saveScreen,
                ),
                const SizedBox(width: AppSpacing.xs),
                _buildButton(
                  icon: SvgPicture.asset(
                    'assets/images/my_business/solutions/send_sol.svg',
                  ),
                  onPressed: (hasTextInChatField && !widget.isGeneralLoading)
                      ? _sendMessage
                      : () {},
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}

class RightSectionWithTabs extends StatefulWidget {
  final Map<String, dynamic>? jsonData;

  const RightSectionWithTabs({super.key, this.jsonData});

  @override
  State<RightSectionWithTabs> createState() => _RightSectionWithTabsState();
}

class _RightSectionWithTabsState extends State<RightSectionWithTabs> {
  int selectedIndex = 0;
  List<Map<String, dynamic>> tabs = [];

  @override
  void initState() {
    super.initState();
    _loadTabsData();
  }

  @override
  void didUpdateWidget(covariant RightSectionWithTabs oldWidget) {
    _loadTabsData();
    super.didUpdateWidget(oldWidget);
  }

  void _loadTabsData() {
    if (widget.jsonData != null) {
      final leaveAnalytics = widget.jsonData!['leave_analytics_dashboard'];
      final dataAnalytics = leaveAnalytics?['data_analytics'];
      final systemArchitecture = leaveAnalytics?['system_architecture'];

      // Build Related tab content from data_analytics
      String relatedContent = _buildDataAnalyticsContent(dataAnalytics);

      // Build Contextual tab content from system_architecture
      String contextualContent =
          _buildSystemArchitectureContent(systemArchitecture);

      tabs = [
        {
          "label": "Related",
          "iconPath":
              'assets/images/my_business/solutions/solution_related.svg',
          "content": relatedContent,
        },
        {
          "label": "Contextual",
          "iconPath":
              'assets/images/my_business/solutions/solution_contextual.svg',
          "content": contextualContent,
        },
      ];
      setState(() {});
    } else {
      // Fallback tabs
      tabs = [
        {
          "label": "Related",
          "iconPath":
              'assets/images/my_business/solutions/solution_related.svg',
          "content": "This is the Related tab content.\n" * 30,
        },
        {
          "label": "Contextual",
          "iconPath":
              'assets/images/my_business/solutions/solution_contextual.svg',
          "content": "This is the Contextual tab content.\n" * 30,
        },
      ];
    }
  }

  String _buildDataAnalyticsContent(Map<String, dynamic>? dataAnalytics) {
    if (dataAnalytics == null) return "No data analytics available.";

    StringBuffer content = StringBuffer();
    // content.writeln("LEAVE ANALYTICS\n");

    // Leave Analytics Card
    final leaveAnalyticsCard = dataAnalytics['leave_analytics_card'];
    if (leaveAnalyticsCard != null) {
      content.writeln("${leaveAnalyticsCard['header'] ?? 'LEAVE ANALYTICS'}\n");

      final personalBalance = leaveAnalyticsCard['personal_balance'];
      if (personalBalance != null) {
        content.writeln("Personal Balance:");
        content.writeln(
            "• Current Usage: ${personalBalance['current_usage'] ?? 'N/A'}");
        content
            .writeln("• Percentage: ${personalBalance['percentage'] ?? 'N/A'}");
        content.writeln(
            "• Remaining After Request: ${personalBalance['remaining_after_request'] ?? 'N/A'}\n");
      }
    }

    // Team Availability Card
    final teamAvailabilityCard = dataAnalytics['team_availability_card'];
    if (teamAvailabilityCard != null) {
      content.writeln("TEAM AVAILABILITY\n");
      content.writeln(
          "Time Period: ${teamAvailabilityCard['time_period'] ?? 'N/A'}\n");

      final teamMembers =
          teamAvailabilityCard['team_members'] as List<dynamic>?;
      if (teamMembers != null) {
        content.writeln("Team Members:");
        for (var member in teamMembers) {
          content.writeln("• ${member['name']} (${member['role']})");
          content
              .writeln("  Availability: ${member['availability_percentage']}");
          content.writeln("  Status: ${member['status']}\n");
        }
      }
    }

    // Conflicts & Warnings Card
    final conflictsCard = dataAnalytics['conflicts_warnings_card'];
    if (conflictsCard != null) {
      content.writeln("⚠️ CONFLICTS & WARNINGS\n");

      final conflicts =
          conflictsCard['high_priority_conflicts'] as List<dynamic>?;
      if (conflicts != null) {
        content.writeln("High Priority Conflicts:");
        for (var conflict in conflicts) {
          content.writeln(
              "• ${conflict['description']} (${conflict['severity']})");
        }
        content.writeln();
      }

      final aiSuggestion = conflictsCard['ai_suggestion'];
      if (aiSuggestion != null) {
        content.writeln("💡 AI Suggestion:");
        content.writeln("${aiSuggestion['recommendation'] ?? 'N/A'}\n");

        final benefits = aiSuggestion['benefits'] as List<dynamic>?;
        if (benefits != null) {
          content.writeln("Benefits:");
          for (var benefit in benefits) {
            content.writeln("• $benefit");
          }
        }
      }
    }

    return content.toString();
  }

  String _buildSystemArchitectureContent(
      Map<String, dynamic>? systemArchitecture) {
    if (systemArchitecture == null)
      return "No system architecture information available.";

    StringBuffer content = StringBuffer();
    content.writeln("🏗️ SYSTEM ARCHITECTURE\n");

    content.writeln(
        "${systemArchitecture['description'] ?? 'Backend infrastructure supporting the leave analytics dashboard'}\n");

    // Real-time Processing
    final realTimeProcessing = systemArchitecture['real_time_processing'];
    if (realTimeProcessing != null) {
      content.writeln("⚡ REAL-TIME PROCESSING\n");

      final dataSources = realTimeProcessing['data_sources'] as List<dynamic>?;
      if (dataSources != null) {
        content.writeln("Data Sources:");
        for (var source in dataSources) {
          content.writeln("• $source");
        }
        content.writeln();
      }

      final updateFreqs = realTimeProcessing['update_frequencies'];
      if (updateFreqs != null) {
        content.writeln("Update Frequencies:");
        updateFreqs.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }
    }

    // AI Processing
    final aiProcessing = systemArchitecture['ai_processing'];
    if (aiProcessing != null) {
      content.writeln("🤖 AI PROCESSING\n");

      final intentRecognition = aiProcessing['intent_recognition'];
      if (intentRecognition != null) {
        content.writeln("Intent Recognition:");
        content.writeln("• Input: ${intentRecognition['input'] ?? 'N/A'}");
        content.writeln(
            "• Confidence: ${intentRecognition['confidence'] ?? 'N/A'}\n");

        final parsedEntities = intentRecognition['parsed_entities'];
        if (parsedEntities != null) {
          content.writeln("Parsed Entities:");
          parsedEntities.forEach((key, value) {
            content.writeln("• ${key.toUpperCase()}: $value");
          });
          content.writeln();
        }
      }

      final suggestionEngine = aiProcessing['suggestion_engine'];
      if (suggestionEngine != null) {
        content.writeln("Suggestion Engine:");
        suggestionEngine.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }
    }

    // Integration Points
    final integrationPoints =
        systemArchitecture['integration_points'] as List<dynamic>?;
    if (integrationPoints != null) {
      content.writeln("🔗 INTEGRATION POINTS\n");
      for (var point in integrationPoints) {
        content.writeln("• $point");
      }
      content.writeln();
    }

    // Performance Metrics
    final performanceMetrics = systemArchitecture['performance_metrics'];
    if (performanceMetrics != null) {
      content.writeln("📈 PERFORMANCE METRICS\n");

      final responseTimes = performanceMetrics['response_times'];
      if (responseTimes != null) {
        content.writeln("Response Times:");
        responseTimes.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
        content.writeln();
      }

      final dataFreshness = performanceMetrics['data_freshness'];
      if (dataFreshness != null) {
        content.writeln("Data Freshness:");
        dataFreshness.forEach((key, value) {
          content
              .writeln("• ${key.replaceAll('_', ' ').toUpperCase()}: $value");
        });
      }
    }

    return content.toString();
  }
@override
Widget build(BuildContext context) {
  return Container(
    color: Colors.white,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 44,
          child: Row(
            children: List.generate(tabs.length, (index) {
              final isSelected = selectedIndex == index;
              final isTwoTabs = tabs.length == 2;

              return Expanded(
                child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () => setState(() => selectedIndex = index),
                    child: Container(
                      decoration: BoxDecoration(
                         color: isTwoTabs && isSelected
                          ? const Color(0xFFF1F5FB)
                          : Colors.white,
                        border: Border.all(color: Colors.grey.shade300)
                      ),
                     
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SvgPicture.asset(
                            tabs[index]["iconPath"]!,
                            width: 14,
                            height: 14,
                            colorFilter: const ColorFilter.mode(
                                Colors.blue, BlendMode.srcIn),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            tabs[index]["label"]!,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10),
            child: Container(
              width: double.infinity,
              child: Text(
                tabs[selectedIndex]["content"]!,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ),
        ),
      ],
    ),
  );
}
}