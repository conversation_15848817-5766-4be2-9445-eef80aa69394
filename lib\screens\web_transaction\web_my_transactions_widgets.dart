import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart' as f1;
//import 'package:pie_chart/pie_chart.dart' as pc;
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:nsl/models/WorkflowItem.dart';
import 'package:nsl/models/task_data.dart';

class WebMyTransactionsWidgets extends StatefulWidget {
  const WebMyTransactionsWidgets({super.key});

  @override
  State<WebMyTransactionsWidgets> createState() =>
      _WebMyTransactionsWidgetsState();
}

class _WebMyTransactionsWidgetsState extends State<WebMyTransactionsWidgets> {
  bool _showSearchBox = false;
  bool showPopup = false;
  dynamic selectedItem;

  int currentPageTwo = 0;
  int currentPageSeven = 0;
  final int itemsPerPage = 4;

  bool showGlobalModal = false;

  void openGlobalModal() {
    setState(() {
      showGlobalModal = true;
    });
  }

  void closeGlobalModal() {
    setState(() {
      showGlobalModal = false;
    });
  }

  void _toggleSearchBox() {
    setState(() {
      _showSearchBox = !_showSearchBox;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFEDEDED),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 42),
                  child: SizedBox(
                    width: 1326,
                    height: 240,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top: 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const SizedBox(
                                width: 121,
                                height: 20,
                                child: Text(
                                  "My Transaction",
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    fontFamily: 'Inter',
                                    color: Color(0xFF606060),
                                  ),
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(right: 20),
                                child: SizedBox(
                                  width: 324,
                                  height: 36,
                                  child: Stack(
                                    alignment: Alignment.centerRight,
                                    children: [
                                      AnimatedOpacity(
                                        duration:
                                            const Duration(milliseconds: 250),
                                        opacity: _showSearchBox ? 1.0 : 0.0,
                                        child: Visibility(
                                          visible: _showSearchBox,
                                          child: TextField(
                                            autofocus: true,
                                            decoration: InputDecoration(
                                              hintText: "Search Notification",
                                              filled: true,
                                              fillColor: Colors.white,
                                              contentPadding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 10),
                                              suffixIcon: InkWell(
                                                onTap: _toggleSearchBox,
                                                child: const Icon(Icons.search,
                                                    size: 20,
                                                    color: Color(0xFF7E7D7D)),
                                              ),
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                                borderSide: const BorderSide(
                                                    color: Color(0xFFCCCCCC)),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        right: 3,
                                        child: InkWell(
                                          onTap: _toggleSearchBox,
                                          child: const Icon(Icons.search,
                                              size: 20,
                                              color: Color(0xFF7E7D7D)),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 6),
                        // Row of boxes with horizontal scroll
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              _buildBox(
                                  taskPriorityTwo, itemsPerPage, currentPageTwo,
                                  (newPage) {
                                setState(() {
                                  currentPageTwo = newPage;
                                });
                              }),
                              const SizedBox(width: 14),
                              _buildBox(taskPrioritySeven, itemsPerPage,
                                  currentPageSeven, (newPage) {
                                setState(() {
                                  currentPageSeven = newPage;
                                });
                              }),
                              const SizedBox(width: 14),
                              _buildBoxPieChart(
                                  "Status of all Solutions", "Total 100"),
                              const SizedBox(width: 14),
                              _buildBoxLineChart(
                                  "Work-in-progress Tasks Timeline"),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 18),
                TabChartSection(
                  onOpenModal: openGlobalModal,
                ),
              ],
            ),
          ),
          if (showGlobalModal)
            Positioned.fill(
              child: GestureDetector(
                onTap: closeGlobalModal,
                child: Container(color: Colors.black.withOpacity(0.5)),
              ),
            ),
          if (showGlobalModal)
            Positioned(
              left: MediaQuery.of(context).size.width / 2 - 330, // (660 / 2)
              top: MediaQuery.of(context).size.height / 2 -
                  304.5 -
                  8, // center - half height - shift up
              child: Container(
                width: 660,
                height: 590,
                margin:
                    const EdgeInsets.only(top: 20), // Optional, visual padding
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x4ACDD7E8),
                      blurRadius: 10,
                      offset: Offset(0, 3),
                    ),
                  ],
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: 10,
                      right: 10,
                      child: IconButton(
                        icon: Icon(Icons.close),
                        onPressed: closeGlobalModal,
                      ),
                    ),
                    Positioned(
                      left: 24,
                      top: 22.5,
                      child: Text(
                        "Transaction Details",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          height: 1.21, // 17px / 14px
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                          textBaseline: TextBaseline.alphabetic,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      top: 40,
                      left: 24,
                      right: 10,
                      child: Divider(
                        color: Color(0xFFE4E4E4),
                        thickness: 1,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 60,
                      child: Text(
                        "Solution Name",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          height: 1.2, // 12px line height
                          fontWeight: FontWeight.w500, // "medium"
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      top: 60,
                      right: 24,
                      child: Text(
                        "April 13, 2025",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 12,
                          height: 1.25, // 15px line-height
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                    Positioned(
                      top: 75,
                      left: 25,
                      child: Text(
                        "Leave Management",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          height: 1.21, // 17px line-height
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0,
                          color: Color(0xFF3379FF),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      top: 75,
                      right: 24,
                      child: Text(
                        "3:30 PM",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 12,
                          height: 1.25, // Equivalent to 15px line-height
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                    Positioned(
                      top: 110,
                      left: 25,
                      child: Text(
                        "Task Name",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          height: 1.2, // 12px line-height
                          fontWeight: FontWeight.w500, // Medium weight
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      top: 110,
                      left: 331,
                      child: Text(
                        "Transaction ID",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          height: 1.2, // 12px line-height
                          fontWeight: FontWeight.w500, // Medium
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      top: 125,
                      left: 25,
                      child: Text(
                        "Leave Request",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          height: 1.2, // 17px line-height
                          fontWeight: FontWeight.w600, // Bold
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      top: 125,
                      left: 331,
                      child: Text(
                        "987654321e-76543-2345-2",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          height: 1.2, // 17px line-height
                          fontWeight: FontWeight.w600, // Equivalent to bold
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      top: 145,
                      left: 24,
                      right: 10,
                      child: Divider(
                        color: Color(0xFFE4E4E4),
                        thickness: 1,
                      ),
                    ),
                    Positioned(
                      top: 165,
                      left: 25,
                      child: Text(
                        "Leave Request",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          height: 1.2, // 17px line-height
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0,
                          color: Color(0xFF000000),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      right: 25,
                      top: 165, // adjust `top` as needed for placement
                      child: Container(
                        width: 85,
                        height: 23,
                        decoration: BoxDecoration(
                          color: Color(0xFFA8FCDB),
                          borderRadius: BorderRadius.circular(13),
                        ),
                        child: Center(
                          child: Text(
                            "Approved",
                            style: TextStyle(
                              fontFamily: 'Inter',
                              fontSize: 12,
                              fontWeight: FontWeight.w500, // medium
                              height: 1.25, // line-height: 15px
                              letterSpacing: 0,
                              color: Color(0xFF008B60),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 200,
                      child: Text(
                        "Reason",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // medium
                          height: 1.2, // line-height: 12px
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 215,
                      child: Text(
                        "Illness and health-related and Medical appointments",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight:
                              FontWeight.w600, // 600 corresponds to semi-bold
                          height: 1.2, // 17px line height
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 250,
                      child: Text(
                        "Leave Type",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // medium
                          height: 1.2, // line height of 12px
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 232,
                      top: 250,
                      child: Text(
                        "Duration in days",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // 'medium'
                          height: 1.2, // line-height of 12px
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 439,
                      top: 250,
                      child: Text(
                        "Request ID",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // 'medium'
                          height: 1.2, // line-height of 12px
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 265,
                      child: Text(
                        "Sick Leave",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 weight = semi-bold
                          height: 1.21, // 17 / 14 ≈ 1.21 for line-height
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 232,
                      top: 265,
                      child: Text(
                        "1",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 weight = semi-bold
                          height: 1.21, // 17 / 14 ≈ 1.21 for line-height
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 439,
                      top: 265,
                      child: Text(
                        "April 13, 2025",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 = semi-bold
                          height: 1.21, // 17 / 14 ≈ 1.21 line height
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                          // opacity 1 is default, no need to set
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 295,
                      child: Text(
                        "Employee ID",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // medium
                          height: 1.2, // 12 / 10 line height
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                          // opacity 1 by default
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 232,
                      top: 295,
                      child: Text(
                        "Start Date",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // medium
                          height: 1.2, // 12 / 10 line height
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                          // opacity 1 by default
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 439,
                      top: 295,
                      child: Text(
                        "End Date",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // medium
                          height: 1.2, // 12 / 10 line height
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                          // opacity 1 by default
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 310,
                      child: Text(
                        "5432-asy-234",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 weight
                          height:
                              17 / 14, // line height 17px with font size 14px
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                          // opacity 1 by default
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 232,
                      top: 310,
                      child: Text(
                        "April 13, 2025",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 weight
                          height:
                              17 / 14, // line height 17px with font size 14px
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                          // opacity 1 by default
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 439,
                      top: 310,
                      child: Text(
                        "April 13, 2025",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 weight
                          height:
                              17 / 14, // line height 17px with font size 14px
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                          // opacity 1 by default
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      top: 330,
                      left: 24,
                      right: 10,
                      child: Divider(
                        color: Color(0xFFE4E4E4),
                        thickness: 1,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 355,
                      child: Text(
                        "Document",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.bold, // equivalent to 'bold'
                          height:
                              17 / 14, // line height = 17px / font size = 14px
                          letterSpacing: 0,
                          color: Color(0xFF000000), // #000000
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 390,
                      child: Text(
                        "File Name",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // 'medium'
                          height:
                              12 / 10, // line-height = 12px / font-size = 10px
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 232,
                      top: 390,
                      child: Text(
                        "File Type",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // 'medium'
                          height:
                              12 / 10, // line-height = 12px / font-size = 10px
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 439,
                      top: 390,
                      child: Text(
                        "Upload Date",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // 'medium'
                          height:
                              12 / 10, // line-height = 12px / font-size = 10px
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 405,
                      child: Text(
                        "Leave Request v01",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 weight
                          height: 17 / 14, // line-height
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 232,
                      top: 405,
                      child: Text(
                        "PDF",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 weight
                          height: 17 / 14, // line-height
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 439,
                      top: 405,
                      child: Text(
                        "April 13, 2025",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 weight
                          height: 17 / 14, // line-height
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 440,
                      child: Text(
                        "Document ID",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // medium
                          height: 12 / 10, // line-height
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                          // Opacity 1 means fully visible, no extra step needed
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 331,
                      top: 440,
                      child: Text(
                        "Request ID",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500, // medium
                          height: 12 / 10, // line-height
                          letterSpacing: 0,
                          color: Color(0xFFB3B3B3),
                          // Opacity 1 means fully visible, no extra step needed
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 25,
                      top: 455,
                      child: Text(
                        "5432-asy-234-5432-asy-234",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 = semi-bold
                          height: 17 / 14, // line-height
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      left: 331,
                      top: 455,
                      child: Text(
                        "5432-asy-234-5432-asy",
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          fontWeight: FontWeight.w600, // 600 = semi-bold
                          height: 17 / 14, // line-height
                          letterSpacing: 0,
                          color: Color(0xFF606060),
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    Positioned(
                      top: 475,
                      left: 24,
                      right: 10,
                      child: Divider(
                        color: Color(0xFFE4E4E4),
                        thickness: 1,
                      ),
                    ),
                    Positioned(
                      left: 43,
                      top: 500,
                      child: Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: "Note: ",
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                height: 17 / 14,
                                letterSpacing: 0,
                                color: Color(0xFF000000),
                              ),
                            ),
                            TextSpan(
                              text:
                                  "Calculate Business Days Check Leave Balance Validate Leave Policy Compliance",
                              style: TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                height: 15 / 12,
                                letterSpacing: 0,
                                color: Color(0xFF606060),
                              ),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

Widget _buildBox(List<String> taskPriority, int itemsPerPage, int currentPage,
    Function(int) onPageChange) {
  int totalPages = ((taskPriority.length - 1) / itemsPerPage).ceil();
  final displayItems = taskPriority.sublist(
    1 + currentPage * itemsPerPage,
    (1 + (currentPage + 1) * itemsPerPage).clamp(1, taskPriority.length),
  );

  return SizedBox(
    width: 298,
    height: 185,
    child: Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: const Color(0x1A000000),
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 18, left: 16),
                child: SizedBox(
                  width: 146,
                  height: 17,
                  child: Text(
                    taskPriority[0]
                        .split(" ")
                        .sublist(0, taskPriority[0].split(" ").length - 2)
                        .join(" "),
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      height: 16 / 14,
                      fontFamily: 'Inter',
                      color: Color(0xFF606060),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 16, right: 16),
                child: Text(
                  taskPriority[0]
                      .split(" ")
                      .sublist(taskPriority[0].split(" ").length - 2)
                      .join(" "),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    height: 21 / 18,
                    fontFamily: 'Inter',
                    color: taskPriority[0].contains("2 Days")
                        ? const Color(0xFFF47B74)
                        : const Color(0xFFF5BA76),
                  ),
                ),
              ),
            ],
          ),

          // Items
          ...displayItems.map((item) {
            return Padding(
              padding: const EdgeInsets.only(left: 22, top: 6, bottom: 2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: taskPriority[0].contains("2 Days")
                              ? const Color(0xFFF47B74)
                              : const Color(0xFFFECA8E),
                        ),
                        child: Center(
                          child: Transform.rotate(
                            angle: -0.785398,
                            child: const Icon(
                              Icons.arrow_forward,
                              size: 12,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        item,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF606060),
                          fontFamily: 'Inter',
                          height: 1.25,
                        ),
                      ),
                    ],
                  ),
                  const Padding(
                    padding: EdgeInsets.only(right: 34),
                    child: Text(
                      ">",
                      style: TextStyle(
                        color: Color(0xFFEDEDED),
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),

          const Spacer(),

          // Pagination
          Padding(
            padding: const EdgeInsets.only(bottom: 12), // Pushes pagination up
            child: Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildCircleArrowButton(
                    icon: Icons.arrow_back,
                    onTap: currentPage > 0
                        ? () => onPageChange(currentPage - 1)
                        : null,
                  ),
                  for (int i = 0; i < totalPages; i++)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2),
                      child: Container(
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: i == currentPage
                              ? const Color(0xFF242424)
                              : Colors.transparent,
                          border: i == currentPage
                              ? null
                              : Border.all(
                                  color:
                                      const Color(0xFF242424).withOpacity(0.2),
                                ),
                        ),
                      ),
                    ),
                  _buildCircleArrowButton(
                    icon: Icons.arrow_forward,
                    onTap: currentPage < totalPages - 1
                        ? () => onPageChange(currentPage + 1)
                        : null,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildCircleArrowButton({required IconData icon, VoidCallback? onTap}) {
  return InkWell(
    onTap: onTap,
    borderRadius: BorderRadius.circular(12),
    child: Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: onTap != null ? Colors.black : Colors.grey.shade300,
          width: 1,
        ),
        color: Colors.white,
      ),
      child: Center(
        child: Icon(
          icon,
          size: 12,
          color: onTap != null ? Colors.black : Colors.grey.shade400,
        ),
      ),
    ),
  );
}

/* Widget _buildLeftCircle(IconData iconData) {
  return Opacity(
    opacity: 0.6, // Fully visible
    child:
        _buildCircleArrow(iconData, top: 157, left: 109, right: 169, bottom: 8),
  );
}

Widget _buildRightCircle(IconData iconData) {
  return Opacity(
    opacity: 1.0, // 60% opacity
    child:
        _buildCircleArrow(iconData, top: 157, left: 169, right: 109, bottom: 8),
  );
}

// Reusable helper for circular arrow button
Widget _buildCircleArrow(IconData iconData,
    {double? top, double? left, double? right, double? bottom}) {
  return Positioned(
    top: top,
    left: left,
    right: right,
    bottom: bottom,
    child: Container(
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.black, width: 1),
      ),
      child: Center(
        child: Icon(
          iconData,
          size: 16,
          color: Colors.black,
        ),
      ),
    ),
  );
} */

Widget _buildBoxPieChart(String heading, String content) {
  final double completed = 56;
  final double pending = 44;

  final Color completedColor = const Color.fromRGBO(146, 222, 163, 1);
  final Color pendingColor = const Color.fromRGBO(253, 238, 224, 1);

  return SizedBox(
    width: 210,
    height: 188,
    child: Container(
      // padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white, // background
        borderRadius: BorderRadius.circular(12), // border-radius: 12px
        boxShadow: [
          BoxShadow(
            color: const Color(0x1A000000), // #0000001A (10% black)
            offset: const Offset(0, 2), // 0px 2px
            blurRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 16, left: 16, bottom: 10.91),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                heading,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12, // correct as per UX
                  height: 15 / 12, // Line height = 15px
                  fontFamily: 'Inter',
                  letterSpacing: 0.0,
                  color: Color(0xFF606060),
                ),
              ),
            ),
          ),

          //const SizedBox(height: 4),
          Expanded(
            child: Stack(
              alignment: Alignment.center,
              children: [
                f1.PieChart(
                  f1.PieChartData(
                    sectionsSpace: 0,
                    centerSpaceRadius: 30,
                    sections: [
                      f1.PieChartSectionData(
                        value: completed,
                        title: completed.toInt().toString(),
                        color: completedColor,
                        radius: 18,
                        titleStyle: const TextStyle(
                          fontSize: 9,
                          //fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        titlePositionPercentageOffset: 0.5,
                      ),
                      f1.PieChartSectionData(
                        value: pending,
                        title: pending.toInt().toString(),
                        color: pendingColor,
                        radius: 18,
                        titleStyle: const TextStyle(
                          fontSize: 9,
                          //  fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        titlePositionPercentageOffset: 0.5,
                      ),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Total',
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                        height: 1,
                        fontFamily: 'Inter',
                        letterSpacing: 0,
                        color: Color.fromRGBO(170, 169, 169, 1),
                      ),
                    ),
                    Text(
                      content.replaceAll(RegExp(r'^[^\d]*'), ''),
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        height: 17 / 14,
                        fontFamily: 'Inter',
                        letterSpacing: 0,
                        color: Color.fromRGBO(96, 96, 96, 1),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),
          Padding(
            padding:
                EdgeInsets.only(bottom: 13), // Simulate `bottom: 13` spacing
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLegend(color: pendingColor, label: "Pending"),
                const SizedBox(width: 12),
                _buildLegend(color: completedColor, label: "Completed"),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildBoxLineChart(String heading) {
  return SizedBox(
    width: 420,
    height: 188,
    child: Container(
      padding: const EdgeInsets.only(left: 16, top: 8, right: 12, bottom: 18.5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A000000),
            offset: Offset(0, 2),
            blurRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and filter row unchanged
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 195,
                height: 15,
                child: Text(
                  heading,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                    fontSize: 12,
                    height: 15 / 12,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Inter',
                    letterSpacing: 0,
                    color: Color(0xFF606060),
                  ),
                ),
              ),
              const Row(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 16, left: 16),
                    child: Row(
                      children: [
                        Text("5D | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("1M | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("3M | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("6M | ",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                            )),
                        Text("1Y",
                            style: TextStyle(
                              fontSize: 10,
                              height: 1.6,
                              fontWeight: FontWeight.w600,
                              fontFamily: 'Inter',
                              letterSpacing: 0,
                              color: Colors.black,
                              decoration: TextDecoration.underline,
                            )),
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
          const SizedBox(height: 5),

          // Chart area + "Hrs" label below
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 11, // Increased flex for chart to take more space
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: 4,
                        right: 4,
                        top: 0,
                        bottom: 0), // added bottom padding to push chart down
                    child: f1.LineChart(
                      f1.LineChartData(
                        minX: 0,
                        maxX: 12,
                        minY: 0,
                        maxY: 180,
                        gridData: f1.FlGridData(
                          show: true,
                          drawVerticalLine: true,
                          drawHorizontalLine: true,
                          horizontalInterval: 45,
                          verticalInterval: 1,
                          checkToShowHorizontalLine: (value) =>
                              value == 0 ||
                              value == 45 ||
                              value == 90 ||
                              value == 135 ||
                              value == 180,
                          checkToShowVerticalLine: (value) =>
                              value >= 0 && value <= 12,
                          getDrawingHorizontalLine: (value) => f1.FlLine(
                            color: const Color(0xFFCCCCCC),
                            strokeWidth: 0.3,
                          ),
                          getDrawingVerticalLine: (value) => f1.FlLine(
                            color: const Color(0xFFCCCCCC),
                            strokeWidth: 0.3,
                          ),
                        ),
                        borderData: f1.FlBorderData(
                          show: true,
                          border: const Border(
                            left: BorderSide(
                                color: Color(0xFFCCCCCC), width: 0.3),
                            bottom: BorderSide(
                                color: Color(0xFFCCCCCC), width: 0.3),
                            top: BorderSide(
                                color: Color(0xFFCCCCCC),
                                width: 0.3), // now visible
                            right: BorderSide(
                                color: Color(0xFFCCCCCC),
                                width: 0.3), // now visible
                          ),
                        ),
                        titlesData: f1.FlTitlesData(
                          topTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(showTitles: false),
                          ),
                          rightTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(showTitles: false),
                          ),
                          bottomTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(
                              showTitles: true,
                              reservedSize: 14,
                              interval: 0.5,
                              getTitlesWidget: (value, meta) {
                                const months = [
                                  'Ap',
                                  'Ma',
                                  'Jn',
                                  'Ju',
                                  'Au',
                                  'Se',
                                  'Oc',
                                  'No',
                                  'De',
                                  'Ja',
                                  'Fe',
                                  'Ma',
                                ];
                                if (value % 1 == 0.5) {
                                  int index = value.floor();
                                  if (index >= 0 && index < months.length) {
                                    return Padding(
                                      padding: const EdgeInsets.only(
                                          left:
                                              8), // shifted right to center month label
                                      child: Text(
                                        months[index],
                                        style: const TextStyle(fontSize: 10),
                                      ),
                                    );
                                  }
                                }
                                return const SizedBox.shrink();
                              },
                            ),
                          ),
                          leftTitles: f1.AxisTitles(
                            sideTitles: f1.SideTitles(
                              showTitles: true,
                              reservedSize: 18,
                              interval: 45,
                              getTitlesWidget: (value, meta) {
                                return Text(
                                  '${value.toInt()}',
                                  style: const TextStyle(fontSize: 10),
                                );
                              },
                            ),
                          ),
                        ),
                        lineBarsData: [
                          f1.LineChartBarData(
                            isCurved: true,
                            curveSmoothness: 0.6,
                            spots: const [
                              f1.FlSpot(0, 45),
                              f1.FlSpot(2, 55),
                              f1.FlSpot(4, 39),
                              f1.FlSpot(6, 60),
                              f1.FlSpot(8, 170),
                              f1.FlSpot(11, 45),
                              f1.FlSpot(12, 3),
                            ],
                            barWidth: 1,
                            color: const Color(0xFF66BA04),
                            dotData: f1.FlDotData(show: false),
                          ),
                          f1.LineChartBarData(
                            isCurved: true,
                            curveSmoothness: 0.6,
                            spots: const [
                              f1.FlSpot(0, 90),
                              f1.FlSpot(2, 97),
                              f1.FlSpot(4, 120),
                              f1.FlSpot(6, 50),
                              f1.FlSpot(9, 140),
                              f1.FlSpot(11, 10),
                              f1.FlSpot(12, 170),
                            ],
                            barWidth: 1,
                            color: const Color(0xFF00008B),
                            dotData: f1.FlDotData(show: false),
                          ),
                          f1.LineChartBarData(
                            isCurved: true,
                            curveSmoothness: 0.6,
                            spots: const [
                              f1.FlSpot(0, 20),
                              f1.FlSpot(2, 70),
                              f1.FlSpot(5, 140),
                              f1.FlSpot(7, 100),
                              f1.FlSpot(9, 110),
                              f1.FlSpot(11, 133),
                              f1.FlSpot(12, 160),
                            ],
                            barWidth: 1,
                            color: const Color(0xFFD862FC),
                            dotData: f1.FlDotData(show: false),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // "Hrs" label at bottom-left corner with minimal padding
                Expanded(
                  flex: 0,
                  child: Align(
                    alignment: Alignment.bottomLeft,
                    child: Text(
                      'Hrs',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Inter',
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _buildLegend({required Color color, required String label}) {
  return Row(
    children: [
      Container(
        width: 12,
        height: 12,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color,
        ),
      ),
      const SizedBox(width: 4),
      Text(
        label,
        style: const TextStyle(fontSize: 12, color: Colors.black),
      ),
    ],
  );
}

class TabChartSection extends StatefulWidget {
//  const TabChartSection({super.key});
  final VoidCallback onOpenModal;

  const TabChartSection({super.key, required this.onOpenModal});

  @override
  State<TabChartSection> createState() => _TabChartSectionState();
}

class _TabChartSectionState extends State<TabChartSection> {
  List<WorkflowItem> allData = [];
  List<WorkflowItem> filteredData = [];
  String? popupType; // "Completed" or "Pending"

  String selectedTab = 'All';
  int currentPage = 1;
  int itemsPerPage = 12;
  int totalPages = 1;

  bool showPopup = false;
  WorkflowItem? selectedItem;
  bool showCenterModal = false;

  @override
  void initState() {
    super.initState();
    loadData();
  }

  void loadData() async {
    final String jsonString =
        await rootBundle.loadString('assets/data/workflows.json');
    final List<dynamic> jsonResponse = json.decode(jsonString);

    setState(() {
      allData =
          jsonResponse.map((data) => WorkflowItem.fromJson(data)).toList();
      applyFilter();
    });
  }

  void applyFilter() {
    setState(() {
      if (selectedTab == 'All') {
        filteredData = allData;
      } else {
        filteredData =
            allData.where((item) => item.status == selectedTab).toList();
      }
      totalPages = (filteredData.length / itemsPerPage).ceil();
      currentPage = 1;
    });
  }

  void nextPage() {
    if (currentPage < totalPages) {
      setState(() {
        currentPage++;
      });
    }
  }

  void prevPage() {
    if (currentPage > 1) {
      setState(() {
        currentPage--;
      });
    }
  }

  List<WorkflowItem> get paginatedData {
    int start = (currentPage - 1) * itemsPerPage;
    int end = start + itemsPerPage;
    return filteredData.sublist(
      start,
      end > filteredData.length ? filteredData.length : end,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _buildMainContent(), // Your main UI (tabs, charts, etc.)
        //  if (showCenterModal) _buildModalOverlay(), // Only when modal is active
      ],
    );
  }

/*   Widget _buildModalOverlay() {
    return Positioned.fill(
      child: GestureDetector(
        onTap: () {
          setState(() {
            showCenterModal = false;
          });
        },
        child: Container(
          color: Colors.black.withOpacity(0.4),
          child: Center(
            child: Container(
              width: 660,
              height: 609,
              //  margin: const EdgeInsets.only(top: 30), // push a bit up
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x4ACDD7E8),
                    blurRadius: 10,
                    offset: Offset(0, 3),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  Positioned(
                    top: 10,
                    right: 10,
                    child: IconButton(
                      icon: Icon(Icons.close),
                      onPressed: () {
                        setState(() {
                          showCenterModal = false;
                        });
                      },
                    ),
                  ),
                  // Future: Add your modal content here
                  Center(child: Text("Modal Content Goes Here")),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  } */

  Widget _buildPopup() {
    if (selectedItem == null || popupType == null) return SizedBox.shrink();

    return Container(
      width: 314,
      height: 531,
      margin: const EdgeInsets.only(left: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color(0x4ACDD7E8),
            blurRadius: 10,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            top: 1.5,
            right: 0,
            child: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                setState(() {
                  showPopup = false;
                  selectedItem = null;
                  popupType = null;
                });
              },
            ),
          ),
          popupType == 'Completed'
              ? _buildPopupDataCompleted()
              : _buildPopupDataPending(),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      color: const Color.fromRGBO(245, 245, 245, 1),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 22, right: 10.5),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    bool isSmallScreen = constraints.maxWidth < 500;
                    Widget tabSection = isSmallScreen
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Wrap(
                                spacing: 10,
                                runSpacing: 10,
                                children: [
                                  _buildTab("All"),
                                  _buildTab("Pending"),
                                  _buildTab("Completed"),
                                ],
                              ),
                              const SizedBox(height: 10),
                              Align(
                                alignment: Alignment.centerRight,
                                child: _buildPaginationControls(),
                              ),
                            ],
                          )
                        : Row(
                            children: [
                              Expanded(
                                child: Row(
                                  children: [
                                    _buildTab("All"),
                                    const SizedBox(width: 12),
                                    _buildTab("Pending"),
                                    const SizedBox(width: 12),
                                    _buildTab("Completed"),
                                  ],
                                ),
                              ),
                              AnimatedContainer(
                                duration: Duration(milliseconds: 0),
                                margin:
                                    EdgeInsets.only(right: showPopup ? 328 : 0),
                                child: _buildPaginationControls(),
                              ),
                            ],
                          );
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        tabSection,
                        const Divider(
                          color: Color(0xFFEDEDED),
                          thickness: 1.0,
                          height: 0.5,
                        ),
                      ],
                    );
                  },
                ),
              ),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.only(left: 30),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(child: _buildChartContent()),
                    if (showPopup && selectedItem != null) _buildPopup(),
                  ],
                ),
              ),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPopupDataCompleted() {
    return Stack(
      children: const [
        // Header Title
        Positioned(
          top: 13.5,
          left: 20,
          child: Text(
            'Transaction Details',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Color(0xFF606060),
              height: 1.25,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        // Divider
        Positioned(
          top: 44,
          left: 0,
          right: 0,
          child: Divider(
            color: Color(0xFFE4E4E4),
            thickness: 1,
            height: 0,
          ),
        ),
        // "Solution Name" (left side)
        Positioned(
          top: 52,
          left: 20,
          child: Text(
            'Solution Name',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Color(0xFFB3B3B3),
              height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        // "April 30, 2025" (right side)
        Positioned(
          top: 50,
          right: 16,
          child: Text(
            'April 30, 2025',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color.fromARGB(96, 96, 96, 1),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.right,
          ),
        ),
        // "Leave Management" (left side)
        Positioned(
          top: 67,
          left: 20,
          child: Text(
            'Leave Management',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color(0xFF3379FF),
              height: 1.25,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

// "3.30PM" (right side)
        Positioned(
          top: 67,
          right: 16,
          child: Text(
            '3.30PM',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color.fromARGB(96, 96, 96, 1),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.right,
          ),
        ),

        // "Task Name" label
        Positioned(
          top: 100,
          left: 20,
          child: Text(
            'Task Name',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Color(0xFFB3B3B3),
              height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

// "Leave Request" value
        Positioned(
          top: 115,
          left: 20,
          child: Text(
            'Leave Request',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color(0xFF606060),
              height: 1.25,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        // "Transaction ID" label
        Positioned(
          top: 145,
          left: 20,
          child: Text(
            'Transaction ID',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Color(0xFFB3B3B3),
              height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

// "987654321e-76543-2345-2" value
        Positioned(
          top: 160,
          left: 20,
          child: Text(
            '987654321e-76543-2345-2',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color(0xFF606060),
              height: 1.25,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Positioned(
          top: 190,
          left: 0,
          right: 0,
          child: Divider(
            color: Color(0xFFE4E4E4),
            thickness: 1,
            height: 0,
          ),
        ),
        // "Leave Request" text
        Positioned(
          top: 200,
          left: 20,
          child: Text(
            'Leave Request',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Color(0xFF000000),
              height: 1.25,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

// "Approved" button on the right
        Positioned(
          top: 200,
          right: 16,
          child: SizedBox(
            width: 70,
            height: 20,
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: Color.fromRGBO(
                    168, 252, 219, 1), // background color from CSS
                borderRadius:
                    BorderRadius.all(Radius.circular(13)), // border radius
              ),
              child: Center(
                child: Text(
                  'Approved',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 10,
                    fontWeight: FontWeight.w500, // medium
                    height: 1.2, // 12px/10px
                    letterSpacing: 0,
                    color: Color.fromRGBO(0, 139, 96, 1), // font color
                  ),
                ),
              ),
            ),
          ),
        ),

        Positioned(
          top: 235,
          left: 20,
          child: Text(
            'Reason',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500, // medium weight
              color: Color(0xFFB3B3B3),
              height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 250,
          left: 20,
          child: SizedBox(
            width: 270, // prevent overflow, adjust as needed
            child: Text(
              'Illness and health-related and Medical appointments',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(0xFF606060),
                height: 1.25,
                letterSpacing: 0,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ),
        Positioned(
          top: 300,
          left: 20,
          child: Text(
            'Leave Type',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500, // medium weight
              color: Color(0xFFB3B3B3),
              height: 1.2,
              letterSpacing: 0,
              // opacity 1 by default, no need to set explicitly
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 300,
          right: 90,
          child: Text(
            'Duration in days',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Color(0xFFB3B3B3),
              height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Positioned(
          top: 315,
          left: 20,
          child: Text(
            'Sick Leave',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600, // 600 weight
              color: Color(0xFF606060),
              height: 1.25,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 315,
          right: 90,
          child: Text(
            '1',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color(0xFF606060),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Positioned(
          top: 345,
          left: 20,
          child: Text(
            'Request ID',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500, // medium weight
              color: Color(0xFFB3B3B3),
              height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 345,
          right: 90,
          child: Text(
            'Employee ID',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500, // medium weight
              color: Color(0xFFB3B3B3),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Positioned(
          top: 360,
          left: 20,
          child: Text(
            '5432234',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600, // 600 weight
              color: Color(0xFF606060),
              height: 1.25,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 360,
          right: 90,
          child: Text(
            '5432-asy-234',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color(0xFF606060),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Positioned(
          top: 390,
          left: 20,
          child: Text(
            'Start Date',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500, // medium weight
              color: Color(0xFFB3B3B3),
              height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 390,
          right: 90,
          child: Text(
            'End Date',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Color(0xFFB3B3B3),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Positioned(
          top: 405,
          left: 20,
          child: Text(
            'April 13, 2025',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600, // semi-bold
              height: 1.25, // 15px line height / 12px font size
              color: Color(0xFF606060),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 405,
          right: 90,
          child: Text(
            'April 13, 2025',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              height: 1.25,
              color: Color(0xFF606060),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Positioned(
          top: 435,
          left: 0,
          right: 0,
          child: Divider(
            color: Color(0xFFE4E4E4),
            thickness: 1,
            height: 0,
          ),
        ),
        Positioned(
          top: 445,
          left: 20,
          child: Text(
            'Document',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.bold,
              height: 1.25, // 15px / 12px
              color: Colors.black,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 470,
          left: 20,
          child: Text(
            'Document ID',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500, // medium weight
              height: 1.2, // 12px / 10px
              color: Color(0xFFB3B3B3),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 483,
          left: 20,
          child: Text(
            '5432-asy-234-5432-asy-234',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w600,
              height: 1.25,
              color: Color(0xFF606060),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 505,
          left: 20,
          child: Text(
            'Request ID',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 10,
              fontWeight: FontWeight.w500,
              height: 1.2,
              color: Color(0xFFB3B3B3),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        // Add the rest of your Positioned widgets here (all the other UI content)
      ],
    );
  }

  Widget _buildPopupDataPending() {
    return Stack(
      children: [
        Positioned(
          top: 13.5,
          left: 20,
          child: Text(
            'Transaction Details',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Color(0xFF606060),
              height: 1.25,
            ),
          ),
        ),
        Positioned(
          top: 30,
          left: 0,
          right: 0,
          child: Divider(
            color: Color(0xFFE4E4E4),
            thickness: 1,
          ),
        ),

        Positioned(
          top: 50,
          left: 20,
          child: Text(
            'Workflow ID',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              // fontWeight: FontWeight.w500,
              color: Color(0xFFB3B3B3),
              height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 70,
          left: 20,
          child: Text(
            '987654321e-76543-2345-2',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Color(0xFF606060),
              //  height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),
        Positioned(
          top: 100,
          left: 20,
          child: Row(
            children: [
              SvgPicture.asset(
                'assets/images/dateTime.svg',
                width: 14,
                height: 16,
                //  color: Color(0xFFB3B3B3), // Optional: match text color
              ),
              SizedBox(width: 4), // Spacing
              Text(
                'Created',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 12,
                  color: Color(0xFFB3B3B3),
                  height: 1.2,
                  letterSpacing: 0,
                ),
                textAlign: TextAlign.left,
              ),
            ],
          ),
        ),

        Positioned(
          top: 120,
          left: 20,
          child: Text(
            'April 24, 2025',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 13,
              color: Color(0xFF606060),
              fontWeight: FontWeight.w600,
              height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 120,
          right: 16,
          child: Text(
            '11:30 AM',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Color(0xFF606060),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.right,
          ),
        ),

        Positioned(
          top: 150,
          left: 20,
          child: Row(
            children: [
              SvgPicture.asset(
                'assets/images/dateTime.svg',
                width: 14,
                height: 16,
                // color: Color(0xFFB3B3B3), // Optional: match text color
              ),
              SizedBox(width: 4), // Spacing
              Text(
                'Updated',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 12,
                  color: Color(0xFFB3B3B3),
                  height: 1.2,
                  letterSpacing: 0,
                ),
                textAlign: TextAlign.left,
              ),
            ],
          ),
        ),

        Positioned(
          top: 170,
          left: 20,
          child: Text(
            'April 13, 2025',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 13,
              color: Color(0xFF606060),
              fontWeight: FontWeight.w600,
              height: 1.2,
              letterSpacing: 0,
            ),
            textAlign: TextAlign.left,
          ),
        ),

        Positioned(
          top: 170,
          right: 16,
          child: Text(
            '3:30 PM',
            style: TextStyle(
              fontFamily: 'Inter',
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Color(0xFF606060),
              letterSpacing: 0,
            ),
            textAlign: TextAlign.right,
          ),
        ),

        Positioned(
          top: 190,
          left: 0,
          right: 0,
          child: Divider(
            color: Color(0xFFE4E4E4),
            thickness: 1,
          ),
        ),

        Positioned(
          top: 210,
          left: 20,
          child: Container(
            width: 276,
            height: 33,
            decoration: BoxDecoration(
              color: Color.fromRGBO(0, 88, 255, 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 8), // similar to CSS `left: 2`
            child: Text(
              'Total Local Objectives: 4',
              style: TextStyle(
                color: Color(0xFF000000),
                fontWeight: FontWeight.w500,
                fontSize: 12,
                letterSpacing: 0, // 'NaNpx' = default 0
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ),

        Positioned(
          top: 260,
          left: 20,
          child: Container(
            width: 276,
            height: 59,
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xFFE6F2FF)),
              // borderRadius: BorderRadius.circular(18), // rounded border
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 6,
                  left: 12,
                  child: Text(
                    'Leave Request',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      height: 1.2,
                      letterSpacing: 0,
                      color: Color(0xFF606060),
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Positioned(
                  top: 30,
                  left: 12,
                  child: Text(
                    'April 04, 2025 | 11:30 AM',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 12,
                      color: Color(0xFF606060),
                      letterSpacing: 0,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Positioned(
                  top: 28,
                  right: 12,
                  child: GestureDetector(
                    // <-- Make sure GestureDetector wraps it
                    onTap: widget.onOpenModal,
                    child: Container(
                      width: 68,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Color.fromRGBO(226, 254, 227, 1),
                        borderRadius: BorderRadius.all(Radius.circular(13)),
                      ),
                      child: Center(
                        child: Text(
                          'Completed',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontFamily: 'Inter',
                            fontSize: 10,
                            letterSpacing: 0,
                            color: Color(0xFF03A015),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        Positioned(
          top: 320,
          left: 20,
          child: Container(
            width: 276,
            height: 59,
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xFFE6F2FF)),
              //borderRadius: BorderRadius.circular(18),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 6,
                  left: 12,
                  child: Text(
                    'Leave Request',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      height: 1.2,
                      letterSpacing: 0,
                      color: Color(0xFF606060),
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Positioned(
                  top: 30,
                  left: 12,
                  child: Text(
                    'April 04, 2025 | 11:30 AM',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 12,
                      color: Color(0xFF606060),
                      letterSpacing: 0,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Positioned(
                  top: 28,
                  right: 12,
                  child: Container(
                    width: 68,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Color.fromRGBO(226, 254, 227, 1),
                      borderRadius: BorderRadius.all(Radius.circular(13)),
                    ),
                    child: Center(
                      child: Text(
                        'Completed',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          letterSpacing: 0,
                          color: Color(0xFF03A015),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        Positioned(
          top: 380,
          left: 20,
          child: Container(
            width: 276,
            height: 59,
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xFFE6F2FF)),
              //borderRadius: BorderRadius.circular(18),
            ),
            child: Stack(
              children: [
                Positioned(
                  top: 6,
                  left: 12,
                  child: Text(
                    'Leave Request',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0,
                      color: Color(0xFF606060),
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Positioned(
                  top: 30,
                  left: 12,
                  child: Text(
                    'April 04, 2025 | 11:30 AM',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontSize: 12,
                      letterSpacing: 0,
                      color: Color(0xFF606060),
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                Positioned(
                  top: 28,
                  right: 12,
                  child: Container(
                    width: 60,
                    height: 22,
                    decoration: BoxDecoration(
                      color:
                          Color.fromRGBO(238, 227, 202, 1), // yellow background
                      borderRadius: BorderRadius.all(Radius.circular(13)),
                    ),
                    child: Center(
                      child: Text(
                        'Pending',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          height: 1.2,
                          letterSpacing: 0,
                          color: Color(0xFFCE8F00),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        Positioned(
          top: 470,
          left: 20, // Add this if needed to match horizontal position
          child: Container(
            width: 276,
            height: 40,
            decoration: BoxDecoration(
              color: Color.fromRGBO(0, 88, 255, 1), // background color
              borderRadius: BorderRadius.circular(18), // border-radius: 18px
            ),
            alignment: Alignment.center, // center text
            child: Text(
              'Resume Transaction',
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: 13,
                fontWeight: FontWeight.w600, // font-weight: 600
                height: 17 / 14, // line-height: 17px
                letterSpacing: 0,
                color: Color(0xFFFFFFFF), // text color
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),

        // Add more Pending widgets here
      ],
    );
  }

/*   Widget _buildCenterModal() {
    return Positioned(
      top: 0,
      left: 353,
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: 660,
          height: 609,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(6),
            boxShadow: [
              BoxShadow(
                color: const Color(0x4ACDD7E8),
                blurRadius: 10,
                offset: Offset(0, 3),
              ),
            ],
          ),
          child: Stack(
            children: [
              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      showCenterModal = false;
                    });
                  },
                ),
              ),
              Center(
                child: Text(
                  "Center Modal Opened!",
                  style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  } */

  Widget _buildTab(String label) {
    final bool isSelected = selectedTab == label;
    return InkWell(
      onTap: () {
        setState(() {
          selectedTab = label;
          applyFilter();
        });
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? Colors.black : Colors.grey,
              ),
            ),
            Container(
              height: 0.5,
              width: 40,
              color: isSelected ? const Color(0xFF0057FF) : Colors.transparent,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaginationControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.filter_alt_outlined, size: 14, color: Colors.grey),
        const SizedBox(width: 5),
        const Text(
          "Filter",
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: 12,
            color: Color(0xFF000000),
          ),
        ),
        const SizedBox(width: 20),
        GestureDetector(
          onTap: prevPage,
          child: Icon(Icons.chevron_left,
              color: currentPage > 1 ? Colors.black : const Color(0xFFD0D0D0),
              size: 18),
        ),
        const SizedBox(width: 4),
        Text(
          "$currentPage | $totalPages",
          style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Color(0xFF242424)),
        ),
        const SizedBox(width: 4),
        GestureDetector(
          onTap: nextPage,
          child: Icon(Icons.chevron_right,
              color: currentPage < totalPages
                  ? Colors.black
                  : const Color(0xFFD0D0D0),
              size: 18),
        ),
      ],
    );
  }

  Widget _buildChartContent() {
    final ScrollController _scrollController = ScrollController();
    double spacing = showPopup ? 29 : 33; // Adjust spacing if popup is visible

    return Scrollbar(
      thumbVisibility: true,
      controller: _scrollController,
      child: SingleChildScrollView(
        controller: _scrollController,
        child: Wrap(
          spacing: spacing,
          runSpacing: 21,
          children: paginatedData
              .map((item) => GestureDetector(
                    onTap: () {
                      if (item.status == 'Completed' ||
                          item.status == 'Pending') {
                        setState(() {
                          showPopup = true;
                          selectedItem = item;
                          popupType = item.status;
                        });
                      }
                    },
                    child: _workflowBlock(item),
                  ))
              .toList(),
        ),
      ),
    );
  }
}

Widget _workflowBlock(WorkflowItem item) {
  return SizedBox(
    width: 292,
    height: 133,
    child: Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A000000),
            offset: Offset(0, 2),
            blurRadius: 0,
          ),
        ],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 18,
                height: 18,
                padding: const EdgeInsets.all(2),
                child: SvgPicture.asset('assets/images/usermanagement.svg'),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  item.title,
                  style: const TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 14,
                    height: 1.21,
                    fontWeight: FontWeight.w300,
                    color: Color(0xFF606060),
                  ),
                ),
              ),
            ],
          ),
          const Spacer(),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              SvgPicture.asset('assets/images/assignedtome.svg',
                  width: 8, height: 8),
              const SizedBox(width: 4),
              const Text(
                "Assigned to me",
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF606060),
                ),
              ),
            ],
          ),
          const Divider(thickness: 1, height: 0, color: Color(0xFFE4E4E4)),
          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text("Updated on:",
                        style:
                            TextStyle(fontSize: 10, color: Color(0xFFB3B3B3))),
                    Text(item.updatedOn,
                        style: const TextStyle(
                            fontSize: 11, color: Color(0xFF606060))),
                  ],
                ),
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 20,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Color(0xFFE4E4E4)),
                      ),
                      child: const Text("1Lo",
                          style: TextStyle(fontSize: 10, color: Colors.blue)),
                    ),
                    const SizedBox(width: 4),
                    Container(
                      width: 64,
                      height: 20,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: item.status == "Pending"
                            ? const Color(0xFFFEF7E2)
                            : const Color(0xFFE0F7E9),
                        borderRadius: BorderRadius.circular(13),
                      ),
                      child: Text(
                        item.status,
                        style: TextStyle(
                          fontSize: 10,
                          color: item.status == "Pending"
                              ? const Color.fromRGBO(206, 143, 0, 1)
                              : const Color.fromRGBO(0, 125, 80, 1),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
