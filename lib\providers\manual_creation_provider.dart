import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/models/role_info.dart';
import '../models/agent_manual_response_model.dart';
import '../models/agent_data.dart';
import '../models/workflow/entity_manual_response_model.dart';
import '../models/workflow/relation_ship_properties.dart';
import '../models/workflow/workflow_manual_response_model.dart';
import '../models/workflow/workflow_lo_response_model.dart';
import '../models/entities_data.dart' as entities_model;
import '../services/text_validation_service.dart';
import '../services/entity_validation_service.dart';
import '../services/workflow_validation_service.dart';
import '../services/workflow_lo_validation_service.dart';
import '../utils/logger.dart';

enum WorkflowStep {
  agentCreation,
  entityCreation,
  workflowCreation,
  workflowLoCreation
}

class ManualCreationProvider extends ChangeNotifier {
  late TextEditingController _textController;
  String? _hoveredIcon;

  // Workflow state
  WorkflowStep _currentStep = WorkflowStep.agentCreation;

  // Side panel state
  bool _showSidePanel = false;
  RoleInfo? _selectedRole;
  double _sidePanelWidth = 600.0;
  double _minSidePanelWidth = 300.0;
  double _maxSidePanelWidth = 700.0;

  // WorkflowLO side panel state
  bool _showWorkflowLoSidePanel = false;
  WorkFlowLoResponseModel? _selectedWorkflowLoData;

  // Agent validation state
  bool _isValidating = false;
  AgentModelResponseModel? _validationResult;
  String? _validationError;
  bool _showAgentTable = false;
  bool _showAgentTableForBook = false;
  AgentData? _extractedAgentData;

  // Entity validation state
  bool _isValidatingEntity = false;
  EntityModelResponseModel? _entityValidationResult;
  String? _entityValidationError;
  bool _showEntityTable = false;
  bool _showEntityTableForBook = false;
  entities_model.EntitiesData? _extractedEntityData;

  // Workflow validation state
  bool _isValidatingWorkflow = false;
  WorkFlowModelResponseModel? _workflowValidationResult;
  String? _workflowValidationError;
  bool _showWorkflowTable = false;
  bool _showWorkflowTableForBook = false;
  List<Map<String, dynamic>>? _extractedWorkflowData;

  // WorkflowLO validation state
  bool _isValidatingWorkflowLo = false;
  WorkFlowLoResponseModel? _workflowLoValidationResult;
  String? _workflowLoValidationError;
  bool _showWorkflowLoTable = false;
  List<Map<String, dynamic>>? _extractedWorkflowLoData;

  // Service instances
  final TextValidationService _validationService = TextValidationService();
  final EntityValidationService _entityValidationService =
      EntityValidationService();
  final WorkflowValidationService _workflowValidationService =
      WorkflowValidationService();
  final WorkflowLoValidationService _workflowLoValidationService =
      WorkflowLoValidationService();

  ManualCreationProvider() {
    _textController = TextEditingController();
  }

  intializeWidths(sidePanelWidth, minSidePanelWidth, maxSidePanelWidth) {
    _sidePanelWidth = sidePanelWidth;
    _minSidePanelWidth = minSidePanelWidth;
    _maxSidePanelWidth = maxSidePanelWidth;
  }

  // Getters
  TextEditingController get textController => _textController;
  String? get hoveredIcon => _hoveredIcon;

  // Workflow getters
  WorkflowStep get currentStep => _currentStep;

  // Agent validation getters
  bool get isValidating => _isValidating;
  AgentModelResponseModel? get validationResult => _validationResult;
  String? get validationError => _validationError;
  bool get showAgentTable => _showAgentTable;
  bool get showAgentTableForBook => _showAgentTableForBook;
  AgentData? get extractedAgentData => _extractedAgentData;

  // Entity validation getters
  bool get isValidatingEntity => _isValidatingEntity;
  EntityModelResponseModel? get entityValidationResult =>
      _entityValidationResult;
  String? get entityValidationError => _entityValidationError;
  bool get showEntityTable => _showEntityTable;
  bool get showEntityTableForBook => _showEntityTableForBook;
  entities_model.EntitiesData? get extractedEntityData => _extractedEntityData;

  // Workflow validation getters
  bool get isValidatingWorkflow => _isValidatingWorkflow;
  WorkFlowModelResponseModel? get workflowValidationResult =>
      _workflowValidationResult;
  String? get workflowValidationError => _workflowValidationError;
  bool get showWorkflowTable => _showWorkflowTable;
  bool get showWorkflowTableForBook => _showWorkflowTableForBook;
  List<Map<String, dynamic>>? get extractedWorkflowData =>
      _extractedWorkflowData;

  // WorkflowLO validation getters
  bool get isValidatingWorkflowLo => _isValidatingWorkflowLo;
  WorkFlowLoResponseModel? get workflowLoValidationResult =>
      _workflowLoValidationResult;
  String? get workflowLoValidationError => _workflowLoValidationError;
  bool get showWorkflowLoTable => _showWorkflowLoTable;
  List<Map<String, dynamic>>? get extractedWorkflowLoData =>
      _extractedWorkflowLoData;

// Side panel getters
  bool get showSidePanel => _showSidePanel;
  RoleInfo? get selectedRole => _selectedRole;
  double get sidePanelWidth => _sidePanelWidth;
  double get minSidePanelWidth => _minSidePanelWidth;
  double get maxSidePanelWidth => _maxSidePanelWidth;

  // WorkflowLO side panel getters
  bool get showWorkflowLoSidePanel => _showWorkflowLoSidePanel;
  WorkFlowLoResponseModel? get selectedWorkflowLoData =>
      _selectedWorkflowLoData;

  bool _showSideEntityPanel = false;
  bool get showSideEntityPanel => _showSideEntityPanel;

  List<Color> pastelColors = [
    Color(0xffBFEFFF),
    Color(0xffFFA07A),
    Color(0xffDFFF00),
    Color(0xffE0B0FF),
    Color(0xffB4A7D6),
    Color(0xffC7E9F1),
    Color(0xffC3CDE6),
    Color(0xffCCCCFF),
    Color(0xffE6E6FA),
    Color(0xffE4D0EC),
    Color(0xffD8BFD8),
    Color(0xffB0E0E6),
    Color(0xffF2D1E3),
    Color(0xffF49AC2),
    Color(0xffFFA6C9),
    Color(0xffFBB1BD),
    Color(0xffFFD1DC),
    Color(0xffFFDFDD),
    Color(0xffFFDAB9),
    Color(0xffFDC5B5),
    Color(0xffD8B7DD),
    Color(0xffFBCEB1),
    Color(0xffFFD8B1),
    Color(0xffF7BCAC),
    Color(0xffFFE5B4),
    Color(0xffFFFACD),
    Color(0xffFFFACD),
    Color(0xffFAFAD2),
    Color(0xffFFFDD0),
    Color(0xffF5F5DC),
    Color(0xffAAF0D1),
    Color(0xffAAF0D1),
    Color(0xffF0FFF0),
    Color(0xffB0EACD),
    Color(0xffACE1AF),
    Color(0xffD0F0C0),
    Color(0xffADDFAD),
    Color(0xffA8E4A0),
    Color(0xffD5F5B8),
    Color(0xffCDE5B2),
    Color(0xffECEBBD),
    Color(0xffB2FFFF),
    Color(0xffAFEEEE),
    Color(0xffF5FFFA),
    Color(0xffA7D8C9),
    Color(0xff7FFFD4),
    Color(0xffCFFFE5),
    Color(0xffD4F1F9),
    Color(0xffD6F1FF),
    Color(0xffAEC6CF),
  ];

  entities_model.Entity? _selectedEntity;

  entities_model.Entity? get selectedEntity => _selectedEntity;

  // User data getter
  List<User>? get extractedUsers {
    if (_validationResult?.parsedUsers != null) {
      return _validationResult!.parsedUsers!.values.toList();
    }
    return null;
  }

  // Get linear workflow data for linear tree visualization
  Map<String, dynamic>? getLinearWorkflowData() {
    Logger.info('🌳 Getting linear workflow data...');

    if (_extractedWorkflowData == null || _extractedWorkflowData!.isEmpty) {
      Logger.info('🌳 No extracted workflow data available for linear display');
      return null;
    }

    try {
      // Use the first workflow data
      final workflowData = _extractedWorkflowData!.first;
      final workFlowDetails = workflowData['workFlowDetails'];

      Logger.info('🌳 Processing workflow data for linear display...');

      // Extract basic workflow information from API response
      String title = _extractWorkflowTitle(workFlowDetails);
      String subtitle = _extractWorkflowSubtitle(workFlowDetails);

      // Extract sequential steps from process flow
      List<String> sequentialSteps = _extractSequentialSteps(workFlowDetails);

      // Extract hierarchical breakdown
      List<Map<String, dynamic>> hierarchicalSteps =
          _extractHierarchicalSteps(workFlowDetails);

      Map<String, dynamic> linearData = {
        'title': title,
        'subtitle': subtitle,
        'sequentialSteps': sequentialSteps,
        'hierarchicalSteps': hierarchicalSteps,
      };

      Logger.info(
          '🌳 Generated linear workflow data with ${sequentialSteps.length} sequential steps and ${hierarchicalSteps.length} hierarchical steps');

      // Debug log the hierarchical structure
      _logHierarchicalStructure(hierarchicalSteps);

      return linearData;
    } catch (e) {
      Logger.error('🌳 Error creating linear workflow data: $e');
      return null;
    }
  }

  // Helper method to extract sequential steps for the one-line display
  List<String> _extractSequentialSteps(dynamic workFlowDetails) {
    List<String> steps = [];

    try {
      // Extract from process flow data
      if (workFlowDetails?.processFlow != null &&
          workFlowDetails.processFlow.isNotEmpty) {
        for (ProcessFlow flow in workFlowDetails.processFlow) {
          if (flow.loName != null && flow.loName!.isNotEmpty) {
            steps.add(flow.loName!);
          }
        }
        Logger.info('🌳 Extracted ${steps.length} steps from processFlow');
      }

      // Return empty list if no data found
      if (steps.isEmpty) {
        Logger.warning('🌳 No sequential steps found in process flow data');
      }
    } catch (e) {
      Logger.error('Error extracting sequential steps: $e');
    }

    return steps;
  }

  // Helper method to extract hierarchical steps for the tree breakdown
  List<Map<String, dynamic>> _extractHierarchicalSteps(
      dynamic workFlowDetails) {
    List<Map<String, dynamic>> hierarchicalSteps = [];

    try {
      // Build workflow hierarchy from process flow data
      hierarchicalSteps = _buildProcessFlowHierarchy(workFlowDetails);
      Logger.info(
          '🌳 Built process flow hierarchy with ${hierarchicalSteps.length} root steps');

      // Return empty list if no data found
      if (hierarchicalSteps.isEmpty) {
        Logger.warning('🌳 No hierarchical steps found in process flow data');
      }
    } catch (e) {
      Logger.error('Error extracting hierarchical steps: $e');
    }

    return hierarchicalSteps;
  }

  // Build workflow hierarchy from process flow data
  List<Map<String, dynamic>> _buildProcessFlowHierarchy(
      dynamic workFlowDetails) {
    List<Map<String, dynamic>> hierarchy = [];

    try {
      // Create root node from Global Objectives
      String goTitle =
          workFlowDetails?.globalObjectives?.name ?? 'Global Objective';

      Map<String, dynamic> goNode = {
        'level': 0,
        'text': goTitle,
        'flowType': 'root',
        'children': <Map<String, dynamic>>[],
      };

      // Build workflow tree from process flow data
      if (workFlowDetails?.processFlow != null &&
          workFlowDetails.processFlow.isNotEmpty) {
        List<Map<String, dynamic>> processFlowChildren =
            _buildProcessFlowTree(workFlowDetails.processFlow);
        goNode['children'] = processFlowChildren;
      }

      hierarchy.add(goNode);
    } catch (e) {
      Logger.error('Error building process flow hierarchy: $e');
    }

    return hierarchy;
  }

  // Build process flow tree from process flow data
  List<Map<String, dynamic>> _buildProcessFlowTree(
      List<ProcessFlow> processFlow) {
    List<Map<String, dynamic>> treeNodes = [];
    Map<String, Map<String, dynamic>> nodeMap = {};

    try {
      // First pass: Create all nodes
      for (ProcessFlow flow in processFlow) {
        String nodeId = flow.id ?? flow.loId ?? 'unknown';
        String nodeName = flow.loName ?? 'Unknown Node';
        String routeType = flow.routeType ?? 'Unknown';

        Map<String, dynamic> node = {
          'id': nodeId,
          'level': 1,
          'text': nodeName,
          'flowType': _getFlowTypeFromRouteType(routeType),
          'routeType': routeType,
          'actorType': flow.actorType ?? '',
          'description': flow.description ?? '',
          'children': <Map<String, dynamic>>[],
          'routes': <String>[],
          'conditions': <Map<String, dynamic>>[],
          'parallelRoutes': <Map<String, dynamic>>[],
          'joinAt': flow.joinAt ?? '',
        };

        // Store routing information based on route type
        if (flow.conditions != null && flow.conditions!.isNotEmpty) {
          for (var condition in flow.conditions!) {
            node['conditions'].add({
              'condition': condition.condition ?? '',
              'routeTo': condition.routeTo ?? '',
            });
          }
        }

        if (flow.routes != null && flow.routes!.isNotEmpty) {
          node['routes'] = List<String>.from(flow.routes!);
        }

        if (flow.parallelRoutes != null && flow.parallelRoutes!.isNotEmpty) {
          for (var parallelRoute in flow.parallelRoutes!) {
            node['parallelRoutes'].add({
              'routeTo': parallelRoute.routeTo ?? '',
              'description': parallelRoute.description ?? '',
            });
          }
        }

        nodeMap[nodeId] = node;
      }

      // Second pass: Build tree structure based on routing
      for (var node in nodeMap.values) {
        List<Map<String, dynamic>> children = _buildNodeChildren(node, nodeMap);
        node['children'] = children;
      }

      // Find root nodes (nodes that are not referenced by others)
      Set<String> referencedNodes = {};
      for (var node in nodeMap.values) {
        // Add all nodes that are referenced by routes
        for (String route in node['routes']) {
          referencedNodes.add(route);
        }
        // Add all nodes referenced by conditions
        for (var condition in node['conditions']) {
          if (condition['routeTo'] != null && condition['routeTo'].isNotEmpty) {
            referencedNodes.add(condition['routeTo']);
          }
        }
        // Add all nodes referenced by parallel routes
        for (var parallelRoute in node['parallelRoutes']) {
          if (parallelRoute['routeTo'] != null &&
              parallelRoute['routeTo'].isNotEmpty) {
            referencedNodes.add(parallelRoute['routeTo']);
          }
        }
      }

      // Root nodes are those not referenced by others
      for (var node in nodeMap.values) {
        if (!referencedNodes.contains(node['id'])) {
          treeNodes.add(node);
        }
      }

      Logger.info(
          '🌳 Built process flow tree with ${treeNodes.length} root nodes');
    } catch (e) {
      Logger.error('Error building process flow tree: $e');
    }

    return treeNodes;
  }

  // Helper method to get flow type from route type
  String _getFlowTypeFromRouteType(String routeType) {
    switch (routeType.toLowerCase()) {
      case 'alternate':
        return 'conditional';
      case 'sequential':
        return 'sequential';
      case 'parallel':
        return 'parallel';
      default:
        return 'unknown';
    }
  }

  // Build children for a node based on its routing information
  List<Map<String, dynamic>> _buildNodeChildren(
      Map<String, dynamic> node, Map<String, Map<String, dynamic>> nodeMap) {
    List<Map<String, dynamic>> children = [];

    try {
      String routeType = node['routeType'] ?? '';

      switch (routeType.toLowerCase()) {
        case 'alternate':
          // Handle conditional routing
          for (var condition in node['conditions']) {
            String routeTo = condition['routeTo'] ?? '';
            if (routeTo.isNotEmpty && nodeMap.containsKey(routeTo)) {
              Map<String, dynamic> childNode = Map.from(nodeMap[routeTo]!);
              childNode['conditionText'] = condition['condition'] ?? '';
              children.add(childNode);
            }
          }
          break;

        case 'sequential':
          // Handle sequential routing
          for (String route in node['routes']) {
            if (route.isNotEmpty && nodeMap.containsKey(route)) {
              children.add(Map.from(nodeMap[route]!));
            }
          }
          break;

        case 'parallel':
          // Handle parallel routing
          for (var parallelRoute in node['parallelRoutes']) {
            String routeTo = parallelRoute['routeTo'] ?? '';
            if (routeTo.isNotEmpty && nodeMap.containsKey(routeTo)) {
              Map<String, dynamic> childNode = Map.from(nodeMap[routeTo]!);
              childNode['flowType'] = 'parallel_item';
              childNode['parallelDescription'] =
                  parallelRoute['description'] ?? '';
              children.add(childNode);
            }
          }
          break;
      }
    } catch (e) {
      Logger.error('Error building node children: $e');
    }

    return children;
  }

  // Debug method to log hierarchical structure
  void _logHierarchicalStructure(List<Map<String, dynamic>> hierarchicalSteps,
      [int depth = 0]) {
    for (var step in hierarchicalSteps) {
      String indent = '  ' * depth;
      String flowType = step['flowType'] ?? '';
      String text = step['text'] ?? '';
      String routeType = step['routeType'] ?? '';
      Logger.info('🌳 $indent[$flowType/$routeType] $text');

      // Log routing information
      if (step['conditions'] != null &&
          (step['conditions'] as List).isNotEmpty) {
        for (var condition in step['conditions']) {
          Logger.info(
              '🌳 $indent  → Condition: ${condition['condition']} → ${condition['routeTo']}');
        }
      }
      if (step['routes'] != null && (step['routes'] as List).isNotEmpty) {
        for (var route in step['routes']) {
          Logger.info('🌳 $indent  → Route: $route');
        }
      }
      if (step['parallelRoutes'] != null &&
          (step['parallelRoutes'] as List).isNotEmpty) {
        for (var parallelRoute in step['parallelRoutes']) {
          Logger.info(
              '🌳 $indent  → Parallel: ${parallelRoute['description']} → ${parallelRoute['routeTo']}');
        }
      }

      List<Map<String, dynamic>> children = step['children'] ?? [];
      if (children.isNotEmpty) {
        _logHierarchicalStructure(children, depth + 1);
      }
    }
  }

  // Helper method to extract workflow title from API response
  String _extractWorkflowTitle(dynamic workFlowDetails) {
    try {
      // Try to get title from global objectives
      if (workFlowDetails?.globalObjectives?.name != null &&
          workFlowDetails.globalObjectives.name.isNotEmpty) {
        return workFlowDetails.globalObjectives.name;
      }

      // Return empty string if no title found - no fallback
      return '';
    } catch (e) {
      Logger.error('Error extracting workflow title: $e');
      return '';
    }
  }

  // Helper method to extract workflow subtitle from API response
  String _extractWorkflowSubtitle(dynamic workFlowDetails) {
    try {
      // Try to get description from global objectives
      if (workFlowDetails?.globalObjectives?.description != null &&
          workFlowDetails.globalObjectives.description.isNotEmpty) {
        return workFlowDetails.globalObjectives.description;
      }

      // Try to get primary entity as subtitle
      if (workFlowDetails?.globalObjectives?.primaryEntity != null &&
          workFlowDetails.globalObjectives.primaryEntity.isNotEmpty) {
        return 'Process for ${workFlowDetails.globalObjectives.primaryEntity}';
      }

      // Try to get trigger condition as subtitle
      if (workFlowDetails?.triggerDefinition?.triggerCondition != null &&
          workFlowDetails.triggerDefinition.triggerCondition.isNotEmpty) {
        return workFlowDetails.triggerDefinition.triggerCondition;
      }

      // Return empty string if no subtitle found - no fallback
      return '';
    } catch (e) {
      Logger.error('Error extracting workflow subtitle: $e');
      return '';
    }
  }

  // Methods
  void setHoveredIcon(String? iconName) {
    _hoveredIcon = iconName;
    notifyListeners();
  }

  void clearHoveredIcon() {
    _hoveredIcon = null;
    notifyListeners();
  }

  void clearText() {
    _textController.clear();
    _selectedRole = null;
    _showSidePanel = false;
    clearValidationResults();
    notifyListeners();
  }

  void clearAgentSection() {
    _textController.clear();
    _validationResult = null;
    _validationError = null;
    _isValidating = false;
    _showAgentTable = false;
    _extractedAgentData = null;
    _selectedRole = null;
    _showSidePanel = false;
    notifyListeners();
  }

  void clearEntitySection() {
    _textController.clear();
    _entityValidationResult = null;
    _entityValidationError = null;
    _isValidatingEntity = false;
    _showEntityTable = false; // ← This is crucial!
    _extractedEntityData = null; // ← This too!
    _showSideEntityPanel = false;
    _selectedEntity = null;
    notifyListeners();
  }

  void clearWorkflowSection() {
    _textController.clear();
    _workflowValidationResult = null;
    _workflowValidationError = null;
    _isValidatingWorkflow = false;
    _showWorkflowTable = false;
    _extractedWorkflowData = null;
    // _selectedRole = null;
    // _showSidePanel = false;
    notifyListeners();
  }

  void clearWorkflowLoSection() {
    _textController.clear();
    _workflowLoValidationResult = null;
    _workflowLoValidationError = null;
    _isValidatingWorkflowLo = false;
    _showWorkflowLoTable = false; // Hide workflowLO table
    _extractedWorkflowLoData = null; // Clear workflowLO response data
    _showWorkflowLoSidePanel = false; // Hide workflowLO side panel
    _selectedWorkflowLoData = null; // Clear selected workflowLO data
    notifyListeners();
  }

  void clearValidationResults() {
    _validationResult = null;
    _validationError = null;
    _isValidating = false;
    _showAgentTable = false;
    _extractedAgentData = null;
    _showSidePanel = false;
    _showEntityTable = false;
    _showSideEntityPanel = false;
    _extractedEntityData = null;
    _showWorkflowTable = false;
    _extractedWorkflowData = null;
    _showWorkflowLoTable = false;
    _extractedWorkflowLoData = null;
    _showWorkflowLoSidePanel = false;
  }

  void clearEntityValidationResults() {
    _entityValidationResult = null;
    _entityValidationError = null;
    _isValidatingEntity = false;
    _showEntityTable = false;
    _extractedEntityData = null;
  }

  void clearWorkflowValidationResults() {
    _workflowValidationResult = null;
    _workflowValidationError = null;
    _isValidatingWorkflow = false;
    _showWorkflowTable = false;
    _extractedWorkflowData = null;
  }

  void clearWorkflowLoValidationResults() {
    _workflowLoValidationResult = null;
    _workflowLoValidationError = null;
    _isValidatingWorkflowLo = false;
    _showWorkflowLoTable = false;
    _extractedWorkflowLoData = null;
  }

  // Workflow navigation methods
  void goToNextStep() {
    // Close all side panels when navigating to next step
    closeAllSidePanels();
    
    if (_currentStep == WorkflowStep.agentCreation) {
      _currentStep = WorkflowStep.entityCreation;
      // Clear text field for entity input
      _textController.clear();
      notifyListeners();
    } else if (_currentStep == WorkflowStep.entityCreation) {
      _currentStep = WorkflowStep.workflowCreation;
      // Clear text field for workflow input
      _textController.clear();
      notifyListeners();
    } else if (_currentStep == WorkflowStep.workflowCreation) {
      _currentStep = WorkflowStep.workflowLoCreation;
      // Clear text field for workflowLO input
      _textController.clear();
      notifyListeners();
    }
  }

  void goToPreviousStep() {
    // Close all side panels when navigating to previous step
    closeAllSidePanels();
    
    if (_currentStep == WorkflowStep.entityCreation) {
      _currentStep = WorkflowStep.agentCreation;
      // Clear entity validation results
      clearEntityValidationResults();
      notifyListeners();
    } else if (_currentStep == WorkflowStep.workflowCreation) {
      _currentStep = WorkflowStep.entityCreation;
      // Clear workflow validation results
      clearWorkflowValidationResults();
      notifyListeners();
    } else if (_currentStep == WorkflowStep.workflowLoCreation) {
      _currentStep = WorkflowStep.workflowCreation;
      // Clear workflowLO validation results
      clearWorkflowLoValidationResults();
      notifyListeners();
    }
  }

  void resetWorkflow() {
    _currentStep = WorkflowStep.agentCreation;
    _textController.clear();
    clearValidationResults();
    clearEntityValidationResults();
    clearWorkflowValidationResults();
    clearWorkflowLoValidationResults();
    notifyListeners();
  }

  void handleAgentsTap() {
    // Close all side panels when navigating to agents
    closeAllSidePanels();
    
    // Handle agents tap logic
    _currentStep = WorkflowStep.agentCreation;
    notifyListeners();
  }

  void handleDataSetsTap() {
    // Close all side panels when navigating to datasets
    closeAllSidePanels();
    
    // Handle datasets tap logic
    _currentStep = WorkflowStep.entityCreation;
    notifyListeners();
  }

  void handleWorkflowsTap() {
    // Close all side panels when navigating to workflows
    closeAllSidePanels();
    
    // Handle workflows tap logic
    _currentStep = WorkflowStep.workflowCreation;
    notifyListeners();
  }

  void handleWorkflowsloTap() {
    // Close all side panels when navigating to workflow LO
    closeAllSidePanels();
    
    // Handle workflows tap logic
    _currentStep = WorkflowStep.workflowLoCreation;
    notifyListeners();
  }

  // Side panel management methods
  void showRoleDetailsPanel(RoleInfo role) {
    _selectedRole = role;
    _showSidePanel = true;
    notifyListeners();
  }

  void showEntityDetailsPanel(entities_model.Entity entity) {
    _selectedEntity = entity;
    _showSidePanel = false;
    _showSideEntityPanel = true;
    notifyListeners();
  }

  void hideSidePanel() {
    _showSidePanel = false;
    _selectedRole = null;
    notifyListeners();
  }

  void updateSidePanelWidth(double width) {
    if (width >= _minSidePanelWidth && width <= _maxSidePanelWidth) {
      _sidePanelWidth = width;
      notifyListeners();
    }
  }

  // WorkflowLO side panel management methods
  void showWorkflowLoDetailsPanel(WorkFlowLoResponseModel workflowLoData) {
    _selectedWorkflowLoData = workflowLoData;
    _showWorkflowLoSidePanel = true;
    notifyListeners();
  }

  void hideWorkflowLoSidePanel() {
    _showWorkflowLoSidePanel = false;
    _selectedWorkflowLoData = null;
    notifyListeners();
  }

  void hideEntitySidePanel() {
    _showSideEntityPanel = false;
    _selectedEntity = null;
    notifyListeners();
  }

  // Centralized method to close all side panels
  void closeAllSidePanels() {
    _showSidePanel = false;
    _showSideEntityPanel = false;
    _showWorkflowLoSidePanel = false;
    _selectedRole = null;
    _selectedEntity = null;
    _selectedWorkflowLoData = null;
    notifyListeners();
  }

  void handleValidate() async {
    // Handle validate action based on current step
    if (_currentStep == WorkflowStep.agentCreation) {
      await _handleAgentValidation();
    } else if (_currentStep == WorkflowStep.entityCreation) {
      await _handleEntityValidation();
    } else if (_currentStep == WorkflowStep.workflowCreation) {
      await _handleWorkflowValidation();
    } else if (_currentStep == WorkflowStep.workflowLoCreation) {
      await _handleWorkflowLoValidation();
    }
  }

  Future<void> _handleAgentValidation() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      _validationError = 'Please enter some text to validate';
      notifyListeners();
      return;
    }

    // Clear previous results
    _validationError = null;
    _validationResult = null;
    _isValidating = true;
    notifyListeners();

    try {
      Logger.info('Starting agent text validation...');
      final result = await _validationService.validateText(text);

      _validationResult = result;
      _isValidating = false;
      // _showAgentTable = true;
      // _extractedAgentData = _extractAgentDataFromResponse(result);

      // Check if validation was successful (no validation errors)
      //result.validationErrors == null || result.validationErrors!.isEmpty ||
      if ((result.success ?? false)) {
        _showAgentTable = true;
        _extractedAgentData = _extractAgentDataFromResponse(result);
        Logger.info(
            'Agent text validation completed successfully - showing agent table');
      }

      Logger.info('Agent text validation completed successfully');
      notifyListeners();
    } catch (e) {
      _validationError = e.toString().replaceFirst('Exception: ', '');
      _isValidating = false;
      Logger.error('Agent text validation failed: $e');
      notifyListeners();
    }
  }

  Future<void> handleAgentValidationForBook() async {
    try {
      Logger.info('Starting agent text validation...');
      final result = AgentModelResponseModel.fromJson(jsonDecode(
          await rootBundle.loadString(
              'assets/data/manual_validation/agents_manual_response.json')));
      _validationResult = result;
      _isValidating = false;
      _showAgentTableForBook = true;

      _showEntityTableForBook = false;
      _showWorkflowTable = false;
      _extractedAgentData = _extractAgentDataFromResponse(result);

      Logger.info('Agent text validation completed successfully');
      notifyListeners();
    } catch (e) {
      notifyListeners();
    }
  }

  Future<void> _handleEntityValidation() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      _entityValidationError = 'Please enter some text to validate';
      notifyListeners();
      return;
    }

    // Clear previous results
    _entityValidationError = null;
    _entityValidationResult = null;
    _isValidatingEntity = true;
    notifyListeners();

    try {
      Logger.info('Starting entity text validation...');
      final result = await _entityValidationService.validateText(text);

      _entityValidationResult = result;
      _isValidatingEntity = false;

      // Check if validation was successful
      if (result.success == true) {
        _showEntityTable = true;
        _extractedEntityData = _extractEntityDataFromResponse(result);
        Logger.info(
            'Entity text validation completed successfully - showing entity table');
      }

      Logger.info('Entity text validation completed successfully');
      notifyListeners();
    } catch (e) {
      _entityValidationError = e.toString().replaceFirst('Exception: ', '');
      _isValidatingEntity = false;
      Logger.error('Entity text validation failed: $e');
      notifyListeners();
    }
  }

  Future<void> handleEntityValidationForBook() async {
    try {
      Logger.info('Starting entity text validation...');
      final result = EntityModelResponseModel.fromJson(jsonDecode(
          await rootBundle.loadString(
              'assets/data/manual_validation/entity_manual_response.json')));
      _entityValidationResult = result;
      _isValidatingEntity = false;
      _showAgentTableForBook = false;
      _showEntityTableForBook = true;
      _showWorkflowTable = false;
      _extractedEntityData = _extractEntityDataFromResponse(result);
      Logger.info('Entity text validation completed successfully');
      notifyListeners();
    } catch (e) {
      notifyListeners();
    }
  }

  Future<void> _handleWorkflowValidation() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      _workflowValidationError = 'Please enter some text to validate';
      notifyListeners();
      return;
    }

    // Clear previous results
    _workflowValidationError = null;
    _workflowValidationResult = null;
    _isValidatingWorkflow = true;
    notifyListeners();

    try {
      Logger.info('Starting workflow text validation...');
      final result = await _workflowValidationService.validateText(text);

      _workflowValidationResult = result;
      _isValidatingWorkflow = false;

      // Check if validation was successful
      if (result.success == true) {
        _showWorkflowTable = true;
        _extractedWorkflowData = _extractWorkflowDataFromResponse(result);
        Logger.info(
            'Workflow text validation completed successfully - showing workflow table');
        Logger.info('🌳 Workflow validation success: ${result.success}');
        Logger.info(
            '🌳 Extracted workflow data count: ${_extractedWorkflowData?.length ?? 0}');
      } else {
        Logger.info(
            '🌳 Workflow validation failed - success: ${result.success}');
        // Let's still try to extract data and show the table for debugging
        // _showWorkflowTable = true;
        // _extractedWorkflowData = _extractWorkflowDataFromResponse(result);
        Logger.info(
            '🌳 Extracted workflow data count (despite failure): ${_extractedWorkflowData?.length ?? 0}');
      }

      Logger.info('Workflow text validation completed successfully');
      notifyListeners();
    } catch (e) {
      _workflowValidationError = e.toString().replaceFirst('Exception: ', '');
      _isValidatingWorkflow = false;
      Logger.error('Workflow text validation failed: $e');
      notifyListeners();
    }
  }

  Future<void> handleWorkflowValidationForBook() async {
    try {
      Logger.info('Starting workflow text validation...');
      final result = WorkFlowModelResponseModel.fromJson(jsonDecode(
          await rootBundle.loadString(
              'assets/data/manual_validation/workflow_manual_response.json')));
      _workflowValidationResult = result;
      _isValidatingWorkflow = false;
      _showAgentTableForBook = false;
      _showEntityTableForBook = false;
      _showWorkflowTableForBook = true;
      _extractedWorkflowData = _extractWorkflowDataFromResponse(result);
      Logger.info('Workflow text validation completed successfully');
      notifyListeners();
    } catch (e) {
      notifyListeners();
    }
  }

  Future<void> _handleWorkflowLoValidation() async {
    final text = _textController.text.trim();
    if (text.isEmpty) {
      _workflowLoValidationError = 'Please enter some text to validate';
      notifyListeners();
      return;
    }

    // Clear previous results
    _workflowLoValidationError = null;
    _workflowLoValidationResult = null;
    _isValidatingWorkflowLo = true;
    notifyListeners();

    try {
      Logger.info('Starting workflow LO text validation...');
      final result = await _workflowLoValidationService.validateText(text);

      _workflowLoValidationResult = result;
      _isValidatingWorkflowLo = false;

      // Check if validation was successful
      if (result.success == true) {
        _showWorkflowLoTable = true;
        _extractedWorkflowLoData = _extractWorkflowLoDataFromResponse(result);
        // Show the WorkflowLO side panel with the validation result
        showWorkflowLoDetailsPanel(result);
        Logger.info(
            'Workflow LO text validation completed successfully - showing workflow LO table and side panel');
      } else {
        // Still show table for debugging even if validation failed
        // _showWorkflowLoTable = true;
        // _extractedWorkflowLoData = _extractWorkflowLoDataFromResponse(result);
        // showWorkflowLoDetailsPanel(result);
        Logger.info('Workflow LO text validation completed with warnings');
      }

      Logger.info('Workflow LO text validation completed successfully');
      notifyListeners();
    } catch (e) {
      _workflowLoValidationError = e.toString().replaceFirst('Exception: ', '');
      _isValidatingWorkflowLo = false;
      Logger.error('Workflow LO text validation failed: $e');
      notifyListeners();
    }
  }

  // Extract agent data from validation response
  AgentData _extractAgentDataFromResponse(AgentModelResponseModel response) {
    try {
      Logger.info('🔍 Extracting agent data from validation response');

      // Create a new AgentData object
      AgentData agentData = AgentData();
      List<AgentInfo> agentInfoList = [];

      // Check if we have parsed roles data
      if (response.parsedRoles != null) {
        Logger.info('🔍 Processing parsed roles data');

        final roles = response.parsedRoles!;

        // Process each role type
        roles.forEach((roleType, role) {
          _processRole(role, roleType, agentInfoList);
        });
      }

      // Create system info
      AgentSystemInfo systemInfo = AgentSystemInfo(
        agentCount: agentInfoList.length,
        bulletPoints: [
          "Agent data extracted from validation response",
          "Contains ${agentInfoList.length} roles",
          "Processed from manual text validation"
        ],
        headerText: "Roles and Use Cases",
        systemVersions: ['1.0'],
        selectedVersion: '1.0',
      );

      // Update the AgentData object
      agentData = AgentData(
        agents: agentInfoList,
        systemInfo: systemInfo,
      );

      Logger.info(
          '🔍 Successfully extracted agent data with ${agentInfoList.length} roles');
      return agentData;
    } catch (e) {
      Logger.error('Error extracting agent data from response: $e');
      return AgentData();
    }
  }

  // Helper method to process individual role
  void _processRole(
      Employee? role, String roleType, List<AgentInfo> agentInfoList) {
    if (role != null) {
      Logger.info('🔍 Processing $roleType role');

      // Create sections for the role
      List<AgentInfoSection> sections = [];

      // Core Responsibilities section
      if (role.coreResponsibilities != null &&
          role.coreResponsibilities!.isNotEmpty) {
        sections.add(AgentInfoSection(
          id: 'responsibilities_${roleType.toLowerCase().replaceAll(' ', '_')}',
          title: 'Core Responsibilities',
          abbreviation: 'CR',
          items: role.coreResponsibilities!
              .map(
                (e) => e.naturalLanguage,
              )
              .toList(),
        ));
      }

      // KPIs section
      if (role.kpis != null && role.kpis!.isNotEmpty) {
        sections.add(AgentInfoSection(
          id: 'kpis_${roleType.toLowerCase().replaceAll(' ', '_')}',
          title: 'Key Performance Indicators',
          abbreviation: 'KPI',
          items: role.kpis!,
        ));
      }

      // Decision Authority section
      if (role.decisionAuthority != null &&
          role.decisionAuthority!.isNotEmpty) {
        sections.add(AgentInfoSection(
          id: 'authority_${roleType.toLowerCase().replaceAll(' ', '_')}',
          title: 'Decision Authority',
          abbreviation: 'DA',
          items: role.decisionAuthority!
              .map(
                (e) => e.naturalLanguage,
              )
              .toList(),
        ));
      }

      String description = "is in";

      if (role.department != null) {
        description += " ${role.department} Department";
      }

      if (role.parentRole != null) {
        description += ", inherits  ${role.parentRole}";
      }

      if (role.reportsTo != null) {
        description += ", reports to  ${role.reportsTo}";
      }

      // Create AgentInfo for this role
      AgentInfo agentInfo = AgentInfo(
        id: 'role_${roleType.toLowerCase().replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}',
        title: role.name ?? roleType,
        description: description,
        // 'Department: ${role.department ?? 'N/A'}, Team: ${role.team ?? 'N/A'}',
        version: '1.0',
        createdBy: 'System',
        createdDate: DateTime.now(),
        modifiedBy: 'System',
        modifiedDate: DateTime.now(),
        sections: sections,
      );

      agentInfoList.add(agentInfo);
      Logger.info('🔍 Added $roleType role to agent list');
    }
  }

  // Extract entity data from validation response
  entities_model.EntitiesData _extractEntityDataFromResponse(
      EntityModelResponseModel response) {
    try {
      Logger.info('🔍 Extracting entity data from validation response');

      // Create entity groups list
      List<entities_model.EntityGroup> entityGroups = [];

      // Check if we have parsed entities data
      if (response.parsedData.entities != null) {
        Logger.info('🔍 Processing parsed entities data');

        final entities = response.parsedData.entities!;

        entities.forEach((key, value) {
          print('$key: $value');
          _processEntityToGroup(value, key, entityGroups);
        });
        // Process each entity type

        // _processEntityToGroup(entities.customer, 'Customer', entityGroups);
        // _processEntityToGroup(entities.collateral, 'Collateral', entityGroups);
        // _processEntityToGroup(entities.payment, 'Payment', entityGroups);
      }

      // Create system info
      entities_model.SystemInfo systemInfo = entities_model.SystemInfo(
        entityCount: entityGroups.length,
        bulletPoints: [
          "Entity data extracted from validation response",
          "Contains ${entityGroups.length} entity types",
          "Processed from manual text validation"
        ],
        headerText: "Entities and Relationships",
        enitiyVersions: ['1.0'],
      );

      // Create EntitiesData object
      entities_model.EntitiesData entityData = entities_model.EntitiesData(
        entityGroups: entityGroups,
        systemInfo: systemInfo,
      );

      Logger.info(
          '🔍 Successfully extracted entity data with ${entityGroups.length} entity groups');
      return entityData;
    } catch (e, stack) {
      Logger.error('Error extracting entity data from response: $e $stack');
      return entities_model.EntitiesData();
    }
  }

  // Helper method to process individual entity into EntityGroup
  void _processEntityToGroup(dynamic entity, String entityType,
      List<entities_model.EntityGroup> entityGroups) {
    if (entity != null) {
      Logger.info('🔍 Processing $entityType entity');
      var attributeString = "";
      // Create attributes list
      List<entities_model.Attribute> attributes = [];
      List<AttributeMetadata> attributeMetaDataList = [];
      List<Relationship> relationships = [];
      List<Validation> validationsList = [];
      List<RelationshipProperties> relationshipProperties = [];
      List<EnumAttribute> enumAttributes = [];

      try {
        if (entity["attributes"] != null) {
          List<String> keysList = entity["attributes"].keys.toList();

          // Output: 1
          entity["attributes"].forEach((key, value) {
            int index = keysList.indexOf(key);
            if (value is Map) {
              attributes.add(entities_model.Attribute(
                name: entity["attribute_metadata"][value["name"]]
                        ["display_name"] ??
                    value["name"],
                type: value["data_type"] ?? 'Unknown',
                required: true,
                isPk: value["primary_key"] ?? false,
                description: entity["attribute_metadata"][value["name"]]
                        ["description"] ??
                    value["name"],
                isFk: value["foreign_key"] ?? false,
              ));

              if (attributeString.isEmpty) {
                attributeString = entity["attribute_metadata"][value["name"]]
                        ["display_name"] ??
                    value["name"];
              } else if (entity["attributes"].length - 1 == index) {
                attributeString +=
                    " and ${entity["attribute_metadata"][value["name"]]["display_name"] ?? value["name"]}";
              } else {
                attributeString +=
                    ", ${entity["attribute_metadata"][value["name"]]["display_name"] ?? value["name"]}";
              }
            } else {
              attributes.add(entities_model.Attribute(
                name: key,
                type: value.dataType?.toString() ?? 'Unknown',
                required: value.primaryKey ?? false,
                isPk: value.primaryKey ?? false,
                description: value.name ?? key,
                isFk: value.foreignKey ?? false,
              ));
            }
            AttributeMetadata? attributeMetaData =
                AttributeMetadata.fromJson(entity["attribute_metadata"][key]);
            if (attributeMetaData != null) {
              attributeMetaDataList.add(attributeMetaData);
            }
          });
        }

        entity["relationships"].forEach((key, value) {
          relationships.add(Relationship.fromJson(value));
        });

        entity["validations"].forEach((key, value) {
          validationsList.add(Validation.fromJson(value));
        });

        entity["relationship_properties"].forEach((key, value) {
          relationshipProperties.add(RelationshipProperties.fromMap(value));
        });

        entity["enum_attributes"].forEach((key, value) {
          enumAttributes.add(EnumAttribute.fromJson(value));
        });
      } catch (e, stack) {
        if (kDebugMode) {
          print("$e,$stack");
        }
      }

      // Create business rules list
      // List<entities_model.BusinessRule> businessRules = [];
      // if (entity["business_rules"] != null) {
      //   businessRules.add(entities_model.BusinessRule(
      //     name: 'Business Rules',
      //     description: 'Business rules defined for this entity',
      //   ));
      // }

      // Create Entity object
      entities_model.Entity entityObj = entities_model.Entity(
          id: entity["entity_id"] ??
              'entity_${entityType.toLowerCase()}_${DateTime.now().millisecondsSinceEpoch}',
          title: entity["display_name"] ?? entityType,
          description: entity["description"] ?? 'Entity Type: $entityType',
          version: '1.0',
          expanded: false,
          checked: false,
          createdBy: 'System',
          createdDate: DateTime.now(),
          modifiedBy: 'System',
          modifiedDate: DateTime.now().toString(),
          attributes: attributes,
          attributeString: attributeString,
          attributeMetaDataList: attributeMetaDataList,
          relationships: relationships,
          validationsList: validationsList,
          relationshipProperties: relationshipProperties,
          enumAttributes: enumAttributes

          // businessRules: businessRules,
          );

      // Create EntityGroup
      entities_model.EntityGroup entityGroup = entities_model.EntityGroup(
        id: 'group_${entityType.toLowerCase()}',
        title: entityType,
        documentId: 'doc_${entityType.toLowerCase()}',
        coreCount: '1',
        enitiyVersions: ['1.0'],
        checked: false,
        entities: [entityObj],
      );

      entityGroups.add(entityGroup);
      Logger.info('🔍 Added $entityType entity group to entity list');
    }
  }

  // Extract workflow data from validation response
  List<Map<String, dynamic>> _extractWorkflowDataFromResponse(
      WorkFlowModelResponseModel response) {
    try {
      Logger.info(
          '🔍 Extracting enhanced workflow data from validation response');

      List<Map<String, dynamic>> extractedWorkflows = [];

      // Check if we have parsed data
      if (response.parsedData != null && response.parsedData!.go != null) {
        Logger.info('🔍 Processing parsed workflow data');

        final go = response.parsedData!.go!;

        // Extract comprehensive workflow data
        Map<String, dynamic> workflow = {
          'id': 'workflow_${DateTime.now().millisecondsSinceEpoch}',
          'title': go.globalObjectives?.name ?? 'Workflow from Validation',
          'description': go.globalObjectives?.description ??
              'Workflow created from manual validation',
          'version': go.globalObjectives?.version ?? '1.0',
          'status': go.globalObjectives?.status ?? 'Active',
          'createdBy': 'Manual Creation',
          'createdDate': DateTime.now().toString(),
          'modifiedBy': 'Manual Creation',
          'modifiedDate': DateTime.now().toString(),
          'mainTitle': go.globalObjectives?.name ?? 'Workflow from Validation',

          // Core Details
          'primaryEntity': go.globalObjectives?.primaryEntity ?? 'Unknown',
          'classification': go.globalObjectives?.classification ?? 'Process',
          'tenantName': go.globalObjectives?.tenantName ?? 'Unknown Tenant',
          'bookName': go.globalObjectives?.bookName ?? 'Unknown Book',
          'chapterName': go.globalObjectives?.chapterName ?? 'Unknown Chapter',

          // Process Ownership
          'processOwnership': _extractProcessOwnership(go),

          // Trigger Definition
          'triggerDefinition': _extractTriggerDefinition(go),

          // Local Objectives
          'localObjectives': _extractLocalObjectives(go),

          // Pathway Definitions
          'pathwayDefinitions': _extractPathwayDefinitions(go),

          // Business Rules
          'businessRules': _extractBusinessRules(go),

          // Performance Metadata
          'performanceMetadata': _extractPerformanceMetadata(go),

          // Process Flow
          'processFlow': _extractProcessFlow(go),

          // Tree structure for visualization
          'tree': _convertValidationDataToTree(go),

          // Raw workflow details
          'workFlowDetails': go,
        };

        extractedWorkflows.add(workflow);
        Logger.info(
            '🔍 Successfully created enhanced workflow from validation response');
      }

      Logger.info(
          '🔍 Successfully extracted ${extractedWorkflows.length} enhanced workflows from validation response');
      return extractedWorkflows;
    } catch (e) {
      Logger.error('Error extracting workflow data from response: $e');
      return [];
    }
  }

  // Helper method to extract process ownership
  Map<String, dynamic> _extractProcessOwnership(dynamic go) {
    try {
      if (go.processOwnership != null) {
        return {
          'originator': go.processOwnership.originator ?? 'Unknown',
          'processOwner': go.processOwnership.processOwner ?? 'Unknown',
          'businessSponsor': go.processOwnership.businessSponsor ?? 'Unknown',
        };
      }
    } catch (e) {
      Logger.error('Error extracting process ownership: $e');
    }
    return {
      'originator': 'Unknown',
      'processOwner': 'Unknown',
      'businessSponsor': 'Unknown',
    };
  }

  // Helper method to extract trigger definition
  Map<String, dynamic> _extractTriggerDefinition(dynamic go) {
    try {
      if (go.triggerDefinition != null) {
        return {
          'triggerType': go.triggerDefinition.triggerType ?? 'Unknown',
          'triggerCondition':
              go.triggerDefinition.triggerCondition ?? 'Unknown',
          'triggerSchedule': go.triggerDefinition.triggerSchedule ?? 'Unknown',
          'triggerAttributes': go.triggerDefinition.triggerAttributes ?? [],
        };
      }
    } catch (e) {
      Logger.error('Error extracting trigger definition: $e');
    }
    return {
      'triggerType': 'Unknown',
      'triggerCondition': 'Unknown',
      'triggerSchedule': 'Unknown',
      'triggerAttributes': [],
    };
  }

  // Helper method to extract local objectives
  List<Map<String, dynamic>> _extractLocalObjectives(dynamic go) {
    List<Map<String, dynamic>> localObjectives = [];
    try {
      if (go.localObjectivesList != null &&
          go.localObjectivesList!.isNotEmpty) {
        for (var lo in go.localObjectivesList!) {
          localObjectives.add({
            'loNumber': lo.loNumber ?? 0,
            'loName': lo.loName ?? 'Unknown',
            'actorType': lo.actorType ?? 'Unknown',
            'id': lo.id ?? 'Unknown',
            'workSource': lo.workSource ?? 'Unknown',
            'terminal': lo.terminal ?? false,
          });
        }
      }
    } catch (e) {
      Logger.error('Error extracting local objectives: $e');
    }
    return localObjectives;
  }

  // Helper method to extract pathway definitions
  List<Map<String, dynamic>> _extractPathwayDefinitions(dynamic go) {
    List<Map<String, dynamic>> pathways = [];
    try {
      if (go.pathwayDefinitions != null && go.pathwayDefinitions!.isNotEmpty) {
        for (var pathway in go.pathwayDefinitions!) {
          pathways.add({
            'id': pathway.id ?? 'Unknown',
            'pathwayNumber': pathway.pathwayNumber ?? 0,
            'pathwayName': pathway.pathwayName ?? 'Unknown',
            'steps': pathway.steps ?? [],
          });
        }
      }
    } catch (e) {
      Logger.error('Error extracting pathway definitions: $e');
    }
    return pathways;
  }

  // Helper method to extract business rules
  List<Map<String, dynamic>> _extractBusinessRules(dynamic go) {
    List<Map<String, dynamic>> businessRules = [];
    try {
      if (go.businessRules != null && go.businessRules!.isNotEmpty) {
        for (var rule in go.businessRules!) {
          businessRules.add({
            'id': rule.id ?? 'Unknown',
            'ruleName': rule.ruleName ?? 'Unknown',
            'ruleDescription': rule.ruleDescription ?? 'Unknown',
            'ruleInputs': rule.ruleInputs ?? [],
            'ruleOutputs': rule.ruleOutputs ?? [],
            'ruleErrorMessage': rule.ruleErrorMessage ?? 'Unknown',
            'ruleValidationType': rule.ruleValidationType ?? 'Unknown',
          });
        }
      }
    } catch (e) {
      Logger.error('Error extracting business rules: $e');
    }
    return businessRules;
  }

  // Helper method to extract performance metadata
  Map<String, dynamic> _extractPerformanceMetadata(dynamic go) {
    try {
      if (go.performanceMetadata != null &&
          go.performanceMetadata.metadataData != null) {
        final metadata = go.performanceMetadata.metadataData;
        return {
          'cycleTime': metadata.cycleTime ?? 'Unknown',
          'numberOfPathways': metadata.numberOfPathways ?? 0,
          'volumeMetrics': {
            'averageVolume': metadata.volumeMetrics?.averageVolume ?? 0,
            'peakVolume': metadata.volumeMetrics?.peakVolume ?? 0,
            'unit': metadata.volumeMetrics?.unit ?? 'Unknown',
          },
          'slaThresholds': {
            'managerReview': metadata.slaThresholds?.managerReview ?? 'Unknown',
            'notification': metadata.slaThresholds?.notification ?? 'Unknown',
            'systemProcessing':
                metadata.slaThresholds?.systemProcessing ?? 'Unknown',
          },
          'criticalLoPerformance': metadata.criticalLoPerformance ?? {},
        };
      }
    } catch (e) {
      Logger.error('Error extracting performance metadata: $e');
    }
    return {
      'cycleTime': 'Unknown',
      'numberOfPathways': 0,
      'volumeMetrics': {'averageVolume': 0, 'peakVolume': 0, 'unit': 'Unknown'},
      'slaThresholds': {
        'managerReview': 'Unknown',
        'notification': 'Unknown',
        'systemProcessing': 'Unknown'
      },
      'criticalLoPerformance': {},
    };
  }

  // Helper method to extract process flow
  List<Map<String, dynamic>> _extractProcessFlow(dynamic go) {
    List<Map<String, dynamic>> processFlow = [];
    try {
      if (go.processFlow != null && go.processFlow!.isNotEmpty) {
        for (var flow in go.processFlow!) {
          processFlow.add({
            'loName': flow.loName ?? 'Unknown',
            'actorType': flow.actorType ?? 'Unknown',
            'description': flow.description ?? 'Unknown',
            'routeType': flow.routeType ?? 'Unknown',
            'id': flow.id ?? 'Unknown',
            'conditions': flow.conditions ?? [],
            'routes': flow.routes ?? [],
            'parallelRoutes': flow.parallelRoutes ?? [],
            'joinAt': flow.joinAt ?? '',
          });
        }
      }
    } catch (e) {
      Logger.error('Error extracting process flow: $e');
    }
    return processFlow;
  }

  // Helper method to convert validation data to tree format
  List<Map<String, dynamic>> _convertValidationDataToTree(dynamic go) {
    List<Map<String, dynamic>> tree = [];

    try {
      // Create tree nodes from global objectives
      if (go.globalObjectives != null) {
        tree.add({
          'id': 'go_${DateTime.now().millisecondsSinceEpoch}',
          'text': go.globalObjectives.name ?? 'Global Objective',
          'level': 0,
          'sequence': 0,
          'altText': go.globalObjectives.description,
          'isRejection': false,
          'isApproval': false,
          'isParallel': false,
          'hasCheckmark': false,
          'hasX': false,
        });
      }

      // Add local objectives if available
      if (go.localObjectivesList != null &&
          go.localObjectivesList!.isNotEmpty) {
        for (int i = 0; i < go.localObjectivesList!.length; i++) {
          final lo = go.localObjectivesList![i];
          tree.add({
            'id': 'lo_${i}_${DateTime.now().millisecondsSinceEpoch}',
            'text': lo.loName ?? 'Local Objective ${i + 1}',
            'level': 1,
            'sequence': i,
            'altText': 'Actor: ${lo.actorType ?? 'Unknown'}',
            'isRejection': false,
            'isApproval': false,
            'isParallel': false,
            'hasCheckmark': false,
            'hasX': false,
          });
        }
      }

      // Add business rules if available
      if (go.businessRules != null && go.businessRules!.isNotEmpty) {
        for (int i = 0; i < go.businessRules!.length; i++) {
          final rule = go.businessRules![i];
          tree.add({
            'id': 'br_${i}_${DateTime.now().millisecondsSinceEpoch}',
            'text': rule.ruleName ?? 'Business Rule ${i + 1}',
            'level': 2,
            'sequence': i,
            'altText': rule.ruleDescription,
            'isRejection': false,
            'isApproval': false,
            'isParallel': false,
            'hasCheckmark': false,
            'hasX': false,
          });
        }
      }

      Logger.info(
          '🔍 Successfully converted validation data to tree format with ${tree.length} nodes');
    } catch (e) {
      Logger.error('Error converting validation data to tree: $e');
    }

    return tree;
  }

  // Extract workflow LO data from API response
  List<Map<String, dynamic>> _extractWorkflowLoDataFromResponse(
      WorkFlowLoResponseModel response) {
    List<Map<String, dynamic>> extractedData = [];

    try {
      Logger.info('🔍 Extracting workflow LO data from API response...');

      // Extract from parsedLos if available
      if (response.parsedLos != null) {
        final parsedLos = response.parsedLos!;

        Map<String, dynamic> loData = {
          'id': 'lo_${DateTime.now().millisecondsSinceEpoch}',
          'name': parsedLos.name ?? 'Workflow LO',
          'version': parsedLos.version ?? '1.0',
          'status': parsedLos.status ?? 'Active',
          'workflowSource': parsedLos.workflowSource ?? '',
          'functionType': parsedLos.functionType ?? '',
          'agentType': parsedLos.agentType?.toString() ?? 'DIGITAL',
          'executionRights': parsedLos.executionRights ?? '',
          'goId': parsedLos.goId ?? '',
          'loId': parsedLos.loId?.toString() ?? '',
        };

        extractedData.add(loData);
        Logger.info('🔍 Added workflow LO data: ${parsedLos.name}');
      }

      // Extract from parsedData.localObjectives if available
      if (response.parsedData?.localObjectives != null) {
        final localObjectives = response.parsedData!.localObjectives!;

        Map<String, dynamic> loData = {
          'id': 'lo_data_${DateTime.now().millisecondsSinceEpoch}',
          'name': localObjectives.name ?? 'Local Objectives',
          'version': localObjectives.version ?? '1.0',
          'status': localObjectives.status ?? 'Active',
          'workflowSource': localObjectives.workflowSource ?? '',
          'functionType': localObjectives.functionType ?? '',
          'agentType': localObjectives.agentType?.toString() ?? 'DIGITAL',
          'executionRights': localObjectives.executionRights ?? '',
          'goId': localObjectives.goId ?? '',
          'loId': localObjectives.loId?.toString() ?? '',
        };

        extractedData.add(loData);
        Logger.info('🔍 Added local objectives data: ${localObjectives.name}');
      }

      Logger.info(
          '🔍 Successfully extracted ${extractedData.length} workflow LO items from API response');
    } catch (e) {
      Logger.error('Error extracting workflow LO data from response: $e');
      // Return default data on error
      Map<String, dynamic> errorData = {
        'id': 'error_lo_${DateTime.now().millisecondsSinceEpoch}',
        'name': 'Workflow LO (Error)',
        'version': '1.0',
        'status': 'Error',
        'workflowSource': 'Error occurred',
        'functionType': 'Error occurred',
        'agentType': 'DIGITAL',
        'executionRights': 'Error occurred',
        'goId': 'Error occurred',
        'loId': 'Error occurred',
      };
      extractedData.add(errorData);
    }

    return extractedData;
  }

  // Convert process flow data to tree data format expected by WorkflowTreeBuilder
  List<dynamic> convertProcessFlowToTreeData(List<ProcessFlow> processFlow) {
    List<dynamic> treeData = [];
    Map<String, Map<String, dynamic>> nodeMap = {};

    try {
      // First pass: Create all nodes and map them by their LO name
      for (int i = 0; i < processFlow.length; i++) {
        ProcessFlow flow = processFlow[i];
        String nodeId = flow.loName ?? 'Node_$i';
        String nodeName = flow.loName ?? 'Unknown Node';
        String routeType = flow.routeType ?? 'Sequential';
        String actorType = flow.actorType ?? 'UNKNOWN';
        String description = flow.description ?? '';

        Map<String, dynamic> node = {
          'id': nodeId,
          'text': nodeName,
          'level': 0, // Will be updated based on hierarchy
          'sequence': i,
          'routeType': routeType,
          'actorType': actorType,
          'description': description,
          'children': <Map<String, dynamic>>[],
          'isRejection': _isRejectionNode(nodeName, description),
          'isApproval': _isApprovalNode(nodeName, description),
          'hasX': _isRejectionNode(nodeName, description),
          'hasCheckmark': _isApprovalNode(nodeName, description),
          'isParallel': routeType.toLowerCase() == 'parallel',
          'conditions': flow.conditions
                  ?.map((c) => {
                        'condition': c.condition ?? '',
                        'routeTo': c.routeTo ?? '',
                      })
                  .toList() ??
              [],
          'routes': flow.routes ?? [],
          'parallelRoutes': flow.parallelRoutes
                  ?.map((pr) => {
                        'routeTo': pr.routeTo ?? '',
                        'description': pr.description ?? '',
                      })
                  .toList() ??
              [],
          'joinAt': flow.joinAt ?? '',
        };

        nodeMap[nodeId] = node;
      }

      // Second pass: Build hierarchy based on routing information
      Set<String> referencedNodes = {};

      for (var node in nodeMap.values) {
        // Build children based on routing type
        List<Map<String, dynamic>> children = _buildNodeChildren(node, nodeMap);
        node['children'] = children;

        // Track referenced nodes
        for (var child in children) {
          referencedNodes.add(child['id']);
        }
      }

      // Third pass: Find root nodes and assign levels
      for (var node in nodeMap.values) {
        if (!referencedNodes.contains(node['id'])) {
          // This is a root node
          _assignLevels(node, 0);
          treeData.add(node);
        }
      }

      // Sort root nodes by sequence
      treeData
          .sort((a, b) => (a['sequence'] ?? 0).compareTo(b['sequence'] ?? 0));
    } catch (e) {
      Logger.error('Error converting process flow to tree data: $e');
      // Return a fallback structure
      treeData = _createFallbackTreeData(processFlow);
    }

    return treeData;
  }

// Recursively assign levels to nodes
  void _assignLevels(Map<String, dynamic> node, int level) {
    node['level'] = level;
    List<Map<String, dynamic>> children = node['children'] ?? [];
    for (var child in children) {
      _assignLevels(child, level + 1);
    }
  }

  // Check if a node represents a rejection action
  bool _isRejectionNode(String nodeName, String description) {
    String lowerName = nodeName.toLowerCase();
    String lowerDesc = description.toLowerCase();
    return lowerName.contains('reject') ||
        lowerName.contains('deny') ||
        lowerName.contains('decline') ||
        lowerDesc.contains('reject') ||
        lowerDesc.contains('deny');
  }

  // Check if a node represents an approval action
  bool _isApprovalNode(String nodeName, String description) {
    String lowerName = nodeName.toLowerCase();
    String lowerDesc = description.toLowerCase();
    return lowerName.contains('approve') ||
        lowerName.contains('accept') ||
        lowerName.contains('confirm') ||
        lowerDesc.contains('approve') ||
        lowerDesc.contains('accept');
  }

  // Create fallback tree data if conversion fails
  List<dynamic> _createFallbackTreeData(List<ProcessFlow> processFlow) {
    List<dynamic> fallbackData = [];

    for (int i = 0; i < processFlow.length; i++) {
      ProcessFlow flow = processFlow[i];
      fallbackData.add({
        'id': 'fallback_$i',
        'text': flow.loName ?? 'Process Step ${i + 1}',
        'level': 0,
        'sequence': i,
        'children': <Map<String, dynamic>>[],
        'isRejection': false,
        'isApproval': false,
        'hasX': false,
        'hasCheckmark': false,
        'isParallel': false,
      });
    }

    return fallbackData;
  }

  // Business logic methods for process flow visualization

  List<List<dynamic>> buildProcessColumns(List<dynamic> processFlow) {
    // Create mapping using ProcessFlow objects with loId as key
    final nodeMap = <String, dynamic>{};
    for (final node in processFlow) {
      if (node.loId != null && node.loId!.isNotEmpty) {
        nodeMap[node.loId!] = node;
      }
    }

    // Add terminal node to mapping
    final terminalNode = _createTerminalNode();
    nodeMap['Terminal'] = terminalNode;

    // Track first appearance of nodes and paused positions
    final firstAppearance = <String, int>{};
    final columns = <List<dynamic>>[];
    final pausedPositions = <int, int>{}; // columnIndex -> position in column

    // Helper to get node by id
    dynamic getNode(String id) {
      if (id == 'Terminal') return terminalNode;
      return nodeMap[id];
    }

    // Initialize with first column - find start node
    dynamic startNode;
    for (final node in processFlow) {
      if (node.loId == processFlow.first.loId) {
        startNode = node;
        break;
      }
    }

    startNode ??= processFlow.isNotEmpty ? processFlow.first : null;

    if (startNode == null) return [];

    List<dynamic> currentColumn = [startNode];
    columns.add(currentColumn);
    firstAppearance[startNode.loName ?? ''] = 0;

    int currentColumnIndex = 0;

    while (currentColumn.isNotEmpty) {
      final nextColumnNodes = <dynamic>[];
      final nextColumnSet = <String>{};

      for (final node in currentColumn) {
        // Handle special nodes (terminal and empty)
        if (_isTerminalNode(node) || _isEmptyNode(node)) continue;

        // Handle ProcessFlow nodes only - check if it has loName property
        if (node.loName == null) continue;
        final loName = node.loName!;

        // Check if this is a duplicate node (appears again)
        bool isDuplicate = firstAppearance.containsKey(loName) &&
            firstAppearance[loName]! < currentColumnIndex;

        if (isDuplicate) {
          // Mark this node as "Paused" and track its position
          final pausedNode = _createPausedNode(loName);
          final nodePosition = currentColumn.indexOf(node);
          pausedPositions[currentColumnIndex] = nodePosition;

          // Replace the node with paused version in current column
          currentColumn[nodePosition] = pausedNode;
          continue;
        }

        if (firstAppearance[loName]! < currentColumnIndex) continue;

        switch (node.routeType) {
          case 'Alternate':
            if (node.conditions != null) {
              for (final cond in node.conditions!) {
                final childId = cond.routeTo ?? '';
                if (childId.isEmpty || nextColumnSet.contains(childId)) {
                  continue;
                }

                final childNode = getNode(childId);
                if (childNode != null) {
                  nextColumnSet.add(childId);
                  nextColumnNodes.add(childNode);
                }
              }
            }
            break;

          case 'Sequential':
            if (node.routes != null) {
              for (final route in node.routes!) {
                final childId = route;
                if (childId.isEmpty || nextColumnSet.contains(childId)) {
                  continue;
                }

                final childNode = getNode(childId);
                if (childNode != null) {
                  nextColumnSet.add(childId);
                  nextColumnNodes.add(childNode);
                }
              }
            }
            break;

          case 'Parallel':
            if (node.parallelRoutes != null) {
              for (final route in node.parallelRoutes!) {
                final childId = route.routeTo ?? '';
                if (childId.isEmpty || nextColumnSet.contains(childId)) {
                  continue;
                }

                final childNode = getNode(childId);
                if (childNode != null) {
                  nextColumnSet.add(childId);
                  nextColumnNodes.add(childNode);
                }
              }
            }
            break;
        }
      }

      // Stop if no more nodes
      if (nextColumnNodes.isEmpty) break;

      // Now check for duplicates and replace with paused nodes
      for (int i = 0; i < nextColumnNodes.length; i++) {
        final node = nextColumnNodes[i];
        if (_isEmptyNode(node) ||
            _isTerminalNode(node) ||
            _isPausedNode(node)) {
          continue;
        }

        final nodeName = node.loName ?? '';
        if (nodeName.isEmpty) continue;

        // Check if this node already appeared in a previous column using firstAppearance
        bool nodeExistsInPreviousColumns =
            firstAppearance.containsKey(nodeName) &&
                firstAppearance[nodeName]! <= currentColumnIndex;

        // Also check if this node appears multiple times in the current nextColumnNodes
        bool appearsMultipleTimesInCurrentColumn = false;
        int firstOccurrenceIndex = -1;
        for (int j = 0; j < nextColumnNodes.length; j++) {
          final otherNode = nextColumnNodes[j];
          if (!_isEmptyNode(otherNode) &&
              !_isTerminalNode(otherNode) &&
              !_isPausedNode(otherNode) &&
              otherNode.loName == nodeName) {
            if (firstOccurrenceIndex == -1) {
              firstOccurrenceIndex = j;
            } else if (j != firstOccurrenceIndex) {
              appearsMultipleTimesInCurrentColumn = true;
              break;
            }
          }
        }

        // Replace with paused node if it's a duplicate
        if (nodeExistsInPreviousColumns ||
            (appearsMultipleTimesInCurrentColumn &&
                i != firstOccurrenceIndex)) {
          nextColumnNodes[i] = _createPausedNode(nodeName);
        }
      }

      // Stop if no more nodes
      if (nextColumnNodes.isEmpty) break;

      // Insert empty nodes at paused positions for subsequent columns
      for (final pausedEntry in pausedPositions.entries) {
        final pausedColumnIndex = pausedEntry.key;
        final pausedPosition = pausedEntry.value;

        // Only add empty nodes for columns after the paused column
        if (currentColumnIndex + 1 > pausedColumnIndex) {
          // Ensure we have enough space in the list
          while (nextColumnNodes.length <= pausedPosition) {
            nextColumnNodes.add(_createEmptyNode());
          }
          // Insert empty node at the paused position
          nextColumnNodes.insert(pausedPosition, _createEmptyNode());
        }
      }

      // Update first appearance for new nodes
      for (final node in nextColumnNodes) {
        if (_isEmptyNode(node) || _isPausedNode(node)) continue;
        final name = _isTerminalNode(node) ? 'Terminal' : node.loName ?? '';
        if (name.isNotEmpty) {
          firstAppearance.putIfAbsent(name, () => currentColumnIndex + 1);
        }
      }

      columns.add(nextColumnNodes);
      currentColumn = nextColumnNodes;
      currentColumnIndex++;
    }

    // columns.forEach(
    //   (element) {
    //     print(element.toList());
    //   },
    // );
    return columns;
  }

  // Helper methods for special nodes
  dynamic _createEmptyNode() {
    return _EmptyNode(height: 30);
  }

  dynamic _createTerminalNode() {
    return _TerminalNode(loName: 'Terminal');
  }

  dynamic _createPausedNode(String loName) {
    return _PausedNode(loName: loName);
  }

  bool _isEmptyNode(dynamic node) {
    return node is _EmptyNode;
  }

  bool _isTerminalNode(dynamic node) {
    return node is _TerminalNode;
  }

  bool _isPausedNode(dynamic node) {
    return node is _PausedNode;
  }

  // Public helper methods for the screen
  bool isEmptyNode(dynamic node) {
    return _isEmptyNode(node);
  }

  bool isTerminalNode(dynamic node) {
    return _isTerminalNode(node);
  }

  bool isPausedNode(dynamic node) {
    return _isPausedNode(node);
  }

  // Analyze node connections to determine styling
  void analyzeNodeConnections(List<dynamic> processFlow,
      Map<String, Map<String, dynamic>> nodeConnections, List<Color> colors) {
    // Build columns to see which nodes appear in which columns
    final columns = buildProcessColumns(processFlow);

    // Track node positions: nodeName -> List of column indices where it appears
    Map<String, List<int>> nodePositions = {};

    for (int columnIndex = 0; columnIndex < columns.length; columnIndex++) {
      final column = columns[columnIndex];
      for (final node in column) {
        // Skip special nodes
        if (_isEmptyNode(node) || _isTerminalNode(node)) continue;

        final nodeName = node.loName ?? '';
        if (nodeName.isNotEmpty) {
          nodePositions.putIfAbsent(nodeName, () => []);
          nodePositions[nodeName]!.add(columnIndex);
        }
      }
    }

    int colorIndex = 0;

    // 1. Handle nodes that appear in multiple columns (duplicates)
    for (final entry in nodePositions.entries) {
      final nodeName = entry.key;
      final positions = entry.value;

      // Only style nodes that appear more than once
      if (positions.length > 1) {
        final color = colors[colorIndex % colors.length];

        // First occurrence gets isLinkStart
        nodeConnections['${nodeName}_${positions.first}'] = {
          'isLinkStart': true,
          'isLinkEnd': false,
          'boxColor': color,
        };

        // Last occurrence gets isLinkEnd
        nodeConnections['${nodeName}_${positions.last}'] = {
          'isLinkStart': false,
          'isLinkEnd': true,
          'boxColor': color,
        };

        colorIndex++;
      }
    }

    // 2. Track alternate and parallel relationships for tagging
    Map<String, String> alternateTagMap = {}; // nodeName -> Alt.X tag
    Map<String, String> parallelTagMap = {}; // nodeName -> Parl.X tag

    // Create a mapping from loId to loName for tag assignment
    Map<String, String> loIdToNameMap = {};
    for (final node in processFlow) {
      if (node.loId != null && node.loName != null) {
        loIdToNameMap[node.loId!] = node.loName!;
      }
    }

    // Analyze alternate and parallel routing to assign tags
    for (final node in processFlow) {
      // Handle alternate routing
      if (node.routeType == 'Alternate' && node.conditions != null) {
        for (int i = 0; i < node.conditions!.length; i++) {
          final targetId = node.conditions![i].routeTo ?? '';
          final targetName = loIdToNameMap[targetId] ?? targetId;
          if (targetName.isNotEmpty) {
            alternateTagMap[targetName] = 'Alt.${i + 1}';
          }
        }
      }

      // Handle parallel routing
      if (node.routeType == 'Parallel' && node.parallelRoutes != null) {
        for (int i = 0; i < node.parallelRoutes!.length; i++) {
          final targetId = node.parallelRoutes![i].routeTo ?? '';
          final targetName = loIdToNameMap[targetId] ?? targetId;
          if (targetName.isNotEmpty) {
            parallelTagMap[targetName] = 'Parl.${i + 1}';
          }
        }
      }
    }

    // Add alternate and parallel tags to node connections
    for (int columnIndex = 0; columnIndex < columns.length; columnIndex++) {
      final column = columns[columnIndex];
      for (final node in column) {
        String nodeName = '';

        if (_isEmptyNode(node) || _isTerminalNode(node)) {
          continue;
        }

        // Handle paused nodes specially
        if (_isPausedNode(node)) {
          nodeName = node.loName ?? '';
        } else {
          nodeName = node.loName ?? '';
        }

        if (nodeName.isNotEmpty) {
          final nodeKey = '${nodeName}_$columnIndex';

          // Add alternate tag if present
          if (alternateTagMap.containsKey(nodeName)) {
            nodeConnections.putIfAbsent(nodeKey, () => {});
            nodeConnections[nodeKey]!['alternateTag'] =
                alternateTagMap[nodeName];
          }

          // Add parallel tag if present
          if (parallelTagMap.containsKey(nodeName)) {
            nodeConnections.putIfAbsent(nodeKey, () => {});
            nodeConnections[nodeKey]!['parallelTag'] = parallelTagMap[nodeName];
          }

          // Generalized handling for paused nodes
          // Give paused nodes the same styling as their corresponding regular node in the same column
          if (_isPausedNode(node)) {
            // Find the corresponding regular node in the same column and copy its styling
            for (final otherNode in column) {
              if (!_isEmptyNode(otherNode) &&
                  !_isTerminalNode(otherNode) &&
                  !_isPausedNode(otherNode) &&
                  otherNode.loName == nodeName) {
                final regularNodeKey = '${nodeName}_$columnIndex';

                // Copy styling from the regular node to the paused node
                if (nodeConnections.containsKey(regularNodeKey)) {
                  final regularStyling = nodeConnections[regularNodeKey]!;
                  nodeConnections[nodeKey] = Map.from(regularStyling);

                  // Override the alternate tag for the paused node if it exists
                  if (alternateTagMap.containsKey(nodeName)) {
                    nodeConnections[nodeKey]!['alternateTag'] =
                        alternateTagMap[nodeName];
                  }

                  // Override the parallel tag for the paused node if it exists
                  if (parallelTagMap.containsKey(nodeName)) {
                    nodeConnections[nodeKey]!['parallelTag'] =
                        parallelTagMap[nodeName];
                  }
                }
                break;
              }
            }
          }
        }
      }
    }

    // 3. Handle convergence points (multiple nodes with same next node)
    Map<String, List<String>> convergenceMap =
        {}; // nextNode -> List of sourceNodes
    Map<String, int> nodeToColumnMap = {}; // nodeName -> columnIndex

    // Build convergence map and node-to-column mapping
    for (int columnIndex = 0; columnIndex < columns.length; columnIndex++) {
      final column = columns[columnIndex];
      for (final node in column) {
        if (_isEmptyNode(node) ||
            _isTerminalNode(node) ||
            _isPausedNode(node)) {
          continue;
        }

        final nodeName = node.loName ?? '';
        if (nodeName.isNotEmpty) {
          nodeToColumnMap[nodeName] = columnIndex;

          // Check what this node routes to
          String? nextNodeName;
          switch (node.routeType) {
            case 'Sequential':
              if (node.routes != null && node.routes!.isNotEmpty) {
                final nextId = node.routes!.first;
                nextNodeName = loIdToNameMap[nextId] ?? nextId;
              }
              break;
            case 'Alternate':
              if (node.conditions != null && node.conditions!.isNotEmpty) {
                // For alternate routing, we'll check each condition target
                for (final cond in node.conditions!) {
                  final targetId = cond.routeTo ?? '';
                  final targetName = loIdToNameMap[targetId] ?? targetId;
                  if (targetName.isNotEmpty) {
                    convergenceMap.putIfAbsent(targetName, () => []);
                    convergenceMap[targetName]!.add(nodeName);
                  }
                }
              }
              break;
            case 'Parallel':
              if (node.parallelRoutes != null &&
                  node.parallelRoutes!.isNotEmpty) {
                // For parallel routing, we'll check each parallel target
                for (final route in node.parallelRoutes!) {
                  final targetId = route.routeTo ?? '';
                  final targetName = loIdToNameMap[targetId] ?? targetId;
                  if (targetName.isNotEmpty) {
                    convergenceMap.putIfAbsent(targetName, () => []);
                    convergenceMap[targetName]!.add(nodeName);
                  }
                }
              }
              break;
          }

          // For sequential routing, add to convergence map
          if (nextNodeName != null && nextNodeName.isNotEmpty) {
            convergenceMap.putIfAbsent(nextNodeName, () => []);
            convergenceMap[nextNodeName]!.add(nodeName);
          }
        }
      }
    }

    // Apply styling for convergence points
    for (final entry in convergenceMap.entries) {
      final nextNodeName = entry.key;
      final sourceNodes = entry.value;

      // Only apply styling if multiple nodes converge to the same next node
      if (sourceNodes.length > 1) {
        final color = colors[colorIndex % colors.length];

        // Find the last source node (second node when multiple nodes are in same column)
        String? lastSourceNode;

        // Group source nodes by column
        Map<int, List<String>> nodesByColumn = {};
        for (final sourceNode in sourceNodes) {
          final columnIndex = nodeToColumnMap[sourceNode] ?? -1;
          nodesByColumn.putIfAbsent(columnIndex, () => []);
          nodesByColumn[columnIndex]!.add(sourceNode);
        }

        // Find the column with multiple nodes, or the rightmost column
        int targetColumnIndex = -1;
        for (final entry in nodesByColumn.entries) {
          final columnIndex = entry.key;
          final nodesInColumn = entry.value;

          if (nodesInColumn.length > 1) {
            // Multiple nodes in same column - take the second one (last in list)
            lastSourceNode = nodesInColumn.last;
            targetColumnIndex = columnIndex;
            break;
          } else if (columnIndex > targetColumnIndex) {
            // Single node in column - keep track of rightmost
            targetColumnIndex = columnIndex;
            lastSourceNode = nodesInColumn.first;
          }
        }

        // Apply styling to the last source node and the target node
        if (lastSourceNode != null) {
          final lastSourceColumnIndex = nodeToColumnMap[lastSourceNode] ?? -1;
          final nextNodeColumnIndex = nodeToColumnMap[nextNodeName] ?? -1;

          // Apply convergence styling (allow overriding existing styling for convergence points)
          String lastSourceKey = '${lastSourceNode}_$lastSourceColumnIndex';
          String nextNodeKey = '${nextNodeName}_$nextNodeColumnIndex';

          // Check if we should apply convergence styling
          bool shouldApplyConvergence = true;

          // Don't override duplicate node styling - it takes priority
          if (nodeConnections.containsKey(lastSourceKey)) {
            final sourceInfo = nodeConnections[lastSourceKey]!;
            // If source already has duplicate node styling, don't override
            if (sourceInfo.containsKey('isLinkStart') ||
                sourceInfo.containsKey('isLinkEnd')) {
              shouldApplyConvergence = false;
            }
          }

          if (nodeConnections.containsKey(nextNodeKey)) {
            final targetInfo = nodeConnections[nextNodeKey]!;
            // If target already has duplicate node styling, don't override
            if (targetInfo.containsKey('isLinkStart') ||
                targetInfo.containsKey('isLinkEnd')) {
              shouldApplyConvergence = false;
            }
          }

          if (shouldApplyConvergence) {
            // Last source node gets isLinkEnd
            nodeConnections[lastSourceKey] = {
              'isLinkStart': false,
              'isLinkEnd': true,
              'boxColor': color,
              // Preserve alternateTag if it exists
              if (nodeConnections.containsKey(lastSourceKey) &&
                  nodeConnections[lastSourceKey]!.containsKey('alternateTag'))
                'alternateTag': nodeConnections[lastSourceKey]!['alternateTag'],
              // Preserve parallelTag if it exists
              if (nodeConnections.containsKey(lastSourceKey) &&
                  nodeConnections[lastSourceKey]!.containsKey('parallelTag'))
                'parallelTag': nodeConnections[lastSourceKey]!['parallelTag'],
            };

            // Next node gets isLinkStart
            nodeConnections[nextNodeKey] = {
              'isLinkStart': true,
              'isLinkEnd': false,
              'boxColor': color,
              // Preserve alternateTag if it exists
              if (nodeConnections.containsKey(nextNodeKey) &&
                  nodeConnections[nextNodeKey]!.containsKey('alternateTag'))
                'alternateTag': nodeConnections[nextNodeKey]!['alternateTag'],
              // Preserve parallelTag if it exists
              if (nodeConnections.containsKey(nextNodeKey) &&
                  nodeConnections[nextNodeKey]!.containsKey('parallelTag'))
                'parallelTag': nodeConnections[nextNodeKey]!['parallelTag'],
            };

            colorIndex++;
          }
        }
      }
    }
  }

  clearBookResults() {
    _showAgentTableForBook = false;
    _showEntityTableForBook = false;
    _showWorkflowTableForBook = false;
    _validationResult = null;
    _validationError = null;
    _isValidating = false;
    _showAgentTable = false;
    _extractedAgentData = null;
    _showSidePanel = false;
    _showEntityTable = false;
    _showSideEntityPanel = false;
    _extractedEntityData = null;
    _showWorkflowTable = false;
    _extractedWorkflowData = null;
    _showWorkflowLoTable = false;
    _extractedWorkflowLoData = null;
    _showWorkflowLoSidePanel = false;
    notifyListeners();
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}

// Special node classes for empty and terminal nodes
class _EmptyNode {
  final int height;

  _EmptyNode({required this.height});
}

class _TerminalNode {
  final String loName;

  _TerminalNode({required this.loName});
}

class _PausedNode {
  final String loName;

  _PausedNode({required this.loName});
}
