// To parse this JSON data, do
//
//     final agentModelResponseModel = agentModelResponseModelFromJson(jsonString);

import 'dart:convert';

AgentModelResponseModel agentModelResponseModelFromJson(String str) =>
    AgentModelResponseModel.fromJson(json.decode(str));

String agentModelResponseModelToJson(AgentModelResponseModel data) =>
    json.encode(data.toJson());

class AgentModelResponseModel {
  bool? success;
  List<String>? messages;
  ParsedData? parsedData;
  List<ValidationError>? validationErrors;
  Map<String, Employee>? parsedRoles;
  Map<String, User>? parsedUsers;

  AgentModelResponseModel({
    this.success,
    this.messages,
    this.parsedData,
    this.validationErrors,
    this.parsedRoles,
    this.parsedUsers,
  });

  AgentModelResponseModel copyWith({
    bool? success,
    List<String>? messages,
    ParsedData? parsedData,
    List<ValidationError>? validationErrors,
    Map<String, Employee>? parsedRoles,
    Map<String, User>? parsedUsers,
  }) =>
      AgentModelResponseModel(
        success: success ?? this.success,
        messages: messages ?? this.messages,
        parsedData: parsedData ?? this.parsedData,
        validationErrors: validationErrors ?? this.validationErrors,
        parsedRoles: parsedRoles ?? this.parsedRoles,
        parsedUsers: parsedUsers ?? this.parsedUsers,
      );

  factory AgentModelResponseModel.fromJson(Map<String, dynamic> json) =>
      AgentModelResponseModel(
        success: json["success"],
        messages: json["messages"] == null
            ? []
            : List<String>.from(json["messages"]!.map((x) => x)),
        parsedData: json["parsed_data"] == null
            ? null
            : ParsedData.fromJson(json["parsed_data"]),
        validationErrors: json["validation_errors"] == null
            ? []
            : List<ValidationError>.from(json["validation_errors"]!
                .map((x) => ValidationError.fromJson(x))),
        parsedRoles: json["parsed_roles"] == null
            ? {}
            : Map.from(json["parsed_roles"]!).map(
                (k, v) => MapEntry<String, Employee>(k, Employee.fromJson(v))),
        parsedUsers: json["parsed_users"] == null
            ? {}
            : Map.from(json["parsed_users"]!)
                .map((k, v) => MapEntry<String, User>(k, User.fromJson(v))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "messages":
            messages == null ? [] : List<dynamic>.from(messages!.map((x) => x)),
        "parsed_data": parsedData?.toJson(),
        "validation_errors": validationErrors == null
            ? []
            : List<dynamic>.from(validationErrors!.map((x) => x.toJson())),
        "parsed_roles": parsedRoles == null
            ? {}
            : Map.from(parsedRoles!)
                .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "parsed_users": parsedUsers == null
            ? {}
            : Map.from(parsedUsers!)
                .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
      };
}

class ParsedData {
  Map<String, Employee>? roles;
  Map<String, User>? users;

  ParsedData({
    this.roles,
    this.users,
  });

  ParsedData copyWith({
    Map<String, Employee>? roles,
    Map<String, User>? users,
  }) =>
      ParsedData(
        roles: roles ?? this.roles,
        users: users ?? this.users,
      );

  factory ParsedData.fromJson(Map<String, dynamic> json) => ParsedData(
        roles: json["roles"] == null
            ? {}
            : Map.from(json["roles"]!).map(
                (k, v) => MapEntry<String, Employee>(k, Employee.fromJson(v))),
        users: json["users"] == null
            ? {}
            : Map.from(json["users"]!)
                .map((k, v) => MapEntry<String, User>(k, User.fromJson(v))),
      );

  Map<String, dynamic> toJson() => {
        "roles": roles == null
            ? {}
            : Map.from(roles!)
                .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
        "users": users == null
            ? {}
            : Map.from(users!)
                .map((k, v) => MapEntry<String, dynamic>(k, v.toJson())),
      };
}

class Employee {
  String? name;
  dynamic parentRole;
  List<String>? coreResponsibilities;
  String? reportsTo;
  String? organizationalLevel;
  String? department;
  String? team;
  List<Kpi>? kpis;
  List<String>? decisionAuthority;

  Employee({
    this.name,
    this.parentRole,
    this.coreResponsibilities,
    this.reportsTo,
    this.organizationalLevel,
    this.department,
    this.team,
    this.kpis,
    this.decisionAuthority,
  });

  Employee copyWith({
    String? name,
    dynamic parentRole,
    List<String>? coreResponsibilities,
    String? reportsTo,
    String? organizationalLevel,
    String? department,
    String? team,
    List<Kpi>? kpis,
    List<String>? decisionAuthority,
  }) =>
      Employee(
        name: name ?? this.name,
        parentRole: parentRole ?? this.parentRole,
        coreResponsibilities: coreResponsibilities ?? this.coreResponsibilities,
        reportsTo: reportsTo ?? this.reportsTo,
        organizationalLevel: organizationalLevel ?? this.organizationalLevel,
        department: department ?? this.department,
        team: team ?? this.team,
        kpis: kpis ?? this.kpis,
        decisionAuthority: decisionAuthority ?? this.decisionAuthority,
      );

  factory Employee.fromJson(Map<String, dynamic> json) => Employee(
        name: json["name"],
        parentRole: json["parent_role"],
        coreResponsibilities: json["core_responsibilities"] == null
            ? []
            : List<String>.from(json["core_responsibilities"]!.map((x) => x)),
        reportsTo: json["reports_to"],
        organizationalLevel: json["organizational_level"],
        department: json["department"],
        team: json["team"],
        kpis: json["kpis"] == null
            ? []
            : List<Kpi>.from(json["kpis"]!.map((x) => Kpi.fromJson(x))),
        decisionAuthority: json["decision_authority"] == null
            ? []
            : List<String>.from(json["decision_authority"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "parent_role": parentRole,
        "core_responsibilities": coreResponsibilities == null
            ? []
            : List<dynamic>.from(coreResponsibilities!.map((x) => x)),
        "reports_to": reportsTo,
        "organizational_level": organizationalLevel,
        "department": department,
        "team": team,
        "kpis": kpis == null
            ? []
            : List<dynamic>.from(kpis!.map((x) => x.toJson())),
        "decision_authority": decisionAuthority == null
            ? []
            : List<dynamic>.from(decisionAuthority!.map((x) => x)),
      };
}

class Kpi {
  String? name;
  String? description;
  String? formula;
  String? target;
  String? measurementFrequency;

  Kpi({
    this.name,
    this.description,
    this.formula,
    this.target,
    this.measurementFrequency,
  });

  Kpi copyWith({
    String? name,
    String? description,
    String? formula,
    String? target,
    String? measurementFrequency,
  }) =>
      Kpi(
        name: name ?? this.name,
        description: description ?? this.description,
        formula: formula ?? this.formula,
        target: target ?? this.target,
        measurementFrequency: measurementFrequency ?? this.measurementFrequency,
      );

  factory Kpi.fromJson(Map<String, dynamic> json) => Kpi(
        name: json["name"],
        description: json["description"],
        formula: json["formula"],
        target: json["target"],
        measurementFrequency: json["measurement_frequency"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "description": description,
        "formula": formula,
        "target": target,
        "measurement_frequency": measurementFrequency,
      };
}

class Manager {
  String? name;
  String? parentRole;
  List<String>? coreResponsibilities;
  String? reportsTo;
  String? organizationalLevel;
  String? department;
  String? team;
  List<Kpi>? kpis;
  List<String>? decisionAuthority;

  Manager({
    this.name,
    this.parentRole,
    this.coreResponsibilities,
    this.reportsTo,
    this.organizationalLevel,
    this.department,
    this.team,
    this.kpis,
    this.decisionAuthority,
  });

  Manager copyWith({
    String? name,
    String? parentRole,
    List<String>? coreResponsibilities,
    String? reportsTo,
    String? organizationalLevel,
    String? department,
    String? team,
    List<Kpi>? kpis,
    List<String>? decisionAuthority,
  }) =>
      Manager(
        name: name ?? this.name,
        parentRole: parentRole ?? this.parentRole,
        coreResponsibilities: coreResponsibilities ?? this.coreResponsibilities,
        reportsTo: reportsTo ?? this.reportsTo,
        organizationalLevel: organizationalLevel ?? this.organizationalLevel,
        department: department ?? this.department,
        team: team ?? this.team,
        kpis: kpis ?? this.kpis,
        decisionAuthority: decisionAuthority ?? this.decisionAuthority,
      );

  factory Manager.fromJson(Map<String, dynamic> json) => Manager(
        name: json["name"],
        parentRole: json["parent_role"],
        coreResponsibilities: json["core_responsibilities"] == null
            ? []
            : List<String>.from(json["core_responsibilities"]!.map((x) => x)),
        reportsTo: json["reports_to"],
        organizationalLevel: json["organizational_level"],
        department: json["department"],
        team: json["team"],
        kpis: json["kpis"] == null
            ? []
            : List<Kpi>.from(json["kpis"]!.map((x) => Kpi.fromJson(x))),
        decisionAuthority: json["decision_authority"] == null
            ? []
            : List<String>.from(json["decision_authority"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "parent_role": parentRole,
        "core_responsibilities": coreResponsibilities == null
            ? []
            : List<dynamic>.from(coreResponsibilities!.map((x) => x)),
        "reports_to": reportsTo,
        "organizational_level": organizationalLevel,
        "department": department,
        "team": team,
        "kpis": kpis == null
            ? []
            : List<dynamic>.from(kpis!.map((x) => x.toJson())),
        "decision_authority": decisionAuthority == null
            ? []
            : List<dynamic>.from(decisionAuthority!.map((x) => x)),
      };
}

class User {
  String? name;
  PersonalInformation? personalInformation;
  Authentication? authentication;
  RoleAssignments? roleAssignments;
  String? department;
  String? team;
  ReportingStructure? reportingStructure;
  AccessControl? accessControl;
  SystemPermissions? systemPermissions;
  ActivityTracking? activityTracking;
  Compliance? compliance;

  User({
    this.name,
    this.personalInformation,
    this.authentication,
    this.roleAssignments,
    this.department,
    this.team,
    this.reportingStructure,
    this.accessControl,
    this.systemPermissions,
    this.activityTracking,
    this.compliance,
  });

  User copyWith({
    String? name,
    PersonalInformation? personalInformation,
    Authentication? authentication,
    RoleAssignments? roleAssignments,
    String? department,
    String? team,
    ReportingStructure? reportingStructure,
    AccessControl? accessControl,
    SystemPermissions? systemPermissions,
    ActivityTracking? activityTracking,
    Compliance? compliance,
  }) =>
      User(
        name: name ?? this.name,
        personalInformation: personalInformation ?? this.personalInformation,
        authentication: authentication ?? this.authentication,
        roleAssignments: roleAssignments ?? this.roleAssignments,
        department: department ?? this.department,
        team: team ?? this.team,
        reportingStructure: reportingStructure ?? this.reportingStructure,
        accessControl: accessControl ?? this.accessControl,
        systemPermissions: systemPermissions ?? this.systemPermissions,
        activityTracking: activityTracking ?? this.activityTracking,
        compliance: compliance ?? this.compliance,
      );

  factory User.fromJson(Map<String, dynamic> json) => User(
        name: json["name"],
        personalInformation: json["personal_information"] == null
            ? null
            : PersonalInformation.fromJson(json["personal_information"]),
        authentication: json["authentication"] == null
            ? null
            : Authentication.fromJson(json["authentication"]),
        roleAssignments: json["role_assignments"] == null
            ? null
            : RoleAssignments.fromJson(json["role_assignments"]),
        department: json["department"],
        team: json["team"],
        reportingStructure: json["reporting_structure"] == null
            ? null
            : ReportingStructure.fromJson(json["reporting_structure"]),
        accessControl: json["access_control"] == null
            ? null
            : AccessControl.fromJson(json["access_control"]),
        systemPermissions: json["system_permissions"] == null
            ? null
            : SystemPermissions.fromJson(json["system_permissions"]),
        activityTracking: json["activity_tracking"] == null
            ? null
            : ActivityTracking.fromJson(json["activity_tracking"]),
        compliance: json["compliance"] == null
            ? null
            : Compliance.fromJson(json["compliance"]),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "personal_information": personalInformation?.toJson(),
        "authentication": authentication?.toJson(),
        "role_assignments": roleAssignments?.toJson(),
        "department": department,
        "team": team,
        "reporting_structure": reportingStructure?.toJson(),
        "access_control": accessControl?.toJson(),
        "system_permissions": systemPermissions?.toJson(),
        "activity_tracking": activityTracking?.toJson(),
        "compliance": compliance?.toJson(),
      };
}

class AccessControl {
  String? accountStatus;
  String? accessLevel;
  String? ipRestrictions;
  String? timeRestrictions;

  AccessControl({
    this.accountStatus,
    this.accessLevel,
    this.ipRestrictions,
    this.timeRestrictions,
  });

  AccessControl copyWith({
    String? accountStatus,
    String? accessLevel,
    String? ipRestrictions,
    String? timeRestrictions,
  }) =>
      AccessControl(
        accountStatus: accountStatus ?? this.accountStatus,
        accessLevel: accessLevel ?? this.accessLevel,
        ipRestrictions: ipRestrictions ?? this.ipRestrictions,
        timeRestrictions: timeRestrictions ?? this.timeRestrictions,
      );

  factory AccessControl.fromJson(Map<String, dynamic> json) => AccessControl(
        accountStatus: json["account_status"],
        accessLevel: json["access_level"],
        ipRestrictions: json["ip_restrictions"],
        timeRestrictions: json["time_restrictions"],
      );

  Map<String, dynamic> toJson() => {
        "account_status": accountStatus,
        "access_level": accessLevel,
        "ip_restrictions": ipRestrictions,
        "time_restrictions": timeRestrictions,
      };
}

class ActivityTracking {
  DateTime? accountCreated;
  DateTime? lastLogin;
  DateTime? lastActivity;
  String? sessionTimeout;

  ActivityTracking({
    this.accountCreated,
    this.lastLogin,
    this.lastActivity,
    this.sessionTimeout,
  });

  ActivityTracking copyWith({
    DateTime? accountCreated,
    DateTime? lastLogin,
    DateTime? lastActivity,
    String? sessionTimeout,
  }) =>
      ActivityTracking(
        accountCreated: accountCreated ?? this.accountCreated,
        lastLogin: lastLogin ?? this.lastLogin,
        lastActivity: lastActivity ?? this.lastActivity,
        sessionTimeout: sessionTimeout ?? this.sessionTimeout,
      );

  factory ActivityTracking.fromJson(Map<String, dynamic> json) =>
      ActivityTracking(
        accountCreated: json["account_created"] == null
            ? null
            : DateTime.parse(json["account_created"]),
        lastLogin: json["last_login"] == null
            ? null
            : DateTime.parse(json["last_login"]),
        lastActivity: json["last_activity"] == null
            ? null
            : DateTime.parse(json["last_activity"]),
        sessionTimeout: json["session_timeout"],
      );

  Map<String, dynamic> toJson() => {
        "account_created": accountCreated == null
            ? null
            : "${accountCreated!.year.toString().padLeft(4, '0')}-${accountCreated!.month.toString().padLeft(2, '0')}-${accountCreated!.day.toString().padLeft(2, '0')}",
        "last_login": lastLogin == null
            ? null
            : "${lastLogin!.year.toString().padLeft(4, '0')}-${lastLogin!.month.toString().padLeft(2, '0')}-${lastLogin!.day.toString().padLeft(2, '0')}",
        "last_activity": lastActivity == null
            ? null
            : "${lastActivity!.year.toString().padLeft(4, '0')}-${lastActivity!.month.toString().padLeft(2, '0')}-${lastActivity!.day.toString().padLeft(2, '0')}",
        "session_timeout": sessionTimeout,
      };
}

class Authentication {
  String? username;
  String? passwordPolicy;
  String? multiFactorAuthentication;
  DateTime? lastPasswordChange;

  Authentication({
    this.username,
    this.passwordPolicy,
    this.multiFactorAuthentication,
    this.lastPasswordChange,
  });

  Authentication copyWith({
    String? username,
    String? passwordPolicy,
    String? multiFactorAuthentication,
    DateTime? lastPasswordChange,
  }) =>
      Authentication(
        username: username ?? this.username,
        passwordPolicy: passwordPolicy ?? this.passwordPolicy,
        multiFactorAuthentication:
            multiFactorAuthentication ?? this.multiFactorAuthentication,
        lastPasswordChange: lastPasswordChange ?? this.lastPasswordChange,
      );

  factory Authentication.fromJson(Map<String, dynamic> json) => Authentication(
        username: json["username"],
        passwordPolicy: json["password_policy"],
        multiFactorAuthentication: json["multi-factor_authentication"],
        lastPasswordChange: json["last_password_change"] == null
            ? null
            : DateTime.parse(json["last_password_change"]),
      );

  Map<String, dynamic> toJson() => {
        "username": username,
        "password_policy": passwordPolicy,
        "multi-factor_authentication": multiFactorAuthentication,
        "last_password_change":
            "${lastPasswordChange!.year.toString().padLeft(4, '0')}-${lastPasswordChange!.month.toString().padLeft(2, '0')}-${lastPasswordChange!.day.toString().padLeft(2, '0')}",
      };
}

class Compliance {
  String? trainingStatus;
  String? agreementAcceptance;
  String? certificationStatus;

  Compliance({
    this.trainingStatus,
    this.agreementAcceptance,
    this.certificationStatus,
  });

  Compliance copyWith({
    String? trainingStatus,
    String? agreementAcceptance,
    String? certificationStatus,
  }) =>
      Compliance(
        trainingStatus: trainingStatus ?? this.trainingStatus,
        agreementAcceptance: agreementAcceptance ?? this.agreementAcceptance,
        certificationStatus: certificationStatus ?? this.certificationStatus,
      );

  factory Compliance.fromJson(Map<String, dynamic> json) => Compliance(
        trainingStatus: json["training_status"],
        agreementAcceptance: json["agreement_acceptance"],
        certificationStatus: json["certification_status"],
      );

  Map<String, dynamic> toJson() => {
        "training_status": trainingStatus,
        "agreement_acceptance": agreementAcceptance,
        "certification_status": certificationStatus,
      };
}

class PersonalInformation {
  String? fullName;
  String? email;
  String? phone;
  String? employeeId;

  PersonalInformation({
    this.fullName,
    this.email,
    this.phone,
    this.employeeId,
  });

  PersonalInformation copyWith({
    String? fullName,
    String? email,
    String? phone,
    String? employeeId,
  }) =>
      PersonalInformation(
        fullName: fullName ?? this.fullName,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        employeeId: employeeId ?? this.employeeId,
      );

  factory PersonalInformation.fromJson(Map<String, dynamic> json) =>
      PersonalInformation(
        fullName: json["full_name"],
        email: json["email"],
        phone: json["phone"],
        employeeId: json["employee_id"],
      );

  Map<String, dynamic> toJson() => {
        "full_name": fullName,
        "email": email,
        "phone": phone,
        "employee_id": employeeId,
      };
}

class ReportingStructure {
  String? reportsTo;
  List<String>? directReports;

  ReportingStructure({
    this.reportsTo,
    this.directReports,
  });

  ReportingStructure copyWith({
    String? reportsTo,
    List<String>? directReports,
  }) =>
      ReportingStructure(
        reportsTo: reportsTo ?? this.reportsTo,
        directReports: directReports ?? this.directReports,
      );

  factory ReportingStructure.fromJson(Map<String, dynamic> json) =>
      ReportingStructure(
        reportsTo: json["reports_to"],
        directReports: json["direct_reports"] == null
            ? []
            : List<String>.from(json["direct_reports"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "reports_to": reportsTo,
        "direct_reports": directReports == null
            ? []
            : List<dynamic>.from(directReports!.map((x) => x)),
      };
}

class RoleAssignments {
  String? primaryRole;
  List<String>? secondaryRoles;

  RoleAssignments({
    this.primaryRole,
    this.secondaryRoles,
  });

  RoleAssignments copyWith({
    String? primaryRole,
    List<String>? secondaryRoles,
  }) =>
      RoleAssignments(
        primaryRole: primaryRole ?? this.primaryRole,
        secondaryRoles: secondaryRoles ?? this.secondaryRoles,
      );

  factory RoleAssignments.fromJson(Map<String, dynamic> json) =>
      RoleAssignments(
        primaryRole: json["primary_role"],
        secondaryRoles: json["secondary_roles"] == null
            ? []
            : List<String>.from(json["secondary_roles"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "primary_role": primaryRole,
        "secondary_roles": secondaryRoles == null
            ? []
            : List<dynamic>.from(secondaryRoles!.map((x) => x)),
      };
}

class SystemPermissions {
  String? dataAccessScope;
  List<String>? specialPermissions;

  SystemPermissions({
    this.dataAccessScope,
    this.specialPermissions,
  });

  SystemPermissions copyWith({
    String? dataAccessScope,
    List<String>? specialPermissions,
  }) =>
      SystemPermissions(
        dataAccessScope: dataAccessScope ?? this.dataAccessScope,
        specialPermissions: specialPermissions ?? this.specialPermissions,
      );

  factory SystemPermissions.fromJson(Map<String, dynamic> json) =>
      SystemPermissions(
        dataAccessScope: json["data_access_scope"],
        specialPermissions: json["special_permissions"] == null
            ? []
            : List<String>.from(json["special_permissions"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "data_access_scope": dataAccessScope,
        "special_permissions": specialPermissions == null
            ? []
            : List<dynamic>.from(specialPermissions!.map((x) => x)),
      };
}

class ValidationError {
  String? type;
  String? message;
  String? location;

  ValidationError({
    this.type,
    this.message,
    this.location,
  });

  ValidationError copyWith({
    String? type,
    String? message,
    String? location,
  }) =>
      ValidationError(
        type: type ?? this.type,
        message: message ?? this.message,
        location: location ?? this.location,
      );

  factory ValidationError.fromJson(Map<String, dynamic> json) =>
      ValidationError(
        type: json["type"],
        message: json["message"],
        location: json["location"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "message": message,
        "location": location,
      };
}
