import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../utils/callback_interpreter.dart';

class DecimalWidget extends StatefulWidget {
  // Configurable properties
  final double initialValue;
  final double defaultValue; // Value to use when input is cleared or empty
  final double minValue;
  final double maxValue;
  final int decimalPlaces;
  final bool allowNegative;
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final bool isReadOnly;
  final bool isDisabled;
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final bool showPrefix;
  final bool showSuffix;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final String? unit;
  final bool showUnit;
  final bool unitOnLeft;
  final Function(double)? onChanged;
  final Function(double)? onSubmitted;
  final TextAlign textAlign;
  final bool autofocus;
  final FocusNode? focusNode;
  final TextInputAction textInputAction;
  final bool enableInteractiveSelection;
  final bool obscureText;
  final bool autocorrect;
  final bool enableSuggestions;
  final int? maxLength;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final bool useMaterial3;
  final bool hasAnimation;
  final String? prefixText;
  final String? suffixText;
  final bool showIncrementButtons;
  final double incrementAmount;
  final String? tooltip;
  final bool showClearButton;
  final bool showCopyButton;
  final bool showPasteButton;
  final bool showValidationIcon;
  final bool showThousandsSeparator;
  final String thousandsSeparator;
  final String decimalSeparator;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  const DecimalWidget({
    super.key,
    this.initialValue = 0.0,
    this.defaultValue = 0.0,
    this.minValue = double.negativeInfinity,
    this.maxValue = double.infinity,
    this.decimalPlaces = 2,
    this.allowNegative = true,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.showPrefix = false,
    this.showSuffix = false,
    this.prefixIcon,
    this.suffixIcon,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.unit,
    this.showUnit = false,
    this.unitOnLeft = false,
    this.onChanged,
    this.onSubmitted,
    this.textAlign = TextAlign.start,
    this.autofocus = false,
    this.focusNode,
    this.textInputAction = TextInputAction.done,
    this.enableInteractiveSelection = true,
    this.obscureText = false,
    this.autocorrect = false,
    this.enableSuggestions = false,
    this.maxLength,
    this.hasShadow = false,
    this.elevation = 0.0,
    this.isDarkTheme = false,
    this.useMaterial3 = false,
    this.hasAnimation = false,
    this.prefixText,
    this.suffixText,
    this.showIncrementButtons = false,
    this.incrementAmount = 1.0,
    this.tooltip,
    this.showClearButton = false,
    this.showCopyButton = false,
    this.showPasteButton = false,
    this.showValidationIcon = false,
    this.showThousandsSeparator = false,
    this.thousandsSeparator = ',',
    this.decimalSeparator = '.',
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
  });

  /// Creates a DecimalWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the DecimalWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialValue": 42.5,
  ///   "minValue": 0,
  ///   "maxValue": 100,
  ///   "decimalPlaces": 2,
  ///   "allowNegative": false,
  ///   "textColor": "blue",
  ///   "backgroundColor": "white",
  ///   "showIncrementButtons": true
  /// }
  /// ```
  factory DecimalWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'amber': return Colors.amber;
          case 'cyan': return Colors.cyan;
          case 'indigo': return Colors.indigo;
          case 'lime': return Colors.lime;
          case 'teal': return Colors.teal;
          default: return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse text alignment
    TextAlign parseTextAlign(dynamic alignValue) {
      if (alignValue == null) return TextAlign.start;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'center': return TextAlign.center;
          case 'end':
          case 'right': return TextAlign.end;
          case 'start':
          case 'left': return TextAlign.start;
          case 'justify': return TextAlign.justify;
          default: return TextAlign.start;
        }
      }

      return TextAlign.start;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.normal;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold': return FontWeight.bold;
          case 'normal': return FontWeight.normal;
          case 'light': return FontWeight.w300;
          default: return FontWeight.normal;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100: return FontWeight.w100;
          case 200: return FontWeight.w200;
          case 300: return FontWeight.w300;
          case 400: return FontWeight.w400;
          case 500: return FontWeight.w500;
          case 600: return FontWeight.w600;
          case 700: return FontWeight.w700;
          case 800: return FontWeight.w800;
          case 900: return FontWeight.w900;
          default: return FontWeight.normal;
        }
      } else if (weightValue is bool && weightValue) {
        return FontWeight.bold;
      }

      return FontWeight.normal;
    }

    // Parse icon data
    IconData? parseIconData(dynamic iconValue) {
      if (iconValue == null) return null;

      if (iconValue is String) {
        switch (iconValue.toLowerCase()) {
          case 'add': return Icons.add;
          case 'remove': return Icons.remove;
          case 'clear': return Icons.clear;
          case 'delete': return Icons.delete;
          case 'edit': return Icons.edit;
          case 'save': return Icons.save;
          case 'check': return Icons.check;
          case 'close': return Icons.close;
          case 'search': return Icons.search;
          case 'settings': return Icons.settings;
          case 'info': return Icons.info;
          case 'warning': return Icons.warning;
          case 'error': return Icons.error;
          case 'help': return Icons.help;
          case 'person': return Icons.person;
          case 'home': return Icons.home;
          case 'menu': return Icons.menu;
          case 'more': return Icons.more_vert;
          case 'more_horiz': return Icons.more_horiz;
          case 'refresh': return Icons.refresh;
          case 'dollar':
          case 'money': return Icons.attach_money;
          case 'euro': return Icons.euro;
          case 'percent': return Icons.percent;
          case 'calculator': return Icons.calculate;
          case 'numbers': return Icons.numbers;
          case 'copy': return Icons.copy;
          case 'paste': return Icons.paste;
          case 'calendar': return Icons.calendar_today;
          case 'time': return Icons.access_time;
          case 'date': return Icons.date_range;
          default: return null;
        }
      }

      return null;
    }

    // Parse text input action
    TextInputAction parseTextInputAction(dynamic actionValue) {
      if (actionValue == null) return TextInputAction.done;

      if (actionValue is String) {
        switch (actionValue.toLowerCase()) {
          case 'done': return TextInputAction.done;
          case 'go': return TextInputAction.go;
          case 'search': return TextInputAction.search;
          case 'send': return TextInputAction.send;
          case 'next': return TextInputAction.next;
          case 'previous': return TextInputAction.previous;
          case 'continue': return TextInputAction.continueAction;
          case 'join': return TextInputAction.join;
          case 'route': return TextInputAction.route;
          case 'emergencycall': return TextInputAction.emergencyCall;
          case 'newline': return TextInputAction.newline;
          case 'none': return TextInputAction.none;
          case 'unspecified': return TextInputAction.unspecified;
          default: return TextInputAction.done;
        }
      }

      return TextInputAction.done;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks = jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onChanged'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onChanged'] = json['onChanged'];
      useJsonCallbacks = true;
    }

    if (json['onSubmitted'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onSubmitted'] = json['onSubmitted'];
      useJsonCallbacks = true;
    }

    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    if (json['onClear'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onClear'] = json['onClear'];
      useJsonCallbacks = true;
    }

    if (json['onIncrement'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onIncrement'] = json['onIncrement'];
      useJsonCallbacks = true;
    }

    if (json['onDecrement'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDecrement'] = json['onDecrement'];
      useJsonCallbacks = true;
    }

    // Create the widget with all properties from JSON
    return DecimalWidget(
      initialValue: json['initialValue'] != null ? (json['initialValue'] as num).toDouble() : 0.0,
      defaultValue: json['defaultValue'] != null ? (json['defaultValue'] as num).toDouble() : 0.0,
      minValue: json['minValue'] != null ? (json['minValue'] as num).toDouble() : double.negativeInfinity,
      maxValue: json['maxValue'] != null ? (json['maxValue'] as num).toDouble() : double.infinity,
      decimalPlaces: json['decimalPlaces'] as int? ?? 2,
      allowNegative: json['allowNegative'] as bool? ?? true,
      textColor: parseColor(json['textColor']) ?? Colors.black,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      borderColor: parseColor(json['borderColor']) ?? Color(0xFFCCCCCC),
      borderWidth: json['borderWidth'] != null ? (json['borderWidth'] as num).toDouble() : 1.0,
      borderRadius: json['borderRadius'] != null ? (json['borderRadius'] as num).toDouble() : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      showPrefix: json['showPrefix'] as bool? ?? false,
      showSuffix: json['showSuffix'] as bool? ?? false,
      prefixIcon: parseIconData(json['prefixIcon']),
      suffixIcon: parseIconData(json['suffixIcon']),
      fontSize: json['fontSize'] != null ? (json['fontSize'] as num).toDouble() : 16.0,
      fontWeight: parseFontWeight(json['fontWeight']),
      isCompact: json['isCompact'] as bool? ?? false,
      unit: json['unit'] as String?,
      showUnit: json['showUnit'] as bool? ?? false,
      unitOnLeft: json['unitOnLeft'] as bool? ?? false,
      textAlign: parseTextAlign(json['textAlign']),
      autofocus: json['autofocus'] as bool? ?? false,
      textInputAction: parseTextInputAction(json['textInputAction']),
      enableInteractiveSelection: json['enableInteractiveSelection'] as bool? ?? true,
      obscureText: json['obscureText'] as bool? ?? false,
      autocorrect: json['autocorrect'] as bool? ?? false,
      enableSuggestions: json['enableSuggestions'] as bool? ?? false,
      maxLength: json['maxLength'] as int?,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: json['elevation'] != null ? (json['elevation'] as num).toDouble() : 0.0,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      useMaterial3: json['useMaterial3'] as bool? ?? false,
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      prefixText: json['prefixText'] as String?,
      suffixText: json['suffixText'] as String?,
      showIncrementButtons: json['showIncrementButtons'] as bool? ?? false,
      incrementAmount: json['incrementAmount'] != null ? (json['incrementAmount'] as num).toDouble() : 1.0,
      tooltip: json['tooltip'] as String?,
      showClearButton: json['showClearButton'] as bool? ?? false,
      showCopyButton: json['showCopyButton'] as bool? ?? false,
      showPasteButton: json['showPasteButton'] as bool? ?? false,
      showValidationIcon: json['showValidationIcon'] as bool? ?? false,
      showThousandsSeparator: json['showThousandsSeparator'] as bool? ?? false,
      thousandsSeparator: json['thousandsSeparator'] as String? ?? ',',
      decimalSeparator: json['decimalSeparator'] as String? ?? '.',
      // Advanced interaction properties
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState: json['callbackState'] != null ? Map<String, dynamic>.from(json['callbackState'] as Map) : {},
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
    );
  }

  /// Converts the widget configuration to a JSON map
  ///
  /// This method allows for serializing the widget's configuration to JSON,
  /// which can be useful for saving configurations or sending them to a server.
  Map<String, dynamic> toJson() {
    // Convert color to hex string
    String? colorToHex(Color? color) {
      if (color == null) return null;

      // Convert to hex format
      final r = (color.r * 255).round();
      final g = (color.g * 255).round();
      final b = (color.b * 255).round();
      return '#${r.toRadixString(16).padLeft(2, '0')}${g.toRadixString(16).padLeft(2, '0')}${b.toRadixString(16).padLeft(2, '0')}';
    }

    // Convert text alignment to string
    String textAlignToString(TextAlign align) {
      switch (align) {
        case TextAlign.center: return 'center';
        case TextAlign.end: return 'end';
        case TextAlign.left: return 'left';
        case TextAlign.right: return 'right';
        case TextAlign.justify: return 'justify';
        case TextAlign.start: return 'start';
        default: return 'start'; // Handle any future additions
      }
    }

    // Convert font weight to int
    int fontWeightToInt(FontWeight weight) {
      if (weight == FontWeight.w100) return 100;
      if (weight == FontWeight.w200) return 200;
      if (weight == FontWeight.w300) return 300;
      if (weight == FontWeight.w400 || weight == FontWeight.normal) return 400;
      if (weight == FontWeight.w500) return 500;
      if (weight == FontWeight.w600) return 600;
      if (weight == FontWeight.w700 || weight == FontWeight.bold) return 700;
      if (weight == FontWeight.w800) return 800;
      if (weight == FontWeight.w900) return 900;
      return 400;
    }

    // Convert icon data to string
    String? iconDataToString(IconData? icon) {
      if (icon == null) return null;

      if (icon == Icons.add) return 'add';
      if (icon == Icons.remove) return 'remove';
      if (icon == Icons.clear) return 'clear';
      if (icon == Icons.delete) return 'delete';
      if (icon == Icons.edit) return 'edit';
      if (icon == Icons.save) return 'save';
      if (icon == Icons.check) return 'check';
      if (icon == Icons.close) return 'close';
      if (icon == Icons.search) return 'search';
      if (icon == Icons.settings) return 'settings';
      if (icon == Icons.info) return 'info';
      if (icon == Icons.warning) return 'warning';
      if (icon == Icons.error) return 'error';
      if (icon == Icons.help) return 'help';
      if (icon == Icons.person) return 'person';
      if (icon == Icons.home) return 'home';
      if (icon == Icons.menu) return 'menu';
      if (icon == Icons.more_vert) return 'more';
      if (icon == Icons.more_horiz) return 'more_horiz';
      if (icon == Icons.refresh) return 'refresh';
      if (icon == Icons.attach_money) return 'money';
      if (icon == Icons.euro) return 'euro';
      if (icon == Icons.percent) return 'percent';
      if (icon == Icons.calculate) return 'calculator';
      if (icon == Icons.numbers) return 'numbers';
      if (icon == Icons.copy) return 'copy';
      if (icon == Icons.paste) return 'paste';
      if (icon == Icons.calendar_today) return 'calendar';
      if (icon == Icons.access_time) return 'time';
      if (icon == Icons.date_range) return 'date';

      return null;
    }

    // Convert text input action to string
    String textInputActionToString(TextInputAction action) {
      switch (action) {
        case TextInputAction.done: return 'done';
        case TextInputAction.go: return 'go';
        case TextInputAction.search: return 'search';
        case TextInputAction.send: return 'send';
        case TextInputAction.next: return 'next';
        case TextInputAction.previous: return 'previous';
        case TextInputAction.continueAction: return 'continue';
        case TextInputAction.join: return 'join';
        case TextInputAction.route: return 'route';
        case TextInputAction.emergencyCall: return 'emergencycall';
        case TextInputAction.newline: return 'newline';
        case TextInputAction.none: return 'none';
        case TextInputAction.unspecified: return 'unspecified';
        default: return 'done'; // Handle any future additions
      }
    }

    // Create the JSON map
    final Map<String, dynamic> json = {
      'initialValue': initialValue,
      'defaultValue': defaultValue,
      'minValue': minValue,
      'maxValue': maxValue,
      'decimalPlaces': decimalPlaces,
      'allowNegative': allowNegative,
      'textColor': colorToHex(textColor),
      'backgroundColor': colorToHex(backgroundColor),
      'borderColor': colorToHex(borderColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'isReadOnly': isReadOnly,
      'isDisabled': isDisabled,
      'label': label,
      'hint': hint,
      'helperText': helperText,
      'errorText': errorText,
      'showPrefix': showPrefix,
      'showSuffix': showSuffix,
      'prefixIcon': iconDataToString(prefixIcon),
      'suffixIcon': iconDataToString(suffixIcon),
      'fontSize': fontSize,
      'fontWeight': fontWeightToInt(fontWeight),
      'isCompact': isCompact,
      'unit': unit,
      'showUnit': showUnit,
      'unitOnLeft': unitOnLeft,
      'textAlign': textAlignToString(textAlign),
      'autofocus': autofocus,
      'textInputAction': textInputActionToString(textInputAction),
      'enableInteractiveSelection': enableInteractiveSelection,
      'obscureText': obscureText,
      'autocorrect': autocorrect,
      'enableSuggestions': enableSuggestions,
      'maxLength': maxLength,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isDarkTheme': isDarkTheme,
      'useMaterial3': useMaterial3,
      'hasAnimation': hasAnimation,
      'prefixText': prefixText,
      'suffixText': suffixText,
      'showIncrementButtons': showIncrementButtons,
      'incrementAmount': incrementAmount,
      'tooltip': tooltip,
      'showClearButton': showClearButton,
      'showCopyButton': showCopyButton,
      'showPasteButton': showPasteButton,
      'showValidationIcon': showValidationIcon,
      'showThousandsSeparator': showThousandsSeparator,
      'thousandsSeparator': thousandsSeparator,
      'decimalSeparator': decimalSeparator,
      'hoverColor': colorToHex(hoverColor),
      'focusColor': colorToHex(focusColor),
      'enableFeedback': enableFeedback,
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonValidation': useJsonValidation,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
    };

    // Add callbacks if they exist
    if (jsonCallbacks != null && jsonCallbacks!.isNotEmpty) {
      json['callbacks'] = jsonCallbacks;
    }

    return json;
  }

  @override
  State<DecimalWidget> createState() => _DecimalWidgetState();
}

class _DecimalWidgetState extends State<DecimalWidget> with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late FocusNode _focusNode;
  double _currentValue = 0.0;
  bool _hasFocus = false;
  bool _isHovered = false;

  // Map to store dynamic state for callbacks
  Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;
  bool _isValid = true;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initialValue;
    _controller = TextEditingController(text: _formatNumber(_currentValue));
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    _callbackState = widget.callbackState != null
        ? Map<String, dynamic>.from(widget.callbackState!)
        : {};

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Execute onInit callback if defined in JSON
    _executeJsonCallback('onInit');
  }

  @override
  void dispose() {
    _controller.dispose();
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
      if (_hasFocus && widget.hasAnimation) {
        _animationController.forward();
      } else if (widget.hasAnimation) {
        _animationController.reverse();
      }
    });

    // Execute onFocus callback if defined in JSON
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onFocus')) {
      _executeJsonCallback('onFocus', _hasFocus);
    }
  }

  /// Executes a callback defined in JSON
  ///
  /// This method interprets and executes a callback defined in the JSON configuration.
  /// It supports various callback types and provides access to the current state.
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      _callbackState['currentValue'] = _currentValue;

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue = _currentValue;
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the current value
  ///
  /// This method applies validation rules defined in the JSON configuration.
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    // Example: Apply min/max validation
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply min value validation
        if (rules.containsKey('min') && rules['min'] is num) {
          final minValue = (rules['min'] as num).toDouble();
          if (_currentValue < minValue) {
            _isValid = false;
            return;
          }
        }

        // Apply max value validation
        if (rules.containsKey('max') && rules['max'] is num) {
          final maxValue = (rules['max'] as num).toDouble();
          if (_currentValue > maxValue) {
            _isValid = false;
            return;
          }
        }

        // Apply custom validation
        if (rules.containsKey('custom') && rules['custom'] is String) {
          final customRule = rules['custom'] as String;

          // Example: Check if value is even
          if (customRule == 'even' && _currentValue % 2 != 0) {
            _isValid = false;
            return;
          }

          // Example: Check if value is odd
          if (customRule == 'odd' && _currentValue % 2 == 0) {
            _isValid = false;
            return;
          }

          // Example: Check if value is integer
          if (customRule == 'integer' && _currentValue != _currentValue.roundToDouble()) {
            _isValid = false;
            return;
          }
        }
      }
    }

    _isValid = true;
  }

  /// Applies JSON styling to the widget
  ///
  /// This method applies styling rules defined in the JSON configuration.
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current value
  ///
  /// This method applies formatting rules defined in the JSON configuration.
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  String _formatNumber(double value) {
    // Format the number based on decimal places
    String formatted = value.toStringAsFixed(widget.decimalPlaces);

    // Apply thousands separator if needed
    if (widget.showThousandsSeparator) {
      List<String> parts = formatted.split('.');
      String integerPart = parts[0];
      String decimalPart = parts.length > 1 ? parts[1] : '';

      // Add thousands separator
      final RegExp reg = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
      integerPart = integerPart.replaceAllMapped(reg, (Match match) => '${match[1]}${widget.thousandsSeparator}');

      // Combine parts with decimal separator
      formatted = integerPart;
      if (decimalPart.isNotEmpty) {
        formatted += '${widget.decimalSeparator}$decimalPart';
      }
    }

    return formatted;
  }

  double _parseFormattedValue(String formattedValue) {
    // Remove thousands separators and handle decimal separator
    String cleanValue = formattedValue;

    if (widget.showThousandsSeparator) {
      cleanValue = cleanValue.replaceAll(widget.thousandsSeparator, '');
    }

    if (widget.decimalSeparator != '.') {
      cleanValue = cleanValue.replaceAll(widget.decimalSeparator, '.');
    }

    cleanValue = cleanValue.trim();

    // Handle empty or invalid input
    if (cleanValue.isEmpty) {
      return widget.defaultValue;
    }

    try {
      return double.parse(cleanValue);
    } catch (e) {
      return _currentValue; // Return the previous value if parsing fails
    }
  }

  void _handleValueChanged(String text) {
    final double parsedValue = _parseFormattedValue(text);

    // Apply min/max constraints
    double constrainedValue = parsedValue;
    if (parsedValue < widget.minValue) {
      constrainedValue = widget.minValue;
      _isValid = false;
    } else if (parsedValue > widget.maxValue) {
      constrainedValue = widget.maxValue;
      _isValid = false;
    } else {
      _isValid = true;
    }

    // Don't allow negative values if not permitted
    if (!widget.allowNegative && constrainedValue < 0) {
      constrainedValue = 0;
      _isValid = false;
    }

    setState(() {
      _currentValue = constrainedValue;
    });

    // Apply JSON validation if enabled
    if (widget.useJsonValidation) {
      _applyJsonValidation();
    }

    // Call standard callback
    if (widget.onChanged != null) {
      widget.onChanged!(_currentValue);
    }

    // Execute JSON callback
    _executeJsonCallback('onChanged', _currentValue);
  }

  void _increment() {
    // Execute onBeforeIncrement callback if defined in JSON
    _executeJsonCallback('onBeforeIncrement');

    final newValue = _currentValue + widget.incrementAmount;
    if (newValue <= widget.maxValue) {
      setState(() {
        _currentValue = newValue;
        _controller.text = _formatNumber(_currentValue);
      });

      // Apply JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Call standard callback
      if (widget.onChanged != null) {
        widget.onChanged!(_currentValue);
      }

      // Execute JSON callbacks
      _executeJsonCallback('onChanged', _currentValue);
      _executeJsonCallback('onIncrement', _currentValue);
    }
  }

  void _decrement() {
    // Execute onBeforeDecrement callback if defined in JSON
    _executeJsonCallback('onBeforeDecrement');

    final newValue = _currentValue - widget.incrementAmount;
    if (newValue >= widget.minValue && (widget.allowNegative || newValue >= 0)) {
      setState(() {
        _currentValue = newValue;
        _controller.text = _formatNumber(_currentValue);
      });

      // Apply JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Call standard callback
      if (widget.onChanged != null) {
        widget.onChanged!(_currentValue);
      }

      // Execute JSON callbacks
      _executeJsonCallback('onChanged', _currentValue);
      _executeJsonCallback('onDecrement', _currentValue);
    }
  }

  void _clearValue() {
    // Execute onBeforeClear callback if defined in JSON
    _executeJsonCallback('onBeforeClear');

    setState(() {
      _currentValue = widget.defaultValue;
      _controller.text = _formatNumber(_currentValue);
    });

    // Apply JSON validation if enabled
    if (widget.useJsonValidation) {
      _applyJsonValidation();
    }

    // Call standard callback
    if (widget.onChanged != null) {
      widget.onChanged!(_currentValue);
    }

    // Execute JSON callbacks
    _executeJsonCallback('onChanged', _currentValue);
    _executeJsonCallback('onClear', _currentValue);
  }

  void _copyValue() {
    // Execute onBeforeCopy callback if defined in JSON
    _executeJsonCallback('onBeforeCopy');

    Clipboard.setData(ClipboardData(text: _currentValue.toString()));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Value copied to clipboard')),
    );

    // Execute onCopy callback if defined in JSON
    _executeJsonCallback('onCopy', _currentValue);
  }

  Future<void> _pasteValue() async {
    // Execute onBeforePaste callback if defined in JSON
    _executeJsonCallback('onBeforePaste');

    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData != null && clipboardData.text != null) {
      try {
        final double pastedValue = double.parse(clipboardData.text!);
        setState(() {
          _currentValue = pastedValue;
          _controller.text = _formatNumber(_currentValue);
        });

        // Apply JSON validation if enabled
        if (widget.useJsonValidation) {
          _applyJsonValidation();
        }

        // Call standard callback
        if (widget.onChanged != null) {
          widget.onChanged!(_currentValue);
        }

        // Execute JSON callbacks
        _executeJsonCallback('onChanged', _currentValue);
        _executeJsonCallback('onPaste', _currentValue);
      } catch (e) {
        // Ignore if the clipboard doesn't contain a valid number
        // Execute onPasteError callback if defined in JSON
        _executeJsonCallback('onPasteError', e.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Create the input formatter for decimal
    final List<TextInputFormatter> inputFormatters = [
      FilteringTextInputFormatter.allow(RegExp(r'[0-9${widget.decimalSeparator}${widget.thousandsSeparator}\-\+]')),
    ];

    if (!widget.allowNegative) {
      inputFormatters.add(FilteringTextInputFormatter.deny(RegExp(r'[\-]')));
    }

    if (widget.maxLength != null) {
      inputFormatters.add(LengthLimitingTextInputFormatter(widget.maxLength!));
    }

    // Create the text field with hover functionality
    Widget textField = MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
        // Call the onHover callback if provided
        if (widget.onHover != null) {
          widget.onHover!(true);
        }
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
        // Call the onHover callback if provided
        if (widget.onHover != null) {
          widget.onHover!(false);
        }
      },
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        keyboardType: const TextInputType.numberWithOptions(decimal: true, signed: true),
        textAlign: widget.textAlign,
        style: TextStyle(
          color: widget.isDisabled ? Colors.grey : widget.textColor,
          fontSize: widget.fontSize,
          fontWeight: widget.fontWeight,
        ),
        decoration: InputDecoration(
          hintText: widget.hint,
          helperText: widget.helperText,
          errorText: widget.errorText,
          filled: true,
          fillColor: widget.isDisabled ? Colors.grey.shade200 : widget.backgroundColor,
          prefixIcon: widget.showPrefix && widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
          suffixIcon: _buildSuffixIcon(),
          prefixText: widget.unitOnLeft && widget.showUnit && widget.unit != null ? widget.unit : widget.prefixText,
          suffixText: !widget.unitOnLeft && widget.showUnit && widget.unit != null ? widget.unit : widget.suffixText,
          border: widget.hasBorder
              ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  borderSide: BorderSide(
                    color: widget.borderColor,
                    width: widget.borderWidth,
                  ),
                )
              : InputBorder.none,
          enabledBorder: widget.hasBorder
              ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  borderSide: BorderSide(
                    color: _isHovered
                        ? (widget.hoverColor ?? const Color(0xFF0058FF)) // Use hoverColor or default pink
                        : widget.borderColor,
                    width: _isHovered ? 1.0 : widget.borderWidth,
                  ),
                )
              : null,
          focusedBorder: widget.hasBorder
              ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  borderSide: BorderSide(
                    color: widget.focusColor ?? const Color(0xFF0058FF), // Use focusColor or default pink
                    width: 1.0,
                  ),
                )
              : null,
          disabledBorder: widget.hasBorder
              ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  borderSide: BorderSide(
                    color: Colors.grey.shade400,
                    width: widget.borderWidth,
                  ),
                )
              : null,
        ),
        inputFormatters: inputFormatters,
        enabled: !widget.isDisabled && !widget.isReadOnly,
        readOnly: widget.isReadOnly,
        autofocus: widget.autofocus,
        textInputAction: widget.textInputAction,
        enableInteractiveSelection: widget.enableInteractiveSelection,
        obscureText: widget.obscureText,
        autocorrect: widget.autocorrect,
        enableSuggestions: widget.enableSuggestions,
        onChanged: (value) {
          _handleValueChanged(value);
        },
        onSubmitted: (value) {
          // Call standard callback
          if (widget.onSubmitted != null) {
            widget.onSubmitted!(_currentValue);
          }

          // Execute JSON callback
          _executeJsonCallback('onSubmitted', _currentValue);
        },
        onTap: () {
          if (widget.isCompact) {
            // Select all text when tapped in compact mode
            _controller.selection = TextSelection(
              baseOffset: 0,
              extentOffset: _controller.text.length,
            );
          }

          // Call standard callback
          if (widget.onTap != null) {
            widget.onTap!();
          }

          // Execute JSON callback
          _executeJsonCallback('onTap');
        },
      ),
    );

    // Apply animation if enabled
    if (widget.hasAnimation) {
      textField = AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: child,
          );
        },
        child: textField,
      );
    }

    // Apply tooltip if provided
    if (widget.tooltip != null) {
      textField = Tooltip(
        message: widget.tooltip!,
        child: textField,
      );
    }

    // Apply shadow if enabled
    if (widget.hasShadow) {
      return Card(
        elevation: widget.elevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        color: Colors.transparent,
        shadowColor: Colors.black.withAlpha(76),
        child: textField,
      );
    }

    return textField;
  }

  Widget? _buildSuffixIcon() {
    if (widget.showSuffix && widget.suffixIcon != null) {
      return Icon(widget.suffixIcon);
    }

    if (widget.showValidationIcon) {
      return Icon(
        _isValid ? Icons.check_circle : Icons.error,
        color: _isValid ? Colors.green : Colors.red,
      );
    }

    if (widget.showClearButton || widget.showCopyButton || widget.showPasteButton) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showClearButton)
            IconButton(
              icon: const Icon(Icons.clear, size: 18),
              onPressed: widget.isDisabled || widget.isReadOnly ? null : _clearValue,
              tooltip: 'Clear',
              constraints: const BoxConstraints(maxHeight: 32, maxWidth: 32),
              padding: EdgeInsets.zero,
            ),
          if (widget.showCopyButton)
            IconButton(
              icon: const Icon(Icons.copy, size: 18),
              onPressed: widget.isDisabled ? null : _copyValue,
              tooltip: 'Copy',
              constraints: const BoxConstraints(maxHeight: 32, maxWidth: 32),
              padding: EdgeInsets.zero,
            ),
          if (widget.showPasteButton)
            IconButton(
              icon: const Icon(Icons.paste, size: 18),
              onPressed: widget.isDisabled || widget.isReadOnly ? null : _pasteValue,
              tooltip: 'Paste',
              constraints: const BoxConstraints(maxHeight: 32, maxWidth: 32),
              padding: EdgeInsets.zero,
            ),
        ],
      );
    }

    return null;
  }
}
