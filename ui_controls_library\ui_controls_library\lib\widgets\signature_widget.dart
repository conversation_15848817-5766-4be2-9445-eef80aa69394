import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

/// Enum for signature widget states
enum SignatureWidgetState {
  defaultState,
  hover,
  pressed,
  active,
}

/// A customizable signature widget that allows users to draw signatures.
///
/// This widget provides a canvas for drawing signatures with various customization options
/// including pen color, pen width, background color, border style, and more.
class SignatureWidget extends StatefulWidget {
  /// The color of the pen used for drawing
  final Color penColor;

  /// The width of the pen stroke
  final double penWidth;

  /// The background color of the signature pad
  final Color backgroundColor;

  /// Whether to show a border around the signature pad
  final bool showBorder;

  /// The color of the border
  final Color? borderColor;

  /// The width of the border
  final double borderWidth;

  /// The radius of the border corners
  final double borderRadius;

  /// The height of the signature pad
  final double height;

  /// The width of the signature pad
  final double width;

  /// The padding around the signature pad
  final EdgeInsetsGeometry padding;

  /// The margin around the signature pad
  final EdgeInsetsGeometry margin;

  /// Whether to show a clear button
  final bool showClearButton;

  /// The text for the clear button
  final String clearButtonText;

  /// The color of the clear button
  final Color? clearButtonColor;

  /// The text color of the clear button
  final Color? clearButtonTextColor;

  /// Whether to show a save button
  final bool showSaveButton;

  /// The text for the save button
  final String saveButtonText;

  /// The color of the save button
  final Color? saveButtonColor;

  /// The text color of the save button
  final Color? saveButtonTextColor;

  /// Whether to show a hint text when the signature pad is empty
  final bool showHint;

  /// The hint text to display
  final String hintText;

  /// The style of the hint text
  final TextStyle? hintTextStyle;

  /// Whether to show a label above the signature pad
  final bool showLabel;

  /// The label text to display
  final String labelText;

  /// The style of the label text
  final TextStyle? labelTextStyle;

  /// Whether to show a shadow around the signature pad
  final bool showShadow;

  /// The color of the shadow
  final Color? shadowColor;

  /// The elevation of the shadow
  final double shadowElevation;

  /// Whether the signature pad is disabled
  final bool isDisabled;

  /// Whether to show a dotted line at the bottom of the signature pad
  final bool showDottedLine;

  /// The color of the dotted line
  final Color? dottedLineColor;

  /// The width of the dotted line
  final double dottedLineWidth;

  /// The dash pattern of the dotted line
  final List<double>? dottedLineDashPattern;

  /// Whether to show a button to export the signature as an image
  final bool showExportButton;

  /// The text for the export button
  final String exportButtonText;

  /// The color of the export button
  final Color? exportButtonColor;

  /// The text color of the export button
  final Color? exportButtonTextColor;

  /// The format of the exported image
  final ui.ImageByteFormat exportFormat;

  /// The callback when the signature is cleared
  final VoidCallback? onClear;

  /// The callback when the signature is saved
  final Function(Uint8List)? onSave;

  /// The callback when the signature is changed
  final VoidCallback? onChanged;

  /// The callback when the signature is exported
  final Function(Uint8List)? onExport;

  /// JSON configuration for the widget
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON configuration
  final bool useJsonConfig;

  /// JSON callbacks
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// Creates a signature widget.
  const SignatureWidget({
    super.key,
    this.penColor = Colors.black,
    this.penWidth = 3.0,
    this.backgroundColor = Colors.white,
    this.showBorder = true,
    this.borderColor,
    this.borderWidth = 1.0,
    this.borderRadius = 8.0,
    this.height = 200.0,
    this.width = double.infinity,
    this.padding = const EdgeInsets.all(8.0),
    this.margin = const EdgeInsets.all(0.0),
    this.showClearButton = true,
    this.clearButtonText = 'Clear',
    this.clearButtonColor,
    this.clearButtonTextColor,
    this.showSaveButton = false,
    this.saveButtonText = 'Save',
    this.saveButtonColor,
    this.saveButtonTextColor,
    this.showHint = true,
    this.hintText = 'Sign here',
    this.hintTextStyle,
    this.showLabel = false,
    this.labelText = 'Signature',
    this.labelTextStyle,
    this.showShadow = false,
    this.shadowColor,
    this.shadowElevation = 4.0,
    this.isDisabled = false,
    this.showDottedLine = false,
    this.dottedLineColor,
    this.dottedLineWidth = 1.0,
    this.dottedLineDashPattern,
    this.showExportButton = false,
    this.exportButtonText = 'Export',
    this.exportButtonColor,
    this.exportButtonTextColor,
    this.exportFormat = ui.ImageByteFormat.png,
    this.onClear,
    this.onSave,
    this.onChanged,
    this.onExport,
    this.jsonConfig,
    this.useJsonConfig = false,
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
  });

  /// Creates a SignatureWidget from a JSON map
  ///
  /// This factory constructor allows for creating a fully configured signature widget
  /// from a JSON object, making it easy to load configurations from external sources.
  factory SignatureWidget.fromJson(dynamic jsonData) {
    // Handle string JSON input
    Map<String, dynamic> json;
    if (jsonData is String) {
      json = jsonDecode(jsonData) as Map<String, dynamic>;
    } else if (jsonData is Map<String, dynamic>) {
      json = jsonData;
    } else {
      throw ArgumentError('Invalid JSON data: $jsonData');
    }

    // Parse pen properties
    final penColor = _parseColor(json['penColor'], Colors.black);
    final penWidth = json['penWidth'] != null ? (json['penWidth'] as num).toDouble() : 3.0;

    // Parse appearance properties
    final backgroundColor = _parseColor(json['backgroundColor'], Colors.white);
    final showBorder = json['showBorder'] as bool? ?? true;
    final borderColor = json['borderColor'] != null ? _parseColor(json['borderColor']) : null;
    final borderWidth = json['borderWidth'] != null ? (json['borderWidth'] as num).toDouble() : 1.0;
    final borderRadius = json['borderRadius'] != null ? (json['borderRadius'] as num).toDouble() : 8.0;

    // Parse size properties
    final height = json['height'] != null ? (json['height'] as num).toDouble() : 200.0;
    final width = json['width'] != null ? (json['width'] as num).toDouble() : double.infinity;

    // Parse padding and margin
    final padding = json['padding'] != null ? _parseEdgeInsets(json['padding']) : const EdgeInsets.all(8.0);
    final margin = json['margin'] != null ? _parseEdgeInsets(json['margin']) : EdgeInsets.zero;

    // Parse button properties
    final showClearButton = json['showClearButton'] as bool? ?? true;
    final clearButtonText = json['clearButtonText'] as String? ?? 'Clear';
    final clearButtonColor = json['clearButtonColor'] != null ? _parseColor(json['clearButtonColor']) : null;
    final clearButtonTextColor = json['clearButtonTextColor'] != null ? _parseColor(json['clearButtonTextColor']) : null;

    final showSaveButton = json['showSaveButton'] as bool? ?? false;
    final saveButtonText = json['saveButtonText'] as String? ?? 'Save';
    final saveButtonColor = json['saveButtonColor'] != null ? _parseColor(json['saveButtonColor']) : null;
    final saveButtonTextColor = json['saveButtonTextColor'] != null ? _parseColor(json['saveButtonTextColor']) : null;

    // Parse hint and label properties
    final showHint = json['showHint'] as bool? ?? true;
    final hintText = json['hintText'] as String? ?? 'Sign here';
    final hintTextStyle = json['hintTextStyle'] != null ? _parseTextStyle(json['hintTextStyle']) : null;

    final showLabel = json['showLabel'] as bool? ?? false;
    final labelText = json['labelText'] as String? ?? 'Signature';
    final labelTextStyle = json['labelTextStyle'] != null ? _parseTextStyle(json['labelTextStyle']) : null;

    // Parse shadow properties
    final showShadow = json['showShadow'] as bool? ?? false;
    final shadowColor = json['shadowColor'] != null ? _parseColor(json['shadowColor']) : null;
    final shadowElevation = json['shadowElevation'] != null ? (json['shadowElevation'] as num).toDouble() : 4.0;

    // Parse other properties
    final isDisabled = json['isDisabled'] as bool? ?? false;

    // Parse dotted line properties
    final showDottedLine = json['showDottedLine'] as bool? ?? false;
    final dottedLineColor = json['dottedLineColor'] != null ? _parseColor(json['dottedLineColor']) : null;
    final dottedLineWidth = json['dottedLineWidth'] != null ? (json['dottedLineWidth'] as num).toDouble() : 1.0;

    List<double>? dottedLineDashPattern;
    if (json['dottedLineDashPattern'] != null && json['dottedLineDashPattern'] is List) {
      dottedLineDashPattern = (json['dottedLineDashPattern'] as List)
          .map((item) => (item as num).toDouble())
          .toList();
    }

    // Parse export properties
    final showExportButton = json['showExportButton'] as bool? ?? false;
    final exportButtonText = json['exportButtonText'] as String? ?? 'Export';
    final exportButtonColor = json['exportButtonColor'] != null ? _parseColor(json['exportButtonColor']) : null;
    final exportButtonTextColor = json['exportButtonTextColor'] != null ? _parseColor(json['exportButtonTextColor']) : null;

    ui.ImageByteFormat exportFormat = ui.ImageByteFormat.png;
    if (json['exportFormat'] != null) {
      final formatStr = json['exportFormat'].toString().toLowerCase();
      if (formatStr == 'png') {
        exportFormat = ui.ImageByteFormat.png;
      } else if (formatStr == 'raw') {
        exportFormat = ui.ImageByteFormat.rawRgba;
      }
    }

    // Parse JSON configuration
    final jsonConfig = json['jsonConfig'] as Map<String, dynamic>?;
    final useJsonConfig = json['useJsonConfig'] as bool? ?? false;
    final jsonCallbacks = json['jsonCallbacks'] as Map<String, dynamic>?;
    final useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    return SignatureWidget(
      penColor: penColor,
      penWidth: penWidth,
      backgroundColor: backgroundColor,
      showBorder: showBorder,
      borderColor: borderColor,
      borderWidth: borderWidth,
      borderRadius: borderRadius,
      height: height,
      width: width,
      padding: padding,
      margin: margin,
      showClearButton: showClearButton,
      clearButtonText: clearButtonText,
      clearButtonColor: clearButtonColor,
      clearButtonTextColor: clearButtonTextColor,
      showSaveButton: showSaveButton,
      saveButtonText: saveButtonText,
      saveButtonColor: saveButtonColor,
      saveButtonTextColor: saveButtonTextColor,
      showHint: showHint,
      hintText: hintText,
      hintTextStyle: hintTextStyle,
      showLabel: showLabel,
      labelText: labelText,
      labelTextStyle: labelTextStyle,
      showShadow: showShadow,
      shadowColor: shadowColor,
      shadowElevation: shadowElevation,
      isDisabled: isDisabled,
      showDottedLine: showDottedLine,
      dottedLineColor: dottedLineColor,
      dottedLineWidth: dottedLineWidth,
      dottedLineDashPattern: dottedLineDashPattern,
      showExportButton: showExportButton,
      exportButtonText: exportButtonText,
      exportButtonColor: exportButtonColor,
      exportButtonTextColor: exportButtonTextColor,
      exportFormat: exportFormat,
      jsonConfig: jsonConfig,
      useJsonConfig: useJsonConfig,
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
    );
  }

  /// Converts the SignatureWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    return {
      'penColor': _colorToString(penColor),
      'penWidth': penWidth,
      'backgroundColor': _colorToString(backgroundColor),
      'showBorder': showBorder,
      'borderColor': borderColor != null ? _colorToString(borderColor!) : null,
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'height': height,
      'width': width == double.infinity ? 'infinity' : width,
      'padding': _edgeInsetsToJson(padding),
      'margin': _edgeInsetsToJson(margin),
      'showClearButton': showClearButton,
      'clearButtonText': clearButtonText,
      'clearButtonColor': clearButtonColor != null ? _colorToString(clearButtonColor!) : null,
      'clearButtonTextColor': clearButtonTextColor != null ? _colorToString(clearButtonTextColor!) : null,
      'showSaveButton': showSaveButton,
      'saveButtonText': saveButtonText,
      'saveButtonColor': saveButtonColor != null ? _colorToString(saveButtonColor!) : null,
      'saveButtonTextColor': saveButtonTextColor != null ? _colorToString(saveButtonTextColor!) : null,
      'showHint': showHint,
      'hintText': hintText,
      'hintTextStyle': hintTextStyle != null ? _textStyleToJson(hintTextStyle!) : null,
      'showLabel': showLabel,
      'labelText': labelText,
      'labelTextStyle': labelTextStyle != null ? _textStyleToJson(labelTextStyle!) : null,
      'showShadow': showShadow,
      'shadowColor': shadowColor != null ? _colorToString(shadowColor!) : null,
      'shadowElevation': shadowElevation,
      'isDisabled': isDisabled,
      'showDottedLine': showDottedLine,
      'dottedLineColor': dottedLineColor != null ? _colorToString(dottedLineColor!) : null,
      'dottedLineWidth': dottedLineWidth,
      'dottedLineDashPattern': dottedLineDashPattern,
      'showExportButton': showExportButton,
      'exportButtonText': exportButtonText,
      'exportButtonColor': exportButtonColor != null ? _colorToString(exportButtonColor!) : null,
      'exportButtonTextColor': exportButtonTextColor != null ? _colorToString(exportButtonTextColor!) : null,
      'exportFormat': exportFormat == ui.ImageByteFormat.png ? 'png' : 'raw',
      'useJsonConfig': useJsonConfig,
      'useJsonCallbacks': useJsonCallbacks,
    };
  }

  /// Helper method to parse a color from a string or map
  static Color _parseColor(dynamic colorValue, [Color defaultColor = Colors.blue]) {
    if (colorValue == null) return defaultColor;

    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red': return Colors.red;
          case 'blue': return Colors.blue;
          case 'green': return Colors.green;
          case 'yellow': return Colors.yellow;
          case 'orange': return Colors.orange;
          case 'purple': return Colors.purple;
          case 'pink': return Colors.pink;
          case 'brown': return Colors.brown;
          case 'grey':
          case 'gray': return Colors.grey;
          case 'black': return Colors.black;
          case 'white': return Colors.white;
          case 'transparent': return Colors.transparent;
          default: return defaultColor;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a = colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return defaultColor;
  }

  /// Helper method to convert a color to a string
  static String _colorToString(Color color) {
    final r = color.toString().split('(0x')[1].substring(2, 4);
    final g = color.toString().split('(0x')[1].substring(4, 6);
    final b = color.toString().split('(0x')[1].substring(6, 8);
    return '#$r$g$b';
  }

  /// Helper method to parse EdgeInsets from a JSON object
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is String) {
      // Parse from string like "all:8.0" or "symmetric:horizontal:8.0,vertical:16.0"
      if (value.startsWith('all:')) {
        final padding = double.tryParse(value.split(':')[1]) ?? 8.0;
        return EdgeInsets.all(padding);
      } else if (value.startsWith('symmetric:')) {
        final parts = value.split(':');
        if (parts.length >= 4) {
          final horizontal = double.tryParse(parts[2].split(',')[0]) ?? 8.0;
          final vertical = double.tryParse(parts[3]) ?? 8.0;
          return EdgeInsets.symmetric(horizontal: horizontal, vertical: vertical);
        }
      }
      return const EdgeInsets.all(8.0);
    } else if (value is Map) {
      // Parse from map
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('symmetric')) {
        final symmetric = value['symmetric'] as Map;
        return EdgeInsets.symmetric(
          horizontal: symmetric['horizontal'] != null ? (symmetric['horizontal'] as num).toDouble() : 0.0,
          vertical: symmetric['vertical'] != null ? (symmetric['vertical'] as num).toDouble() : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value['left'] != null ? (value['left'] as num).toDouble() : 0.0,
          value['top'] != null ? (value['top'] as num).toDouble() : 0.0,
          value['right'] != null ? (value['right'] as num).toDouble() : 0.0,
          value['bottom'] != null ? (value['bottom'] as num).toDouble() : 0.0,
        );
      }
    } else if (value is num) {
      return EdgeInsets.all(value.toDouble());
    }
    return const EdgeInsets.all(8.0);
  }

  /// Helper method to convert EdgeInsets to a JSON object
  static Map<String, dynamic> _edgeInsetsToJson(EdgeInsetsGeometry edgeInsets) {
    if (edgeInsets is EdgeInsets) {
      if (edgeInsets.left == edgeInsets.top &&
          edgeInsets.left == edgeInsets.right &&
          edgeInsets.left == edgeInsets.bottom) {
        return {'all': edgeInsets.left};
      } else if (edgeInsets.left == edgeInsets.right && edgeInsets.top == edgeInsets.bottom) {
        return {
          'symmetric': {
            'horizontal': edgeInsets.left,
            'vertical': edgeInsets.top,
          }
        };
      } else {
        return {
          'left': edgeInsets.left,
          'top': edgeInsets.top,
          'right': edgeInsets.right,
          'bottom': edgeInsets.bottom,
        };
      }
    }
    return {'all': 8.0};
  }

  /// Helper method to parse a TextStyle from a JSON object
  static TextStyle? _parseTextStyle(dynamic value) {
    if (value == null) return null;

    if (value is Map) {
      Color? color;
      if (value['color'] != null) {
        color = _parseColor(value['color']);
      }

      double? fontSize;
      if (value['fontSize'] != null) {
        fontSize = (value['fontSize'] as num).toDouble();
      }

      FontWeight? fontWeight;
      if (value['fontWeight'] != null) {
        final weight = value['fontWeight'];
        if (weight is String) {
          switch (weight.toLowerCase()) {
            case 'bold': fontWeight = FontWeight.bold; break;
            case 'normal': fontWeight = FontWeight.normal; break;
            case 'light': fontWeight = FontWeight.w300; break;
            case 'thin': fontWeight = FontWeight.w100; break;
            case 'medium': fontWeight = FontWeight.w500; break;
            case 'semibold': fontWeight = FontWeight.w600; break;
            case 'black': fontWeight = FontWeight.w900; break;
          }
        } else if (weight is int) {
          switch (weight) {
            case 100: fontWeight = FontWeight.w100; break;
            case 200: fontWeight = FontWeight.w200; break;
            case 300: fontWeight = FontWeight.w300; break;
            case 400: fontWeight = FontWeight.w400; break;
            case 500: fontWeight = FontWeight.w500; break;
            case 600: fontWeight = FontWeight.w600; break;
            case 700: fontWeight = FontWeight.w700; break;
            case 800: fontWeight = FontWeight.w800; break;
            case 900: fontWeight = FontWeight.w900; break;
          }
        }
      }

      FontStyle? fontStyle;
      if (value['fontStyle'] != null) {
        final style = value['fontStyle'].toString().toLowerCase();
        if (style == 'italic') {
          fontStyle = FontStyle.italic;
        } else if (style == 'normal') {
          fontStyle = FontStyle.normal;
        }
      }

      return TextStyle(
        color: color,
        fontSize: fontSize,
        fontWeight: fontWeight,
        fontStyle: fontStyle,
      );
    }

    return null;
  }

  /// Helper method to convert a TextStyle to a JSON object
  static Map<String, dynamic>? _textStyleToJson(TextStyle style) {
    final Map<String, dynamic> result = {};

    if (style.color != null) {
      result['color'] = _colorToString(style.color!);
    }

    if (style.fontSize != null) {
      result['fontSize'] = style.fontSize;
    }

    if (style.fontWeight != null) {
      if (style.fontWeight == FontWeight.bold) {
        result['fontWeight'] = 'bold';
      } else if (style.fontWeight == FontWeight.normal) {
        result['fontWeight'] = 'normal';
      } else if (style.fontWeight == FontWeight.w100) {
        result['fontWeight'] = 'thin';
      } else if (style.fontWeight == FontWeight.w300) {
        result['fontWeight'] = 'light';
      } else if (style.fontWeight == FontWeight.w500) {
        result['fontWeight'] = 'medium';
      } else if (style.fontWeight == FontWeight.w600) {
        result['fontWeight'] = 'semibold';
      } else if (style.fontWeight == FontWeight.w900) {
        result['fontWeight'] = 'black';
      } else {
        result['fontWeight'] = (style.fontWeight!.index + 1) * 100;
      }
    }

    if (style.fontStyle != null) {
      result['fontStyle'] = style.fontStyle == FontStyle.italic ? 'italic' : 'normal';
    }

    return result.isNotEmpty ? result : null;
  }

  @override
  State<SignatureWidget> createState() => _SignatureWidgetState();
}

class _SignatureWidgetState extends State<SignatureWidget> {
  final List<List<Offset>> _strokes = [];
  List<Offset>? _currentStroke;
  bool _hasSignature = false;
  SignatureWidgetState _currentState = SignatureWidgetState.defaultState;

  /// Get border color based on current state
  Color _getBorderColor(BuildContext context) {
    switch (_currentState) {
      case SignatureWidgetState.defaultState:
        return const Color(0xFFCCCCCC); // Light gray
      case SignatureWidgetState.hover:
        return Colors.red; // Red on hover
      case SignatureWidgetState.pressed:
        return Colors.blue; // Blue when pressed
      case SignatureWidgetState.active:
        return _hasSignature ? Colors.green : Colors.blue; // Green when has signature, blue otherwise
    }
  }

  /// Update state and trigger rebuild
  void _updateState(SignatureWidgetState newState) {
    if (_currentState != newState) {
      setState(() {
        _currentState = newState;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Apply JSON configuration if available
    if (widget.useJsonConfig && widget.jsonConfig != null) {
      // Create a new widget with the JSON configuration applied
      final jsonWidget = SignatureWidget.fromJson(widget.jsonConfig!);

      // Use the JSON widget's properties for rendering, but keep the current state
      return _buildWidgetWithConfig(context, jsonWidget);
    }

    return _buildWidgetWithConfig(context, widget);
  }

  /// Builds the widget with the given configuration
  Widget _buildWidgetWithConfig(BuildContext context, SignatureWidget config) {
    final theme = Theme.of(context);

    // Determine effective colors
    final effectiveBorderColor = config.borderColor ?? theme.dividerColor;
    final effectiveShadowColor = config.shadowColor ?? Colors.black.withAlpha(51); // 0.2 opacity
    final effectiveDottedLineColor = config.dottedLineColor ?? theme.dividerColor;
    final effectiveClearButtonColor = config.clearButtonColor ?? theme.colorScheme.error;
    final effectiveClearButtonTextColor = config.clearButtonTextColor ?? Colors.white;
    final effectiveSaveButtonColor = config.saveButtonColor ?? theme.colorScheme.primary;
    final effectiveSaveButtonTextColor = config.saveButtonTextColor ?? Colors.white;
    final effectiveExportButtonColor = config.exportButtonColor ?? theme.colorScheme.secondary;
    final effectiveExportButtonTextColor = config.exportButtonTextColor ?? Colors.white;

    // Get current border color based on state
    final currentBorderColor = _getBorderColor(context);

    // Create the signature pad
    Widget signaturePad = MouseRegion(
      onEnter: (_) => _updateState(SignatureWidgetState.hover),
      onExit: (_) => _updateState(_hasSignature ? SignatureWidgetState.active : SignatureWidgetState.defaultState),
      child: GestureDetector(
        onTapDown: (_) => _updateState(SignatureWidgetState.pressed),
        onTapUp: (_) => _updateState(_hasSignature ? SignatureWidgetState.active : SignatureWidgetState.defaultState),
        onTapCancel: () => _updateState(_hasSignature ? SignatureWidgetState.active : SignatureWidgetState.defaultState),
        onPanStart: config.isDisabled ? null : _onPanStart,
        onPanUpdate: config.isDisabled ? null : _onPanUpdate,
        onPanEnd: config.isDisabled ? null : _onPanEnd,
        child: Container(
        padding: const EdgeInsets.all(10.0), // Move padding to parent
        decoration: BoxDecoration(
          border: Border.all(
            color: currentBorderColor, // Dynamic border color based on state
            width: 2,          // Slightly thicker border for better visibility
          ),
          borderRadius: BorderRadius.circular(config.borderRadius),
        ),
        child: Container(
          height: config.height,
          width: config.width,
          decoration: BoxDecoration(
            color: config.backgroundColor,
            borderRadius: BorderRadius.circular(config.borderRadius),
            boxShadow: config.showShadow
                ? [
                    BoxShadow(
                      color: effectiveShadowColor,
                      blurRadius: config.shadowElevation,
                      offset: Offset(0, config.shadowElevation / 2),
                    ),
                  ]
                : null,
          ),
          child: Stack(
            children: [
              // Signature canvas
              CustomPaint(
                painter: _SignaturePainter(
                  strokes: _strokes,
                  currentStroke: _currentStroke,
                  penColor: config.penColor,
                  penWidth: config.penWidth,
                  showDottedLine: config.showDottedLine,
                  dottedLineColor: effectiveDottedLineColor,
                  dottedLineWidth: config.dottedLineWidth,
                  dottedLineDashPattern: config.dottedLineDashPattern,
                ),
                size: Size(config.width, config.height),
              ),

              // Hint text
              if (config.showHint && !_hasSignature)
                Center(
                  child: Text(
                    config.hintText,
                    style: config.hintTextStyle ??
                        TextStyle(
                          color: Theme.of(context).hintColor,
                          fontSize: 16.0,
                          fontStyle: FontStyle.italic,
                        ),
                  ),
                ),

              // Clear button (X) in top-right corner
              Positioned(
                top: 8,
                right: 8,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _strokes.clear();
                      _currentStroke = null;
                      _hasSignature = false;
                      _currentState = SignatureWidgetState.defaultState;
                    });
                    if (widget.onClear != null) {
                      widget.onClear!();
                    }
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.black54,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        ),
      ),
    );

    // Create the buttons - only show Accept button in pressed/active states
    final buttons = Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Accept button - only show when pressed or active (has signature)
         
        if (config.showSaveButton)
          ElevatedButton(
            onPressed: config.isDisabled || !_hasSignature
                ? null
                : () async {
                    final bytes = await _exportSignature(config);
                    if (bytes != null && widget.onSave != null) {
                      widget.onSave!(bytes);
                    }
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFFFC657D),
              foregroundColor: Color(0xFFFFFFFF),
              disabledBackgroundColor: effectiveSaveButtonColor.withAlpha(128), // 0.5 opacity
            ),
            child: Text(config.saveButtonText),
          ),
        if (config.showExportButton)
          ElevatedButton(
            onPressed: config.isDisabled || !_hasSignature
                ? null
                : () async {
                    final bytes = await _exportSignature(config);
                    if (bytes != null && widget.onExport != null) {
                      widget.onExport!(bytes);
                    }
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFFFC657D),
              foregroundColor: Color(0xFFFFFFFF),
              disabledBackgroundColor: effectiveExportButtonColor.withAlpha(128), // 0.5 opacity
            ),
            child: Text(config.exportButtonText),
          ),
      ],
    );

    // Combine all elements
    return Container(
      padding: config.padding,
      margin: config.margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (config.showLabel)
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                config.labelText,
                style: config.labelTextStyle ??
                    TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.bold,
                      color: theme.textTheme.titleMedium?.color,
                    ),
              ),
            ),
          signaturePad,
          const SizedBox(height: 8.0),
          buttons,
        ],
      ),
    );
  }

  void _onPanStart(DragStartDetails details) {
    final box = context.findRenderObject() as RenderBox;
    final point = box.globalToLocal(details.globalPosition);
    setState(() {
      _currentStroke = [point];
      _hasSignature = true;
      _currentState = SignatureWidgetState.active; // Set to active when drawing starts
    });
    if (widget.onChanged != null) {
      widget.onChanged!();
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final box = context.findRenderObject() as RenderBox;
    final point = box.globalToLocal(details.globalPosition);
    setState(() {
      _currentStroke!.add(point);
    });
    if (widget.onChanged != null) {
      widget.onChanged!();
    }
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _strokes.add(List.from(_currentStroke!));
      _currentStroke = null;
    });
  }

  Future<Uint8List?> _exportSignature([SignatureWidget? config]) async {
    if (!_hasSignature) return null;

    // Use provided config or fall back to widget
    final cfg = config ?? widget;

    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // Draw background
    final paint = Paint()
      ..color = cfg.backgroundColor
      ..style = PaintingStyle.fill;
    canvas.drawRect(
      Rect.fromLTWH(0, 0, cfg.width, cfg.height),
      paint,
    );

    // Draw signature
    final signaturePainter = _SignaturePainter(
      strokes: _strokes,
      currentStroke: null,
      penColor: cfg.penColor,
      penWidth: cfg.penWidth,
      showDottedLine: false,
      dottedLineColor: Colors.transparent,
      dottedLineWidth: 0,
    );
    signaturePainter.paint(canvas, Size(cfg.width, cfg.height));

    // Convert to image
    final picture = recorder.endRecording();
    final img = await picture.toImage(
      cfg.width.toInt(),
      cfg.height.toInt(),
    );
    final byteData = await img.toByteData(format: cfg.exportFormat);

    return byteData?.buffer.asUint8List();
  }
}

class _SignaturePainter extends CustomPainter {
  final List<List<Offset>> strokes;
  final List<Offset>? currentStroke;
  final Color penColor;
  final double penWidth;
  final bool showDottedLine;
  final Color dottedLineColor;
  final double dottedLineWidth;
  final List<double>? dottedLineDashPattern;

  _SignaturePainter({
    required this.strokes,
    required this.currentStroke,
    required this.penColor,
    required this.penWidth,
    required this.showDottedLine,
    required this.dottedLineColor,
    required this.dottedLineWidth,
    this.dottedLineDashPattern,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw dotted line if needed
    if (showDottedLine) {
      final dottedPaint = Paint()
        ..color = dottedLineColor
        ..strokeWidth = dottedLineWidth
        ..style = PaintingStyle.stroke;

      final dashPattern = dottedLineDashPattern ?? [5, 5];
      final path = Path();

      // Draw dotted line at the bottom
      double startX = 0;
      final y = size.height - 20;

      while (startX < size.width) {
        // Draw dash
        path.moveTo(startX, y);
        path.lineTo(startX + dashPattern[0], y);

        // Move to next dash
        startX += dashPattern[0] + dashPattern[1];
      }

      canvas.drawPath(path, dottedPaint);
    }

    // Draw completed strokes
    final paint = Paint()
      ..color = penColor
      ..strokeWidth = penWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    for (final stroke in strokes) {
      if (stroke.length < 2) continue;

      final path = Path();
      path.moveTo(stroke[0].dx, stroke[0].dy);

      for (int i = 1; i < stroke.length; i++) {
        path.lineTo(stroke[i].dx, stroke[i].dy);
      }

      canvas.drawPath(path, paint);
    }

    // Draw current stroke
    if (currentStroke != null && currentStroke!.length >= 2) {
      final path = Path();
      path.moveTo(currentStroke![0].dx, currentStroke![0].dy);

      for (int i = 1; i < currentStroke!.length; i++) {
        path.lineTo(currentStroke![i].dx, currentStroke![i].dy);
      }

      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _SignaturePainter oldDelegate) {
    return oldDelegate.strokes != strokes ||
        oldDelegate.currentStroke != currentStroke ||
        oldDelegate.penColor != penColor ||
        oldDelegate.penWidth != penWidth ||
        oldDelegate.showDottedLine != showDottedLine ||
        oldDelegate.dottedLineColor != dottedLineColor ||
        oldDelegate.dottedLineWidth != dottedLineWidth;
  }
}