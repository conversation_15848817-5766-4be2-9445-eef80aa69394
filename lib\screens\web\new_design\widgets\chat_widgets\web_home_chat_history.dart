import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:provider/provider.dart';
import '../../../../../../providers/chat_history_provider.dart';

// --- Chat Session List Widget ---
class ChatSessionList extends StatelessWidget {
  final bool showNavigationBar;

  const ChatSessionList({super.key, this.showNavigationBar = true});

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatHistoryProvider>(
      builder: (context, chatHistoryProvider, child) {
        if (chatHistoryProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return Padding(
          padding: showNavigationBar
              ? const EdgeInsets.only(left: 94, right: 94, bottom: 0.0, top: 0)
              : EdgeInsets.zero,
          child: Padding(
            padding: const EdgeInsets.only(left: 6, right: 6),
            child: Column(
              children: [
                const SizedBox(height: 32),
                Expanded(
                  child: ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount:
                        chatHistoryProvider.currentPageSessions.length + 1,
                    separatorBuilder: (_, __) => const SizedBox(height: 12),
                    itemBuilder: (context, idx) {
                      if (idx <
                          chatHistoryProvider.currentPageSessions.length) {
                        final session =
                            chatHistoryProvider.currentPageSessions[idx];
                        return AnimatedChatSessionListItem(
                          session: session,
                          index: idx,
                          isSelected: chatHistoryProvider.selectedSessionId ==
                              session.id,
                          onTap: () =>
                              chatHistoryProvider.selectSession(session.id),
                        );
                      } else {
                        return PaginationRow(
                          currentPage: chatHistoryProvider.currentPage,
                          totalPages: chatHistoryProvider.totalPages,
                          canGoToPrevious:
                              chatHistoryProvider.canGoToPreviousPage,
                          canGoToNext: chatHistoryProvider.canGoToNextPage,
                          onPreviousPage: chatHistoryProvider.goToPreviousPage,
                          onNextPage: chatHistoryProvider.goToNextPage,
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// --- Animated Chat Session List Item ---
class AnimatedChatSessionListItem extends StatefulWidget {
  final ChatSession session;
  final int index;
  final bool isSelected;
  final VoidCallback onTap;

  const AnimatedChatSessionListItem({
    required this.session,
    required this.index,
    required this.isSelected,
    required this.onTap,
    super.key,
  });

  @override
  State<AnimatedChatSessionListItem> createState() =>
      _AnimatedChatSessionListItemState();
}

class _AnimatedChatSessionListItemState
    extends State<AnimatedChatSessionListItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<double>(begin: 30.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Start animation with staggered delay
    Future.delayed(Duration(milliseconds: widget.index * 100), () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: GestureDetector(
              onTap: widget.onTap,
              child: ChatSessionListItem(
                title: widget.session.title,
                date: widget.session.date,
                isSelected: widget.isSelected,
              ),
            ),
          ),
        );
      },
    );
  }
}

// --- Chat Session List Item ---
class ChatSessionListItem extends StatelessWidget {
  final String title;
  final String date;
  final bool isSelected;

  const ChatSessionListItem({
    required this.title,
    required this.date,
    this.isSelected = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return HoverBuilder(
      builder: (isHovered) {
        return AnimatedContainer(
          // margin: EdgeInsets.only(right: 20),
          duration: const Duration(milliseconds: 120),
          curve: Curves.easeInOut,
          height: 67,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: isHovered
                ? [
                    BoxShadow(
                      color: const Color(0xFF4A90E2).withValues(alpha: 0.08),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    )
                  ]
                : [],
          ),
          padding: const EdgeInsets.symmetric(horizontal: 17),
          child: Stack(
            children: [
              Positioned(
                left: 0,
                top: 14,
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF848484),
                    fontFamily: "TiemposText",
                  ),
                ),
              ),
              Positioned(
                right: 0,
                bottom: 14,
                child: Text(
                  date,
                  style: const TextStyle(
                    fontSize: AppSpacing.sm,
                    fontFamily: 'TiemposText',
                    color: Color(0xff757575),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// --- Hover Builder Utility Widget ---
class HoverBuilder extends StatelessWidget {
  final Widget Function(bool isHovered) builder;

  const HoverBuilder({required this.builder, super.key});

  @override
  Widget build(BuildContext context) {
    final ValueNotifier<bool> isHovered = ValueNotifier(false);

    return MouseRegion(
      onEnter: (_) => isHovered.value = true,
      onExit: (_) => isHovered.value = false,
      child: ValueListenableBuilder<bool>(
        valueListenable: isHovered,
        builder: (_, value, child) => builder(value),
      ),
    );
  }
}

// --- Pagination Row ---
class PaginationRow extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final bool canGoToPrevious;
  final bool canGoToNext;
  final VoidCallback onPreviousPage;
  final VoidCallback onNextPage;

  const PaginationRow({
    required this.currentPage,
    required this.totalPages,
    required this.canGoToPrevious,
    required this.canGoToNext,
    required this.onPreviousPage,
    required this.onNextPage,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Page info text
          Text(
            'Page $currentPage of $totalPages',
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF8D8D8D),
              fontFamily: "TiemposText",
            ),
          ),
          const SizedBox(width: 16),
          // Previous button
          HoverPaginationButton(
            icon: Icon(
              Icons.chevron_left,
              size: 20,
              color: canGoToPrevious ? Colors.black : Colors.grey,
            ),
            onPressed: canGoToPrevious ? onPreviousPage : null,
          ),
          const SizedBox(width: 8),
          // Next button
          HoverPaginationButton(
            icon: Icon(
              Icons.chevron_right,
              size: 20,
              color: canGoToNext ? Colors.black : Colors.grey,
            ),
            onPressed: canGoToNext ? onNextPage : null,
          ),
        ],
      ),
    );
  }
}

// --- Pagination Button ---
class HoverPaginationButton extends StatelessWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const HoverPaginationButton({
    required this.icon,
    this.onPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return HoverBuilder(
      builder: (isHovered) {
        return Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            border: Border.all(
              color: isHovered ? const Color(0xff0058FF) : Colors.grey.shade300,
              width: 1.0,
            ),
            borderRadius: isHovered ? BorderRadius.zero : null,
            color: Colors.white,
          ),
          child: IconButton(
            icon: icon,
            onPressed: onPressed,
            padding: EdgeInsets.zero,
            color: isHovered ? const Color(0xff0058FF) : Colors.black,
            constraints: const BoxConstraints(),
            hoverColor: Colors.transparent,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
          ),
        );
      },
    );
  }
}
