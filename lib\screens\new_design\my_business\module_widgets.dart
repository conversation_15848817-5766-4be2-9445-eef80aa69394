import 'package:flutter/material.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/screens/new_design/my_business/module.dart';
import 'package:nsl/screens/web_transaction/web_transaction_execution.dart';
import 'package:nsl/theme/spacing.dart';

class ModuleWidgets extends StatelessWidget {
  final String objectiveId;
  final String objectiveName;
  final bool isMobile;

  const ModuleWidgets({
    Key? key,
    required this.objectiveId,
    required this.objectiveName,
    required this.isMobile,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
       resizeToAvoidBottomInset: true,
      backgroundColor: Color(0xffF7F9FB),
      body: Padding(
        padding:const EdgeInsets.only(left: 16.0,right:16,top:60),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _buildBackNavigation(context),
              ],
            ),
            Expanded(
              child: Container(
                color: Colors.red,
                child: WebTransactionExecution(
                  objectiveId: objectiveId,
                  objectiveName: objectiveName,
                  isMobile: isMobile,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackNavigation(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: Container(
          padding: EdgeInsets.only(
            // top: AppSpacing.xxs,
            bottom: AppSpacing.xxs,
          ),
          child: CustomImage.asset(
            'assets/images/my_business/back_arrow.svg',
            width: 12,
            height: 12,
            color: Colors.black,
          ).toWidget(),
        ),
      ),
    );
  }
}
