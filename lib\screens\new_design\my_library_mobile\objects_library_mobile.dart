import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';

import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/library_navbar_mobile.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';

class ObjectMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final String versionNumber;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;
  final DateTime lastUpdated;

  ObjectMobile({
    required this.title,
    this.subtitle = '',
    required this.imageUrl,
    required this.versionNumber,
    required this.lastUpdated,
    this.isDraft = false,
    this.imageWidth = 107.0,
    this.imageHeight = 107.0,
  });

  factory ObjectMobile.fromJson(Map<String, dynamic> json) {
    return ObjectMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String? ?? '',
      imageUrl: json['imageUrl'] as String,
      versionNumber: json['versionNumber'] as String? ?? '',
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 107.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 107.0,
    );
  }
}

class ObjectsLibraryMobile extends StatefulWidget {
  const ObjectsLibraryMobile({super.key, this.showNavigationBar = true});

  final bool showNavigationBar;

  @override
  State<ObjectsLibraryMobile> createState() => _ObjectsLibraryMobileState();
}

class _ObjectsLibraryMobileState extends State<ObjectsLibraryMobile>
    with TickerProviderStateMixin {
  // Constants
  static const double _objectsPerViewNormal = 2.25;
  static const double _objectsPerViewCompact = 3.0;
  static const double _objectAspectRatio = 1.0; // width / height (square)
  static const double _titleHeight = 32.0;
  static const double _subtitleHeight = 16.0;
  static const double _verticalSpacing = 12.0;
  static const double _objectSpacing = 30.0;
  static const double _horizontalPadding = 24.0;
  static const int _recentObjectsLimit = 10;

  // Text Styles
  static const TextStyle _sectionHeadingStyle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _objectTitleStyle = TextStyle(
    fontWeight: FontWeight.w500,
    fontSize: 12,
    height: 1.334,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _objectSubtitleStyle = TextStyle(
    fontWeight: FontWeight.w400,
    fontSize: 11,
    color: Colors.black,
    fontFamily: "TiemposText",
  );

  static const TextStyle _emptyStateStyle = TextStyle(
    fontSize: 16,
    color: Colors.grey,
    fontFamily: "TiemposText",
  );

  // Data
  List<ObjectMobile> objects = [];
  List<ObjectMobile> recentObjects = [];
  List<ObjectMobile> allObjects = [];
  bool isLoading = true;

  // Navigation
  int selectedTabIndex = 2;

  // Controllers
  late CarouselController _recentObjectsController;
  late CarouselController _allObjectsController;
  final FocusNode _searchFocusNode = FocusNode();
  late AnimationController _loadingAnimationController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // UI state
  bool _isKeyboardVisible = false;

  /// Get objects per view based on keyboard visibility
  double _getObjectsPerView() {
    return _isKeyboardVisible ? _objectsPerViewCompact : _objectsPerViewNormal;
  }

  // JSON string containing object data with lastUpdated dates
  static const String objectsJsonString = '''
{
  "objects": [
    {
      "title": "Customer Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00201",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-21T10:15:00Z"
    },
    {
      "title": "Product Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00202",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-23T14:30:00Z"
    },
    {
      "title": "Address Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00203",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-19T09:45:00Z"
    },
    {
      "title": "Order Object",
      "subtitle": "Business Entity",
      "versionNumber": "V00204",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true,
      "lastUpdated": "2024-12-25T16:20:00Z"
    },
    {
      "title": "Payment Object",
      "subtitle": "Financial Entity",
      "versionNumber": "V00205",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-16T11:55:00Z"
    },
    {
      "title": "Inventory Object",
      "subtitle": "Business Entity",
      "versionNumber": "V00206",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-22T13:40:00Z"
    },
    {
      "title": "User Object",
      "subtitle": "Core Entity",
      "versionNumber": "V00207",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": true,
      "lastUpdated": "2024-12-20T08:25:00Z"
    },
    {
      "title": "Category Object",
      "subtitle": "Classification Entity",
      "versionNumber": "V00208",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-24T15:10:00Z"
    },
    {
      "title": "Review Object",
      "subtitle": "Content Entity",
      "versionNumber": "V00209",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-18T12:35:00Z"
    },
    {
      "title": "Notification Object",
      "subtitle": "System Entity",
      "versionNumber": "V00210",
      "imageUrl": "assets/images/object-placeholder.png",
      "isDraft": false,
      "lastUpdated": "2024-12-17T17:50:00Z"
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeAnimations();
    _loadObjects();
  }

  void _initializeControllers() {
    _recentObjectsController = CarouselController();
    _allObjectsController = CarouselController();
    _searchFocusNode.addListener(_onSearchFocusChange);
  }

  void _initializeAnimations() {
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _onSearchFocusChange() {
    // Detect keyboard visibility by checking if search field is focused
    // and using MediaQuery in build method
    setState(() {
      // This will trigger a rebuild where we can check MediaQuery
    });
  }

  void _loadObjects() {
    try {
      final data = json.decode(objectsJsonString);
      final loadedObjects = (data['objects'] as List<dynamic>)
          .map((objectJson) =>
              ObjectMobile.fromJson(objectJson as Map<String, dynamic>))
          .toList();

      // Keep original order for allObjects (as received from API/JSON)
      final originalOrderObjects = List<ObjectMobile>.from(loadedObjects);

      // Sort objects by lastUpdated date (most recent first) for recentObjects
      loadedObjects.sort((a, b) => b.lastUpdated.compareTo(a.lastUpdated));

      setState(() {
        objects = originalOrderObjects; // Original order
        recentObjects =
            loadedObjects.take(_recentObjectsLimit).toList(); // Recent sorted
        allObjects = originalOrderObjects; // Original order (as from API/JSON)
        isLoading = false;
      });

      // Debug: Print recent objects order
      debugPrint('Recent Objects (sorted by lastUpdated):');
      for (int i = 0; i < recentObjects.length; i++) {
        debugPrint(
            '${i + 1}. ${recentObjects[i].title} - ${recentObjects[i].lastUpdated}');
      }

      // Debug: Print all objects order (original)
      debugPrint('\nAll Objects (original API/JSON order):');
      for (int i = 0; i < allObjects.length && i < 5; i++) {
        debugPrint(
            '${i + 1}. ${allObjects[i].title} - ${allObjects[i].lastUpdated}');
      }

      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        objects = <ObjectMobile>[];
        recentObjects = <ObjectMobile>[];
        allObjects = <ObjectMobile>[];
        isLoading = false;
      });
      debugPrint('Error loading objects: $e');
    }
  }

  @override
  void dispose() {
    _recentObjectsController.dispose();
    _allObjectsController.dispose();
    _searchFocusNode.removeListener(_onSearchFocusChange);
    _searchFocusNode.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _updateKeyboardVisibility();
    return _buildObjectsLibraryView();
  }

  void _updateKeyboardVisibility() {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _isKeyboardVisible = isKeyboardVisible;
          });
        }
      });
    }
  }

  Widget _buildObjectsLibraryView() {
    return Scaffold(
      backgroundColor: widget.showNavigationBar
          ? const Color(0xfff6f6f6)
          : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildObjectsContent(),
        ],
      ),
      floatingActionButton:
          widget.showNavigationBar ? _buildFloatingActionButton() : null,
    );
  }

  /// Builds floating action button
  Widget _buildFloatingActionButton() {
    return SizedBox(
      width: 46,
      height: 46,
      child: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CreateBookMobile(),
            ),
          );
        },
        backgroundColor: const Color(0xff0058FF),
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        child: const Icon(Icons.add),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          // Hamburger menu icon
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          // Expanded widget to center the title
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('webobject.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // Invisible spacer to balance the layout (same width as menu icon)
          const SizedBox(width: 56), // IconButton default width
        ],
      ),
    );
  }

  Widget _buildTopNavigation() {
    return LibraryNavbarMobile(
      selectedTabIndex: selectedTabIndex,
    );
  }

  Widget _buildObjectsContent() {
    return Expanded(
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 0, 16),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Recent Objects Section
                _buildSectionHeading("Recent Objects"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildRecentObjectsCarousel(),
                ),
                const SizedBox(height: 24),
                // All Objects Section
                _buildSectionHeading("All Objects"),
                const SizedBox(height: 12),
                SizedBox(
                  height: _calculateCarouselHeight(),
                  child: _buildAllObjectsCarousel(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Builds section heading
  Widget _buildSectionHeading(String title) {
    return Text(title, style: _sectionHeadingStyle);
  }

  /// Builds Recent Objects carousel
  Widget _buildRecentObjectsCarousel() {
    return _buildCarousel(
      objects: recentObjects,
      controller: _recentObjectsController,
      emptyMessage: 'No recent objects found',
    );
  }

  /// Builds All Objects carousel
  Widget _buildAllObjectsCarousel() {
    return _buildCarousel(
      objects: allObjects,
      controller: _allObjectsController,
      emptyMessage: 'No objects found',
    );
  }

  /// Builds a generic carousel widget
  Widget _buildCarousel({
    required List<ObjectMobile> objects,
    required CarouselController controller,
    required String emptyMessage,
  }) {
    if (objects.isEmpty) {
      return Center(
        child: Text(emptyMessage, style: _emptyStateStyle),
      );
    }

    final itemExtent = _calculateItemExtent();
    return CarouselView(
      padding: EdgeInsets.zero,
      backgroundColor: Colors.transparent,
      controller: controller,
      itemExtent: itemExtent,
      enableSplash: false,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      shrinkExtent: itemExtent,
      children: objects.asMap().entries.map((entry) {
        return _buildObjectItem(entry.value, entry.key);
      }).toList(),
    );
  }

  /// Calculate item extent for CarouselView
  double _calculateItemExtent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final objectsPerView = _getObjectsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    return availableWidth / objectsPerView;
  }

  /// Builds individual object item widget
  Widget _buildObjectItem(ObjectMobile object, int objectIndex) {
    return GestureDetector(
      onTap: () => _navigateToObjectDetails(objectIndex),
      child: AnimatedBuilder(
        animation: _loadingAnimationController,
        builder: (context, child) => FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildObjectContent(object),
          ),
        ),
      ),
    );
  }

  /// Builds the object content (cover, title, subtitle)
  Widget _buildObjectContent(ObjectMobile object) {
    final objectDimensions = _calculateObjectDimensions(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildObjectCover(object, objectDimensions),
        const SizedBox(height: 8),
        _buildObjectTitle(object.title, objectDimensions['width']!),
        const SizedBox(height: 4),
        _buildObjectSubtitle(object.versionNumber, objectDimensions['width']!),
      ],
    );
  }

  /// Calculate dynamic object dimensions based on available space
  Map<String, double> _calculateObjectDimensions(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final objectsPerView = _getObjectsPerView();
    final availableWidth = screenWidth - (_horizontalPadding * 2);
    final itemExtent = availableWidth / objectsPerView;

    double objectWidth = itemExtent - _objectSpacing;

    // Size constraints for different modes
    if (_isKeyboardVisible) {
      objectWidth = objectWidth.clamp(85.0, 140.0);
    } else {
      objectWidth = objectWidth.clamp(110.0, 170.0);
    }

    final objectHeight = objectWidth / _objectAspectRatio; // Square objects

    return {
      'width': objectWidth,
      'height': objectHeight,
      'spacing': _objectSpacing,
    };
  }

  /// Calculate dynamic height for CarouselView based on object content
  double _calculateCarouselHeight() {
    final objectDimensions = _calculateObjectDimensions(context);
    final objectHeight = objectDimensions['height']!;
    return objectHeight + _verticalSpacing + _titleHeight + _subtitleHeight + 6;
  }

  /// Navigates to object details screen
  void _navigateToObjectDetails(int objectIndex) {
    // Handle object navigation
    debugPrint('Navigate to object at index: $objectIndex');
  }

  /// Builds object cover with draft badge
  Widget _buildObjectCover(
      ObjectMobile object, Map<String, double> dimensions) {
    final objectWidth = dimensions['width']!;
    final objectHeight = dimensions['height']!;

    return Stack(
      children: [
        Container(
          width: objectWidth,
          height: objectHeight,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(object.imageUrl),
              fit: BoxFit.cover,
            ),
          ),
        ),
        if (object.isDraft)
          Positioned(
            top: objectHeight * 0.08,
            right: objectWidth * 0.08,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.amber,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'Draft',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                  fontFamily: "TiemposText",
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Builds object title
  Widget _buildObjectTitle(String title, double objectWidth) {
    return SizedBox(
      width: objectWidth,
      child: Text(
        title,
        style: _objectTitleStyle,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// Builds object subtitle
  Widget _buildObjectSubtitle(String versionNumber, double objectWidth) {
    return SizedBox(
      width: objectWidth,
      child: Text(
        versionNumber,
        style: _objectSubtitleStyle,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Search bar
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                // Search text field
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0),
                    child: TextField(
                      focusNode: _searchFocusNode,
                      decoration: InputDecoration(
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        hintText: 'Search',
                        border: InputBorder.none,
                        hintStyle:
                            TextStyle(fontSize: 14, color: Colors.grey[500]),
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ),
                // Search and filter icons
                _MobileObjectSvgButton(
                  iconPath: 'assets/images/search.svg',
                  onPressed: () {},
                  size: 20,
                ),
                Container(
                  height: 24,
                  width: 1,
                  color: Colors.grey.shade200,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                ),
                _MobileObjectSvgButton(
                  iconPath: 'assets/images/filter-icon.svg',
                  onPressed: () {},
                  size: 24,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          // const SizedBox(height: 12),
          // Create object button
          // SizedBox(
          //   width: double.infinity,
          //   height: 44,
          //   child: ElevatedButton(
          //     onPressed: () {},
          //     style: ElevatedButton.styleFrom(
          //       backgroundColor: const Color(0xff0058FF),
          //       foregroundColor: Colors.white,
          //       elevation: 0,
          //       shape: RoundedRectangleBorder(
          //         borderRadius: BorderRadius.circular(6),
          //       ),
          //     ),
          //     child: Text(
          //       AppLocalizations.of(context)
          //           .translate('webobject.createButtonText'),
          //       style: const TextStyle(
          //         fontSize: 16,
          //         fontWeight: FontWeight.w500,
          //         fontFamily: 'TiemposText',
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}

// Mobile Object SVG Button Widget
class _MobileObjectSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileObjectSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileObjectSvgButton> createState() => _MobileObjectSvgButtonState();
}

class _MobileObjectSvgButtonState extends State<_MobileObjectSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Object Pagination Button Widget
class _MobileObjectPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback onPressed;

  const _MobileObjectPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_MobileObjectPaginationButton> createState() =>
      _MobileObjectPaginationButtonState();
}

class _MobileObjectPaginationButtonState
    extends State<_MobileObjectPaginationButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.grey.shade300,
            width: 1.0,
          ),
          borderRadius: isPressed ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: Center(
          child: Icon(
            widget.icon.icon,
            color: isPressed ? const Color(0xff0058FF) : Colors.black,
            size: widget.icon.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Object Card Widget
class _MobileObjectCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _MobileObjectCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_MobileObjectCard> createState() => _MobileObjectCardState();
}

class _MobileObjectCardState extends State<_MobileObjectCard> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: widget.child,
    );
  }
}
