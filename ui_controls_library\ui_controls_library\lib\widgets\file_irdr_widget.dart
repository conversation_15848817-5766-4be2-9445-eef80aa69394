import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import '../utils/callback_interpreter.dart';

// Extension to capitalize strings
extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}

/// A widget for Intelligent Document Recognition (IRDR).
///
/// This widget provides functionality for document scanning, OCR (Optical Character Recognition),
/// document classification, data extraction, and more.
class FileIrdrWidget extends StatefulWidget {
  // Basic properties
  final bool isRequired;
  final bool allowMultiple;
  final List<String>? allowedExtensions;
  final int? maxFileSizeBytes;
  final int? maxFiles;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final String buttonText;

  // Icon properties
  final bool showIcon;
  final IconData? icon;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool showFileName;
  final bool showFileSize;
  final bool showFileType;
  final bool showClearButton;
  final bool showPreview;
  final bool uploadImmediately;
  final bool showProgressBar;
  final bool allowDragDrop;

  // IRDR specific properties
  final bool enableOcr;
  final bool enableClassification;
  final bool enableDataExtraction;
  final bool showConfidence;
  final bool showProcessingIndicator;
  final bool autoProcess;
  final bool showResults;
  final bool enableEditing;
  final bool showFieldLabels;
  final bool highlightFields;
  final Color highlightColor;
  final double confidenceThreshold;
  final List<String>? documentTypes;
  final Map<String, String>? fieldMappings;
  final bool showCamera;
  final bool enhanceImage;
  final bool detectBoundaries;
  final bool cropToDocument;
  final bool rotateDocument;
  final bool convertToPdf;
  final bool saveOriginal;
  final String? outputDirectory;
  final bool compressOutput;
  final bool enableBatchProcessing;
  final int? batchSize;
  final bool showBatchProgress;
  final bool validateData;
  final bool showValidationErrors;
  final bool enableManualOverride;
  final bool showExportButton;
  final List<String>? exportFormats;
  final bool showScanHistory;
  final int? maxHistoryItems;
  final bool enableCloudSync;
  final String? cloudProvider;
  final bool showThumbnails;
  final double thumbnailSize;
  final bool enableAnnotation;
  final List<Color>? annotationColors;
  final bool showZoomControls;
  final double maxZoom;
  final bool enablePageNavigation;
  final bool showPageIndicator;
  final bool enableFullScreen;
  final bool showRotationControls;
  final bool enableTextSearch;
  final bool highlightSearchResults;
  final Color searchHighlightColor;
  final bool showSearchCount;
  final bool enableTemplateMatching;
  final List<String>? templates;
  final bool showTemplateSelector;
  final bool enableAutoCorrection;
  final double correctionThreshold;
  final bool showCorrectionSuggestions;
  final bool enableSignatureCapture;
  final bool requireSignature;
  final Color signatureColor;
  final double signatureThickness;
  final bool clearSignatureOnSave;
  final bool showSignatureDate;
  final bool enableFaceDetection;
  final bool highlightFaces;
  final bool blurFaces;
  final bool enableIdCardScan;
  final bool extractIdCardData;
  final bool validateIdCard;
  final bool enableReceiptScan;
  final bool extractReceiptItems;
  final bool calculateReceiptTotal;
  final bool enableBarcodeScan;
  final bool showBarcodeData;
  final List<String>? barcodeTypes;
  final bool enableQrScan;
  final bool decodeQrContent;
  final bool enableMrzScan;
  final bool validateMrz;
  final bool extractMrzData;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callbacks
  final Function(List<PlatformFile>)? onFilesSelected;
  final Function()? onClear;
  final Function(List<PlatformFile>)? onProcess;
  final Function(Map<String, dynamic>)? onDataExtracted;
  final Function(String)? onClassificationComplete;
  final Function(String)? onOcrComplete;
  final Function(Map<String, dynamic>)? onValidationComplete;
  final Function(String)? onExport;
  final Function(File)? onImageEnhanced;
  final Function(List<Map<String, dynamic>>)? onBatchProcessComplete;
  final Function(String)? onError;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  // IRDR-specific JSON configuration
  final bool useJsonIrdrConfig;
  final Map<String, dynamic>? irdrConfig;

  const FileIrdrWidget({
    super.key,
    this.isRequired = false,
    this.allowMultiple = false,
    this.allowedExtensions = const [
      'pdf',
      'jpg',
      'jpeg',
      'png',
      'tiff',
      'tif',
      'bmp',
    ],
    this.maxFileSizeBytes,
    this.maxFiles,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.buttonText = 'Select Document',
    this.showIcon = true,
    this.icon = Icons.document_scanner,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.showFileName = true,
    this.showFileSize = true,
    this.showFileType = true,
    this.showClearButton = true,
    this.showPreview = true,
    this.uploadImmediately = false,
    this.showProgressBar = true,
    this.allowDragDrop = true,
    this.enableOcr = false,
    this.enableClassification = false,
    this.enableDataExtraction = false,
    this.showConfidence = false,
    this.showProcessingIndicator = true,
    this.autoProcess = true,
    this.showResults = true,
    this.enableEditing = true,
    this.showFieldLabels = true,
    this.highlightFields = true,
    this.highlightColor = Colors.blue,
    this.confidenceThreshold = 0.7,
    this.documentTypes,
    this.fieldMappings,
    this.showCamera = false,
    this.enhanceImage = false,
    this.detectBoundaries = false,
    this.cropToDocument = false,
    this.rotateDocument = false,
    this.convertToPdf = false,
    this.saveOriginal = true,
    this.outputDirectory,
    this.compressOutput = false,
    this.enableBatchProcessing = false,
    this.batchSize,
    this.showBatchProgress = true,
    this.validateData = false,
    this.showValidationErrors = true,
    this.enableManualOverride = true,
    this.showExportButton = false,
    this.exportFormats,
    this.showScanHistory = false,
    this.maxHistoryItems,
    this.enableCloudSync = false,
    this.cloudProvider,
    this.showThumbnails = true,
    this.thumbnailSize = 60.0,
    this.enableAnnotation = false,
    this.annotationColors,
    this.showZoomControls = false,
    this.maxZoom = 3.0,
    this.enablePageNavigation = true,
    this.showPageIndicator = true,
    this.enableFullScreen = false,
    this.showRotationControls = false,
    this.enableTextSearch = false,
    this.highlightSearchResults = true,
    this.searchHighlightColor = Colors.yellow,
    this.showSearchCount = true,
    this.enableTemplateMatching = false,
    this.templates,
    this.showTemplateSelector = false,
    this.enableAutoCorrection = false,
    this.correctionThreshold = 0.8,
    this.showCorrectionSuggestions = true,
    this.enableSignatureCapture = false,
    this.requireSignature = false,
    this.signatureColor = const Color(0xFF0058FF),
    this.signatureThickness = 2.0,
    this.clearSignatureOnSave = true,
    this.showSignatureDate = true,
    this.enableFaceDetection = false,
    this.highlightFaces = true,
    this.blurFaces = false,
    this.enableIdCardScan = false,
    this.extractIdCardData = true,
    this.validateIdCard = true,
    this.enableReceiptScan = false,
    this.extractReceiptItems = true,
    this.calculateReceiptTotal = true,
    this.enableBarcodeScan = false,
    this.showBarcodeData = true,
    this.barcodeTypes,
    this.enableQrScan = false,
    this.decodeQrContent = true,
    this.enableMrzScan = false,
    this.validateMrz = true,
    this.extractMrzData = true,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
    this.margin = const EdgeInsets.all(0),
    this.onFilesSelected,
    this.onClear,
    this.onProcess,
    this.onDataExtracted,
    this.onClassificationComplete,
    this.onOcrComplete,
    this.onValidationComplete,
    this.onExport,
    this.onImageEnhanced,
    this.onBatchProcessComplete,
    this.onError,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // IRDR-specific JSON configuration
    this.useJsonIrdrConfig = false,
    this.irdrConfig,
  });

  /// Creates a FileIrdrWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the FileIrdrWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "allowMultiple": true,
  ///   "allowedExtensions": ["pdf", "jpg", "png"],
  ///   "enableOcr": true,
  ///   "enableClassification": true,
  ///   "buttonText": "Scan Document"
  /// }
  /// ```
  factory FileIrdrWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse text alignment
    TextAlign parseTextAlign(dynamic alignValue) {
      if (alignValue == null) return TextAlign.start;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'center':
            return TextAlign.center;
          case 'end':
          case 'right':
            return TextAlign.end;
          case 'start':
          case 'left':
            return TextAlign.start;
          case 'justify':
            return TextAlign.justify;
          default:
            return TextAlign.start;
        }
      }

      return TextAlign.start;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.normal;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'light':
            return FontWeight.w300;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100:
            return FontWeight.w100;
          case 200:
            return FontWeight.w200;
          case 300:
            return FontWeight.w300;
          case 400:
            return FontWeight.w400;
          case 500:
            return FontWeight.w500;
          case 600:
            return FontWeight.w600;
          case 700:
            return FontWeight.w700;
          case 800:
            return FontWeight.w800;
          case 900:
            return FontWeight.w900;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is bool && weightValue) {
        return FontWeight.bold;
      }

      return FontWeight.normal;
    }

    // Parse icon data
    IconData? parseIconData(dynamic iconValue) {
      if (iconValue == null) return null;

      if (iconValue is String) {
        switch (iconValue.toLowerCase()) {
          case 'document_scanner':
            return Icons.document_scanner;
          case 'upload_file':
            return Icons.upload_file;
          case 'file_upload':
            return Icons.file_upload;
          case 'attach_file':
            return Icons.attach_file;
          case 'cloud_upload':
            return Icons.cloud_upload;
          case 'add':
            return Icons.add;
          case 'add_circle':
            return Icons.add_circle;
          case 'add_circle_outline':
            return Icons.add_circle_outline;
          case 'folder':
            return Icons.folder;
          case 'folder_open':
            return Icons.folder_open;
          case 'description':
            return Icons.description;
          case 'insert_drive_file':
            return Icons.insert_drive_file;
          case 'picture_as_pdf':
            return Icons.picture_as_pdf;
          case 'image':
            return Icons.image;
          case 'photo':
            return Icons.photo;
          case 'video_file':
            return Icons.video_file;
          case 'audio_file':
            return Icons.audio_file;
          case 'text_snippet':
            return Icons.text_snippet;
          case 'table_chart':
            return Icons.table_chart;
          case 'slideshow':
            return Icons.slideshow;
          case 'folder_zip':
            return Icons.folder_zip;
          case 'camera':
            return Icons.camera_alt;
          case 'scanner':
            return Icons.scanner;
          case 'qr_code':
            return Icons.qr_code;
          case 'qr_code_scanner':
            return Icons.qr_code_scanner;
          case 'face':
            return Icons.face;
          case 'receipt':
            return Icons.receipt;
          case 'credit_card':
            return Icons.credit_card;
          case 'badge':
            return Icons.badge;
          case 'signature':
            return Icons.draw;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
    }

    // Parse string list
    List<String>? parseStringList(dynamic listValue) {
      if (listValue == null) return null;

      if (listValue is List) {
        return List<String>.from(listValue.map((e) => e.toString()));
      } else if (listValue is String) {
        // Handle comma-separated string
        return listValue.split(',').map((e) => e.trim()).toList();
      }

      return null;
    }

    // Parse map of string to string
    Map<String, String>? parseStringMap(dynamic mapValue) {
      if (mapValue == null) return null;

      if (mapValue is Map) {
        return Map<String, String>.from(
          mapValue.map(
            (key, value) => MapEntry(key.toString(), value.toString()),
          ),
        );
      }

      return null;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onFilesSelected'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFilesSelected'] = json['onFilesSelected'];
      useJsonCallbacks = true;
    }

    if (json['onClear'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onClear'] = json['onClear'];
      useJsonCallbacks = true;
    }

    if (json['onProcess'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onProcess'] = json['onProcess'];
      useJsonCallbacks = true;
    }

    if (json['onDataExtracted'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDataExtracted'] = json['onDataExtracted'];
      useJsonCallbacks = true;
    }

    if (json['onClassificationComplete'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onClassificationComplete'] =
          json['onClassificationComplete'];
      useJsonCallbacks = true;
    }

    if (json['onOcrComplete'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onOcrComplete'] = json['onOcrComplete'];
      useJsonCallbacks = true;
    }

    if (json['onValidationComplete'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onValidationComplete'] = json['onValidationComplete'];
      useJsonCallbacks = true;
    }

    if (json['onExport'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onExport'] = json['onExport'];
      useJsonCallbacks = true;
    }

    if (json['onImageEnhanced'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onImageEnhanced'] = json['onImageEnhanced'];
      useJsonCallbacks = true;
    }

    if (json['onBatchProcessComplete'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onBatchProcessComplete'] = json['onBatchProcessComplete'];
      useJsonCallbacks = true;
    }

    if (json['onError'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onError'] = json['onError'];
      useJsonCallbacks = true;
    }

    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    // Parse IRDR-specific configuration
    Map<String, dynamic>? irdrConfig;
    bool useJsonIrdrConfig = json['useJsonIrdrConfig'] as bool? ?? false;

    if (json['irdrConfig'] != null) {
      if (json['irdrConfig'] is Map) {
        irdrConfig = Map<String, dynamic>.from(json['irdrConfig'] as Map);
        useJsonIrdrConfig = true;
      } else if (json['irdrConfig'] is String) {
        try {
          irdrConfig =
              jsonDecode(json['irdrConfig'] as String) as Map<String, dynamic>;
          useJsonIrdrConfig = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON
    return FileIrdrWidget(
      isRequired: json['isRequired'] as bool? ?? false,
      allowMultiple: json['allowMultiple'] as bool? ?? false,
      allowedExtensions:
          parseStringList(json['allowedExtensions']) ??
          const ['pdf', 'jpg', 'jpeg', 'png', 'tiff', 'tif', 'bmp'],
      maxFileSizeBytes: json['maxFileSizeBytes'] as int?,
      maxFiles: json['maxFiles'] as int?,
      textColor: parseColor(json['textColor']) ?? Colors.black,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      borderColor: parseColor(json['borderColor']) ?? Colors.grey,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      fontSize:
          json['fontSize'] != null
              ? (json['fontSize'] as num).toDouble()
              : 16.0,
      fontWeight: parseFontWeight(json['fontWeight']),
      isCompact: json['isCompact'] as bool? ?? false,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      textAlign: parseTextAlign(json['textAlign']),
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      buttonText: json['buttonText'] as String? ?? 'Select Document',
      showIcon: json['showIcon'] as bool? ?? true,
      icon: parseIconData(json['icon']) ?? Icons.document_scanner,
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      showFileName: json['showFileName'] as bool? ?? true,
      showFileSize: json['showFileSize'] as bool? ?? true,
      showFileType: json['showFileType'] as bool? ?? true,
      showClearButton: json['showClearButton'] as bool? ?? true,
      showPreview: json['showPreview'] as bool? ?? true,
      uploadImmediately: json['uploadImmediately'] as bool? ?? false,
      showProgressBar: json['showProgressBar'] as bool? ?? true,
      allowDragDrop: json['allowDragDrop'] as bool? ?? true,
      enableOcr: json['enableOcr'] as bool? ?? false,
      enableClassification: json['enableClassification'] as bool? ?? false,
      enableDataExtraction: json['enableDataExtraction'] as bool? ?? false,
      showConfidence: json['showConfidence'] as bool? ?? false,
      showProcessingIndicator: json['showProcessingIndicator'] as bool? ?? true,
      autoProcess: json['autoProcess'] as bool? ?? true,
      showResults: json['showResults'] as bool? ?? true,
      enableEditing: json['enableEditing'] as bool? ?? true,
      showFieldLabels: json['showFieldLabels'] as bool? ?? true,
      highlightFields: json['highlightFields'] as bool? ?? true,
      highlightColor: parseColor(json['highlightColor']) ?? Color(0xFF0058FF),
      confidenceThreshold:
          json['confidenceThreshold'] != null
              ? (json['confidenceThreshold'] as num).toDouble()
              : 0.7,
      documentTypes: parseStringList(json['documentTypes']),
      fieldMappings: parseStringMap(json['fieldMappings']),
      showCamera: json['showCamera'] as bool? ?? false,
      enhanceImage: json['enhanceImage'] as bool? ?? false,
      detectBoundaries: json['detectBoundaries'] as bool? ?? false,
      cropToDocument: json['cropToDocument'] as bool? ?? false,
      rotateDocument: json['rotateDocument'] as bool? ?? false,
      convertToPdf: json['convertToPdf'] as bool? ?? false,
      saveOriginal: json['saveOriginal'] as bool? ?? true,
      outputDirectory: json['outputDirectory'] as String?,
      compressOutput: json['compressOutput'] as bool? ?? false,
      enableBatchProcessing: json['enableBatchProcessing'] as bool? ?? false,
      batchSize: json['batchSize'] as int?,
      showBatchProgress: json['showBatchProgress'] as bool? ?? true,
      validateData: json['validateData'] as bool? ?? false,
      showValidationErrors: json['showValidationErrors'] as bool? ?? true,
      enableManualOverride: json['enableManualOverride'] as bool? ?? true,
      showExportButton: json['showExportButton'] as bool? ?? false,
      exportFormats: parseStringList(json['exportFormats']),
      showScanHistory: json['showScanHistory'] as bool? ?? false,
      maxHistoryItems: json['maxHistoryItems'] as int?,
      enableCloudSync: json['enableCloudSync'] as bool? ?? false,
      cloudProvider: json['cloudProvider'] as String?,
      showThumbnails: json['showThumbnails'] as bool? ?? true,
      thumbnailSize:
          json['thumbnailSize'] != null
              ? (json['thumbnailSize'] as num).toDouble()
              : 60.0,
      enableAnnotation: json['enableAnnotation'] as bool? ?? false,
      annotationColors:
          json['annotationColors'] != null
              ? List<Color>.from(
                (json['annotationColors'] as List).map(
                  (c) => parseColor(c) ?? Colors.blue,
                ),
              )
              : null,
      showZoomControls: json['showZoomControls'] as bool? ?? false,
      maxZoom:
          json['maxZoom'] != null ? (json['maxZoom'] as num).toDouble() : 3.0,
      enablePageNavigation: json['enablePageNavigation'] as bool? ?? true,
      showPageIndicator: json['showPageIndicator'] as bool? ?? true,
      enableFullScreen: json['enableFullScreen'] as bool? ?? false,
      showRotationControls: json['showRotationControls'] as bool? ?? false,
      enableTextSearch: json['enableTextSearch'] as bool? ?? false,
      highlightSearchResults: json['highlightSearchResults'] as bool? ?? true,
      searchHighlightColor:
          parseColor(json['searchHighlightColor']) ?? Colors.yellow,
      showSearchCount: json['showSearchCount'] as bool? ?? true,
      enableTemplateMatching: json['enableTemplateMatching'] as bool? ?? false,
      templates: parseStringList(json['templates']),
      showTemplateSelector: json['showTemplateSelector'] as bool? ?? false,
      enableAutoCorrection: json['enableAutoCorrection'] as bool? ?? false,
      correctionThreshold:
          json['correctionThreshold'] != null
              ? (json['correctionThreshold'] as num).toDouble()
              : 0.8,
      showCorrectionSuggestions:
          json['showCorrectionSuggestions'] as bool? ?? true,
      enableSignatureCapture: json['enableSignatureCapture'] as bool? ?? false,
      requireSignature: json['requireSignature'] as bool? ?? false,
      signatureColor: parseColor(json['signatureColor']) ?? Colors.blue,
      signatureThickness:
          json['signatureThickness'] != null
              ? (json['signatureThickness'] as num).toDouble()
              : 2.0,
      clearSignatureOnSave: json['clearSignatureOnSave'] as bool? ?? true,
      showSignatureDate: json['showSignatureDate'] as bool? ?? true,
      enableFaceDetection: json['enableFaceDetection'] as bool? ?? false,
      highlightFaces: json['highlightFaces'] as bool? ?? true,
      blurFaces: json['blurFaces'] as bool? ?? false,
      enableIdCardScan: json['enableIdCardScan'] as bool? ?? false,
      extractIdCardData: json['extractIdCardData'] as bool? ?? true,
      validateIdCard: json['validateIdCard'] as bool? ?? true,
      enableReceiptScan: json['enableReceiptScan'] as bool? ?? false,
      extractReceiptItems: json['extractReceiptItems'] as bool? ?? true,
      calculateReceiptTotal: json['calculateReceiptTotal'] as bool? ?? true,
      enableBarcodeScan: json['enableBarcodeScan'] as bool? ?? false,
      showBarcodeData: json['showBarcodeData'] as bool? ?? true,
      barcodeTypes: parseStringList(json['barcodeTypes']),
      enableQrScan: json['enableQrScan'] as bool? ?? false,
      decodeQrContent: json['decodeQrContent'] as bool? ?? true,
      enableMrzScan: json['enableMrzScan'] as bool? ?? false,
      validateMrz: json['validateMrz'] as bool? ?? true,
      extractMrzData: json['extractMrzData'] as bool? ?? true,
      width:
          json['width'] != null
              ? (json['width'].toString().toLowerCase() == 'infinity'
                  ? double.infinity
                  : (json['width'] as num).toDouble())
              : double.infinity,
      height: json['height'] != null ? (json['height'] as num).toDouble() : 0,
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      // Advanced interaction properties
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : {},
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      // IRDR-specific JSON configuration
      useJsonIrdrConfig: useJsonIrdrConfig,
      irdrConfig: irdrConfig,
    );
  }

  /// Converts the widget configuration to a JSON map
  ///
  /// This method allows for serializing the widget's configuration to JSON,
  /// which can be useful for saving configurations or sending them to a server.
  Map<String, dynamic> toJson() {
    // Convert color to hex string
    String? colorToHex(Color? color) {
      if (color == null) return null;

      // Use a simple approach that works with all Flutter versions
      final hexString = color.toString();
      // Color(0xAARRGGBB) format -> extract RRGGBB part
      final hex = hexString.replaceAll('Color(0x', '').replaceAll(')', '');
      return '#${hex.substring(2)}'; // Skip alpha channel
    }

    // Convert text alignment to string
    String textAlignToString(TextAlign align) {
      switch (align) {
        case TextAlign.center:
          return 'center';
        case TextAlign.end:
          return 'end';
        case TextAlign.left:
          return 'left';
        case TextAlign.right:
          return 'right';
        case TextAlign.justify:
          return 'justify';
        case TextAlign.start:
          return 'start';
      }
    }

    // Convert font weight to int
    int fontWeightToInt(FontWeight weight) {
      if (weight == FontWeight.w100) return 100;
      if (weight == FontWeight.w200) return 200;
      if (weight == FontWeight.w300) return 300;
      if (weight == FontWeight.w400 || weight == FontWeight.normal) return 400;
      if (weight == FontWeight.w500) return 500;
      if (weight == FontWeight.w600) return 600;
      if (weight == FontWeight.w700 || weight == FontWeight.bold) return 700;
      if (weight == FontWeight.w800) return 800;
      if (weight == FontWeight.w900) return 900;
      return 400;
    }

    // Convert icon data to string
    String? iconDataToString(IconData? icon) {
      if (icon == null) return null;

      if (icon == Icons.document_scanner) return 'document_scanner';
      if (icon == Icons.upload_file) return 'upload_file';
      if (icon == Icons.file_upload) return 'file_upload';
      if (icon == Icons.attach_file) return 'attach_file';
      if (icon == Icons.cloud_upload) return 'cloud_upload';
      if (icon == Icons.add) return 'add';
      if (icon == Icons.add_circle) return 'add_circle';
      if (icon == Icons.add_circle_outline) return 'add_circle_outline';
      if (icon == Icons.folder) return 'folder';
      if (icon == Icons.folder_open) return 'folder_open';
      if (icon == Icons.description) return 'description';
      if (icon == Icons.insert_drive_file) return 'insert_drive_file';
      if (icon == Icons.picture_as_pdf) return 'picture_as_pdf';
      if (icon == Icons.image) return 'image';
      if (icon == Icons.photo) return 'photo';
      if (icon == Icons.video_file) return 'video_file';
      if (icon == Icons.audio_file) return 'audio_file';
      if (icon == Icons.text_snippet) return 'text_snippet';
      if (icon == Icons.table_chart) return 'table_chart';
      if (icon == Icons.slideshow) return 'slideshow';
      if (icon == Icons.folder_zip) return 'folder_zip';
      if (icon == Icons.camera_alt) return 'camera';
      if (icon == Icons.scanner) return 'scanner';
      if (icon == Icons.qr_code) return 'qr_code';
      if (icon == Icons.qr_code_scanner) return 'qr_code_scanner';
      if (icon == Icons.face) return 'face';
      if (icon == Icons.receipt) return 'receipt';
      if (icon == Icons.credit_card) return 'credit_card';
      if (icon == Icons.badge) return 'badge';
      if (icon == Icons.draw) return 'signature';

      return null;
    }

    // Convert edge insets to map
    Map<String, dynamic>? edgeInsetsToMap(EdgeInsetsGeometry insets) {
      if (insets is EdgeInsets) {
        if (insets.left == insets.top &&
            insets.left == insets.right &&
            insets.left == insets.bottom) {
          return {'all': insets.left};
        } else if (insets.left == insets.right && insets.top == insets.bottom) {
          return {'horizontal': insets.left, 'vertical': insets.top};
        } else {
          return {
            'left': insets.left,
            'top': insets.top,
            'right': insets.right,
            'bottom': insets.bottom,
          };
        }
      }
      return null;
    }

    // Create the JSON map
    final Map<String, dynamic> json = {
      'isRequired': isRequired,
      'allowMultiple': allowMultiple,
      'allowedExtensions': allowedExtensions,
      'maxFileSizeBytes': maxFileSizeBytes,
      'maxFiles': maxFiles,
      'textColor': colorToHex(textColor),
      'backgroundColor': colorToHex(backgroundColor),
      'borderColor': colorToHex(borderColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'fontSize': fontSize,
      'fontWeight': fontWeightToInt(fontWeight),
      'isCompact': isCompact,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isDarkTheme': isDarkTheme,
      'textAlign': textAlignToString(textAlign),
      'label': label,
      'hint': hint,
      'helperText': helperText,
      'errorText': errorText,
      'buttonText': buttonText,
      'showIcon': showIcon,
      'icon': iconDataToString(icon),
      'isReadOnly': isReadOnly,
      'isDisabled': isDisabled,
      'showFileName': showFileName,
      'showFileSize': showFileSize,
      'showFileType': showFileType,
      'showClearButton': showClearButton,
      'showPreview': showPreview,
      'uploadImmediately': uploadImmediately,
      'showProgressBar': showProgressBar,
      'allowDragDrop': allowDragDrop,
      'width': width == double.infinity ? 'infinity' : width,
      'height': height,
      'padding': edgeInsetsToMap(padding),
      'margin': edgeInsetsToMap(margin),
      'enableFeedback': enableFeedback,
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonValidation': useJsonValidation,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonIrdrConfig': useJsonIrdrConfig,

      // IRDR-specific properties
      'enableOcr': enableOcr,
      'enableClassification': enableClassification,
      'enableDataExtraction': enableDataExtraction,
      'showConfidence': showConfidence,
      'showProcessingIndicator': showProcessingIndicator,
      'autoProcess': autoProcess,
      'showResults': showResults,
      'enableEditing': enableEditing,
      'showFieldLabels': showFieldLabels,
      'highlightFields': highlightFields,
      'highlightColor': colorToHex(highlightColor),
      'confidenceThreshold': confidenceThreshold,
      'documentTypes': documentTypes,
      'showCamera': showCamera,
      'enhanceImage': enhanceImage,
      'detectBoundaries': detectBoundaries,
      'cropToDocument': cropToDocument,
      'rotateDocument': rotateDocument,
      'convertToPdf': convertToPdf,
      'saveOriginal': saveOriginal,
      'outputDirectory': outputDirectory,
      'compressOutput': compressOutput,
      'enableBatchProcessing': enableBatchProcessing,
      'batchSize': batchSize,
      'showBatchProgress': showBatchProgress,
      'validateData': validateData,
      'showValidationErrors': showValidationErrors,
      'enableManualOverride': enableManualOverride,
      'showExportButton': showExportButton,
      'exportFormats': exportFormats,
      'showScanHistory': showScanHistory,
      'maxHistoryItems': maxHistoryItems,
      'enableCloudSync': enableCloudSync,
      'cloudProvider': cloudProvider,
      'showThumbnails': showThumbnails,
      'thumbnailSize': thumbnailSize,
      'enableAnnotation': enableAnnotation,
      'showZoomControls': showZoomControls,
      'maxZoom': maxZoom,
      'enablePageNavigation': enablePageNavigation,
      'showPageIndicator': showPageIndicator,
      'enableFullScreen': enableFullScreen,
      'showRotationControls': showRotationControls,
      'enableTextSearch': enableTextSearch,
      'highlightSearchResults': highlightSearchResults,
      'searchHighlightColor': colorToHex(searchHighlightColor),
      'showSearchCount': showSearchCount,
      'enableTemplateMatching': enableTemplateMatching,
      'templates': templates,
      'showTemplateSelector': showTemplateSelector,
      'enableAutoCorrection': enableAutoCorrection,
      'correctionThreshold': correctionThreshold,
      'showCorrectionSuggestions': showCorrectionSuggestions,
      'enableSignatureCapture': enableSignatureCapture,
      'requireSignature': requireSignature,
      'signatureColor': colorToHex(signatureColor),
      'signatureThickness': signatureThickness,
      'clearSignatureOnSave': clearSignatureOnSave,
      'showSignatureDate': showSignatureDate,
      'enableFaceDetection': enableFaceDetection,
      'highlightFaces': highlightFaces,
      'blurFaces': blurFaces,
      'enableIdCardScan': enableIdCardScan,
      'extractIdCardData': extractIdCardData,
      'validateIdCard': validateIdCard,
      'enableReceiptScan': enableReceiptScan,
      'extractReceiptItems': extractReceiptItems,
      'calculateReceiptTotal': calculateReceiptTotal,
      'enableBarcodeScan': enableBarcodeScan,
      'showBarcodeData': showBarcodeData,
      'barcodeTypes': barcodeTypes,
      'enableQrScan': enableQrScan,
      'decodeQrContent': decodeQrContent,
      'enableMrzScan': enableMrzScan,
      'validateMrz': validateMrz,
      'extractMrzData': extractMrzData,
    };

    // Add hover and focus colors if they exist
    if (hoverColor != null) {
      json['hoverColor'] = colorToHex(hoverColor);
    }

    if (focusColor != null) {
      json['focusColor'] = colorToHex(focusColor);
    }

    // Add annotation colors if they exist
    if (annotationColors != null && annotationColors!.isNotEmpty) {
      json['annotationColors'] =
          annotationColors!.map((color) => colorToHex(color)).toList();
    }

    // Add field mappings if they exist
    if (fieldMappings != null && fieldMappings!.isNotEmpty) {
      json['fieldMappings'] = fieldMappings;
    }

    // Add callbacks if they exist
    if (jsonCallbacks != null && jsonCallbacks!.isNotEmpty) {
      json['callbacks'] = jsonCallbacks;
    }

    // Add IRDR config if it exists
    if (irdrConfig != null && irdrConfig!.isNotEmpty) {
      json['irdrConfig'] = irdrConfig;
    }

    return json;
  }

  @override
  State<FileIrdrWidget> createState() => _FileIrdrWidgetState();
}

class _FileIrdrWidgetState extends State<FileIrdrWidget> {
  List<PlatformFile> _selectedFiles = [];
  String? _errorText;
  bool _isValid = true;
  bool _isProcessing = false;
  bool _isDragging = false;
  double _processingProgress = 0.0;
  Map<String, dynamic> _extractedData = {};
  String? _classificationResult;
  String? _ocrResult;
  bool _isHovered = false;
  bool _isFocused = false;

  // Map to store dynamic state for callbacks
  final Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;

  // Map to store IRDR-specific configuration from JSON
  Map<String, dynamic>? _irdrConfig;

  // Focus node for handling focus events
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState.addAll(widget.callbackState!);
    }

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Parse IRDR-specific configuration if provided
    if (widget.irdrConfig != null) {
      _irdrConfig = Map<String, dynamic>.from(widget.irdrConfig!);

      // Apply initial IRDR configuration if enabled
      if (widget.useJsonIrdrConfig) {
        _applyIrdrConfig();
      }
    }

    // Execute onInit callback if defined in JSON
    _executeJsonCallback('onInit');
  }

  @override
  void dispose() {
    // Clean up focus node if we created it
    if (widget.focusNode == null) {
      _focusNode.removeListener(_handleFocusChange);
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Handles focus changes
  void _handleFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;

      // Call the onFocus callback if provided
      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }

      // Execute JSON callback if defined
      _executeJsonCallback('onFocus', _isFocused);
    });
  }

  /// Handles hover changes
  void _handleHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;

      // Call the onHover callback if provided
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }

      // Execute JSON callback if defined
      _executeJsonCallback('onHover', isHovered);
    });
  }

  /// Executes a callback defined in JSON
  ///
  /// This method interprets and executes a callback defined in the JSON configuration.
  /// It supports various callback types and provides access to the current state.
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      _callbackState['selectedFiles'] =
          _selectedFiles
              .map(
                (file) => {
                  'name': file.name,
                  'size': file.size,
                  'path': file.path,
                  'extension': file.extension,
                },
              )
              .toList();
      _callbackState['isValid'] = _isValid;
      _callbackState['isProcessing'] = _isProcessing;
      _callbackState['processingProgress'] = _processingProgress;
      _callbackState['extractedData'] = _extractedData;
      _callbackState['classificationResult'] = _classificationResult;
      _callbackState['ocrResult'] = _ocrResult;

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue = _selectedFiles.map((file) => file.name).toList();
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the selected files
  ///
  /// This method applies validation rules defined in the JSON configuration.
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    // Example: Apply validation rules
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply required validation
        if (rules.containsKey('required') && rules['required'] == true) {
          if (_selectedFiles.isEmpty) {
            setState(() {
              _errorText = 'File selection is required';
              _isValid = false;
            });
            return;
          }
        }

        // Apply max files validation
        if (rules.containsKey('maxFiles') && rules['maxFiles'] is int) {
          final maxFiles = rules['maxFiles'] as int;
          if (_selectedFiles.length > maxFiles) {
            setState(() {
              _errorText = 'Maximum $maxFiles files allowed';
              _isValid = false;
            });
            return;
          }
        }

        // Apply max file size validation
        if (rules.containsKey('maxFileSizeBytes') &&
            rules['maxFileSizeBytes'] is int) {
          final maxFileSizeBytes = rules['maxFileSizeBytes'] as int;
          for (final file in _selectedFiles) {
            if (file.size > maxFileSizeBytes) {
              setState(() {
                _errorText =
                    'File size exceeds ${_formatFileSize(maxFileSizeBytes)}';
                _isValid = false;
              });
              return;
            }
          }
        }

        // Apply allowed extensions validation
        if (rules.containsKey('allowedExtensions') &&
            rules['allowedExtensions'] is List) {
          final allowedExtensions = List<String>.from(
            (rules['allowedExtensions'] as List).map((e) => e.toString()),
          );
          for (final file in _selectedFiles) {
            final extension = path
                .extension(file.name)
                .toLowerCase()
                .replaceFirst('.', '');
            if (!allowedExtensions
                .map((e) => e.toLowerCase().replaceFirst('.', ''))
                .contains(extension)) {
              setState(() {
                _errorText =
                    'Invalid file type. Allowed: ${allowedExtensions.join(', ')}';
                _isValid = false;
              });
              return;
            }
          }
        }

        // Apply blocked extensions validation
        if (rules.containsKey('blockedExtensions') &&
            rules['blockedExtensions'] is List) {
          final blockedExtensions = List<String>.from(
            (rules['blockedExtensions'] as List).map((e) => e.toString()),
          );
          for (final file in _selectedFiles) {
            final extension = path
                .extension(file.name)
                .toLowerCase()
                .replaceFirst('.', '');
            if (blockedExtensions
                .map((e) => e.toLowerCase().replaceFirst('.', ''))
                .contains(extension)) {
              setState(() {
                _errorText = 'File type not allowed: .$extension';
                _isValid = false;
              });
              return;
            }
          }
        }

        // Apply custom validation
        if (rules.containsKey('custom') && rules['custom'] is String) {
          final customRule = rules['custom'] as String;

          // Example: Check if all files have the same extension
          if (customRule == 'sameExtension' && _selectedFiles.length > 1) {
            final firstExtension =
                path.extension(_selectedFiles.first.name).toLowerCase();
            for (final file in _selectedFiles.skip(1)) {
              final extension = path.extension(file.name).toLowerCase();
              if (extension != firstExtension) {
                setState(() {
                  _errorText = 'All files must have the same extension';
                  _isValid = false;
                });
                return;
              }
            }
          }
        }
      }
    }

    setState(() {
      _errorText = widget.errorText;
      _isValid = true;
    });
  }

  /// Applies JSON styling to the widget
  ///
  /// This method applies styling rules defined in the JSON configuration.
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current value
  ///
  /// This method applies formatting rules defined in the JSON configuration.
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  /// Applies IRDR-specific configuration to the widget
  ///
  /// This method applies IRDR-specific rules defined in the JSON configuration.
  void _applyIrdrConfig() {
    if (_irdrConfig == null || !widget.useJsonIrdrConfig) return;

    // Example: Apply IRDR-specific configuration
    if (_irdrConfig!.containsKey('documentTypes')) {
      // This would be implemented to configure document types
      // Not fully implemented in this example
    }

    if (_irdrConfig!.containsKey('fieldMappings')) {
      // This would be implemented to configure field mappings
      // Not fully implemented in this example
    }

    if (_irdrConfig!.containsKey('processingOptions')) {
      // This would be implemented to configure processing options
      // Not fully implemented in this example
    }
  }

  /// Validates the selected files
  bool _validateFiles() {
    if (_selectedFiles.isEmpty) {
      if (widget.isRequired) {
        setState(() {
          _errorText = 'File selection is required';
          _isValid = false;
        });
        return false;
      } else {
        setState(() {
          _errorText = null;
          _isValid = true;
        });
        return true;
      }
    }

    // Check file count
    if (widget.maxFiles != null && _selectedFiles.length > widget.maxFiles!) {
      setState(() {
        _errorText = 'Maximum ${widget.maxFiles} files allowed';
        _isValid = false;
      });
      return false;
    }

    // Check file size
    if (widget.maxFileSizeBytes != null) {
      for (final file in _selectedFiles) {
        if (file.size > widget.maxFileSizeBytes!) {
          setState(() {
            _errorText =
                'File size exceeds ${_formatFileSize(widget.maxFileSizeBytes!)}';
            _isValid = false;
          });
          return false;
        }
      }
    }

    // Check file extensions
    if (widget.allowedExtensions != null &&
        widget.allowedExtensions!.isNotEmpty) {
      for (final file in _selectedFiles) {
        final extension = path
            .extension(file.name)
            .toLowerCase()
            .replaceFirst('.', '');
        if (!widget.allowedExtensions!
            .map((e) => e.toLowerCase().replaceFirst('.', ''))
            .contains(extension)) {
          setState(() {
            _errorText =
                'Invalid file type. Allowed: ${widget.allowedExtensions!.join(', ')}';
            _isValid = false;
          });
          return false;
        }
      }
    }

    setState(() {
      _errorText = null;
      _isValid = true;
    });
    return true;
  }

  /// Formats file size for display
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Opens the file picker dialog
  Future<void> _pickFiles() async {
    if (widget.isDisabled || widget.isReadOnly) return;

    // Execute onBeforePickFiles callback if defined in JSON
    _executeJsonCallback('onBeforePickFiles');

    try {
      // Get file type and allowed extensions from JSON config if available
      FileType effectiveFileType = FileType.custom;
      List<String>? effectiveAllowedExtensions = widget.allowedExtensions;

      if (widget.useJsonIrdrConfig && _irdrConfig != null) {
        if (_irdrConfig!.containsKey('fileType')) {
          final fileTypeStr = _irdrConfig!['fileType'] as String?;
          if (fileTypeStr != null) {
            switch (fileTypeStr.toLowerCase()) {
              case 'any':
                effectiveFileType = FileType.any;
                break;
              case 'image':
                effectiveFileType = FileType.image;
                break;
              case 'video':
                effectiveFileType = FileType.video;
                break;
              case 'audio':
                effectiveFileType = FileType.audio;
                break;
              case 'media':
                effectiveFileType = FileType.media;
                break;
              case 'custom':
                effectiveFileType = FileType.custom;
                break;
            }
          }
        }

        if (_irdrConfig!.containsKey('allowedExtensions')) {
          final extensions = _irdrConfig!['allowedExtensions'];
          if (extensions is List) {
            effectiveAllowedExtensions = List<String>.from(
              extensions.map((e) => e.toString()),
            );
          } else if (extensions is String) {
            effectiveAllowedExtensions =
                extensions.split(',').map((e) => e.trim()).toList();
          }
        }
      }

      final result = await FilePicker.platform.pickFiles(
        type: effectiveFileType,
        allowMultiple: widget.allowMultiple,
        allowedExtensions:
            effectiveFileType == FileType.custom
                ? effectiveAllowedExtensions
                : null,
      );

      if (result != null) {
        setState(() {
          _selectedFiles = result.files;
        });

        // Validate files using JSON validation if enabled
        if (widget.useJsonValidation) {
          _applyJsonValidation();
        } else {
          _validateFiles();
        }

        // Call standard callback
        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(_selectedFiles);
        }

        // Execute onFilesSelected callback if defined in JSON
        _executeJsonCallback(
          'onFilesSelected',
          _selectedFiles.map((file) => file.name).toList(),
        );

        if (widget.autoProcess && _isValid) {
          _processFiles();
        }
      } else {
        // Execute onCancelled callback if defined in JSON
        _executeJsonCallback('onCancelled');
      }
    } catch (e) {
      setState(() {
        _errorText = 'Error picking files: $e';
        _isValid = false;
      });

      // Call standard error callback
      if (widget.onError != null) {
        widget.onError!('Error picking files: $e');
      }

      // Execute onError callback if defined in JSON
      _executeJsonCallback('onError', 'Error picking files: $e');
    }
  }

  /// Processes the selected files for IRDR
  Future<void> _processFiles() async {
    if (_selectedFiles.isEmpty || !_isValid) return;

    // Execute onBeforeProcess callback if defined in JSON
    _executeJsonCallback('onBeforeProcess');

    setState(() {
      _isProcessing = true;
      _processingProgress = 0.0;
    });

    // Execute onProcessStart callback if defined in JSON
    _executeJsonCallback(
      'onProcessStart',
      _selectedFiles.map((file) => file.name).toList(),
    );

    try {
      // Get processing configuration from JSON if available
      int progressSteps = 100; // Default: 100 steps
      int stepDelayMs = 50; // Default: 50ms per step

      if (widget.useJsonIrdrConfig && _irdrConfig != null) {
        if (_irdrConfig!.containsKey('progressSteps')) {
          progressSteps = _irdrConfig!['progressSteps'] as int? ?? 100;
        }

        if (_irdrConfig!.containsKey('stepDelayMs')) {
          stepDelayMs = _irdrConfig!['stepDelayMs'] as int? ?? 50;
        }
      }

      // Simulate processing with progress updates
      for (int i = 0; i < progressSteps; i++) {
        await Future.delayed(Duration(milliseconds: stepDelayMs));
        final progress = i / progressSteps;
        setState(() {
          _processingProgress = progress;
        });

        // Execute onProcessProgress callback if defined in JSON
        if (i % 10 == 0) {
          // Only call every 10 steps to avoid too many callbacks
          _executeJsonCallback('onProcessProgress', {
            'progress': progress,
            'percentage': (progress * 100).toInt(),
            'step': i,
            'totalSteps': progressSteps,
          });
        }
      }

      // Get simulated results from JSON if available
      String? ocrResult;
      String? classificationResult;
      Map<String, dynamic>? extractedData;

      if (widget.useJsonIrdrConfig && _irdrConfig != null) {
        if (_irdrConfig!.containsKey('simulatedResults')) {
          final simulatedResults = _irdrConfig!['simulatedResults'];
          if (simulatedResults is Map<String, dynamic>) {
            if (simulatedResults.containsKey('ocrResult')) {
              ocrResult = simulatedResults['ocrResult'] as String?;
            }

            if (simulatedResults.containsKey('classificationResult')) {
              classificationResult =
                  simulatedResults['classificationResult'] as String?;
            }

            if (simulatedResults.containsKey('extractedData')) {
              extractedData =
                  simulatedResults['extractedData'] as Map<String, dynamic>?;
            }
          }
        }
      }

      // Simulate results
      setState(() {
        if (widget.enableOcr) {
          _ocrResult = ocrResult ?? 'Sample OCR text extracted from document';
        }

        if (widget.enableClassification) {
          _classificationResult =
              classificationResult ??
              (widget.documentTypes != null && widget.documentTypes!.isNotEmpty
                  ? widget.documentTypes!.first
                  : 'Invoice');
        }

        if (widget.enableDataExtraction) {
          _extractedData =
              extractedData ??
              {
                'date': '2023-05-15',
                'invoice_number': 'INV-12345',
                'total_amount': '\$1,234.56',
                'vendor': 'Sample Vendor Inc.',
                'items': [
                  {'description': 'Item 1', 'quantity': 2, 'price': '\$100.00'},
                  {
                    'description': 'Item 2',
                    'quantity': 1,
                    'price': '\$1,034.56',
                  },
                ],
              };
        }
      });

      // Call standard callbacks
      if (widget.onProcess != null) {
        widget.onProcess!(_selectedFiles);
      }

      if (widget.enableOcr &&
          widget.onOcrComplete != null &&
          _ocrResult != null) {
        widget.onOcrComplete!(_ocrResult!);
      }

      if (widget.enableClassification &&
          widget.onClassificationComplete != null &&
          _classificationResult != null) {
        widget.onClassificationComplete!(_classificationResult!);
      }

      if (widget.enableDataExtraction && widget.onDataExtracted != null) {
        widget.onDataExtracted!(_extractedData);
      }

      // Execute JSON callbacks
      if (widget.enableOcr && _ocrResult != null) {
        _executeJsonCallback('onOcrComplete', _ocrResult);
      }

      if (widget.enableClassification && _classificationResult != null) {
        _executeJsonCallback('onClassificationComplete', _classificationResult);
      }

      if (widget.enableDataExtraction) {
        _executeJsonCallback('onDataExtracted', _extractedData);
      }

      // Execute onProcessComplete callback if defined in JSON
      _executeJsonCallback('onProcessComplete', {
        'files': _selectedFiles.map((file) => file.name).toList(),
        'ocrResult': _ocrResult,
        'classificationResult': _classificationResult,
        'extractedData': _extractedData,
      });
    } catch (e) {
      setState(() {
        _errorText = 'Error processing files: $e';
        _isValid = false;
      });

      // Call standard error callback
      if (widget.onError != null) {
        widget.onError!('Error processing files: $e');
      }

      // Execute onProcessError callback if defined in JSON
      _executeJsonCallback('onProcessError', 'Error processing files: $e');
    } finally {
      setState(() {
        _isProcessing = false;
        _processingProgress = 1.0;
      });
    }
  }

  /// Clears the selected files
  void _clearFiles() {
    // Execute onBeforeClear callback if defined in JSON
    _executeJsonCallback('onBeforeClear');

    // Store the files being cleared for the callback
    final clearedFiles = List<PlatformFile>.from(_selectedFiles);
    final clearedData = Map<String, dynamic>.from(_extractedData);
    final clearedClassification = _classificationResult;
    final clearedOcr = _ocrResult;

    setState(() {
      _selectedFiles = [];
      _errorText = null;
      _isValid = true;
      _isProcessing = false;
      _processingProgress = 0.0;
      _ocrResult = null;
      _classificationResult = null;
      _extractedData = {};
    });

    // Call standard callback
    if (widget.onClear != null) {
      widget.onClear!();
    }

    // Execute onClear callback if defined in JSON
    _executeJsonCallback('onClear', {
      'files': clearedFiles.map((file) => file.name).toList(),
      'extractedData': clearedData,
      'classificationResult': clearedClassification,
      'ocrResult': clearedOcr,
    });
  }

  /// Builds the file preview widget
  Widget _buildFilePreview(PlatformFile file) {
    final extension = path.extension(file.name).toLowerCase();
    final iconColor = widget.isDarkTheme ? Colors.white : Colors.grey.shade700;

    // Check if it's an image file
    if ([
          '.jpg',
          '.jpeg',
          '.png',
          '.gif',
          '.webp',
          '.bmp',
        ].contains(extension) &&
        file.path != null) {
      try {
        return ClipRRect(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          child: Image.file(
            File(file.path!),
            width: widget.thumbnailSize,
            height: widget.thumbnailSize,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // Fallback if image can't be loaded
              return Container(
                width: widget.thumbnailSize,
                height: widget.thumbnailSize,
                color: Colors.grey.shade300,
                child: Icon(Icons.image, color: Colors.grey),
              );
            },
          ),
        );
      } catch (e) {
        // Fallback if there's an error
        return Container(
          width: widget.thumbnailSize,
          height: widget.thumbnailSize,
          color: Colors.grey.shade300,
          child: Icon(Icons.image, color: Colors.grey),
        );
      }
    }

    // For other file types, show an icon based on the file type
    IconData iconData;
    if (extension == '.pdf') {
      iconData = Icons.picture_as_pdf;
    } else if (['.doc', '.docx'].contains(extension)) {
      iconData = Icons.description;
    } else if (['.xls', '.xlsx', '.csv'].contains(extension)) {
      iconData = Icons.table_chart;
    } else if (['.ppt', '.pptx'].contains(extension)) {
      iconData = Icons.slideshow;
    } else if (['.txt', '.rtf'].contains(extension)) {
      iconData = Icons.text_snippet;
    } else if (['.zip', '.rar', '.7z'].contains(extension)) {
      iconData = Icons.archive;
    } else {
      iconData = Icons.insert_drive_file;
    }

    return Container(
      width: widget.thumbnailSize,
      height: widget.thumbnailSize,
      decoration: BoxDecoration(
        color: iconColor.withAlpha(25),
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Center(
        child: Icon(iconData, color: iconColor, size: widget.thumbnailSize / 2),
      ),
    );
  }

  /// Builds the file info widget
  Widget _buildFileInfo(PlatformFile file) {
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showFileName)
            Text(
              file.name,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          if (widget.showFileSize || widget.showFileType)
            Row(
              children: [
                if (widget.showFileSize)
                  Text(
                    _formatFileSize(file.size),
                    style: TextStyle(
                      color: effectiveTextColor.withAlpha(179),
                      fontSize: widget.fontSize * 0.8,
                    ),
                  ),
                if (widget.showFileSize && widget.showFileType)
                  Text(
                    ' • ',
                    style: TextStyle(
                      color: effectiveTextColor.withAlpha(179),
                      fontSize: widget.fontSize * 0.8,
                    ),
                  ),
                if (widget.showFileType)
                  Text(
                    path
                        .extension(file.name)
                        .toUpperCase()
                        .replaceFirst('.', ''),
                    style: TextStyle(
                      color: effectiveTextColor.withAlpha(179),
                      fontSize: widget.fontSize * 0.8,
                    ),
                  ),
              ],
            ),
        ],
      ),
    );
  }

  /// Builds the list of selected files
  Widget _buildSelectedFiles() {
    if (_selectedFiles.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(_selectedFiles.length, (index) {
          final file = _selectedFiles[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 8.0),
            elevation: 0,
            color:
                widget.isDarkTheme
                    ? Colors.grey.shade800
                    : Colors.grey.shade100,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(widget.borderRadius),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  if (widget.showPreview) ...[
                    _buildFilePreview(file),
                    const SizedBox(width: 8),
                  ],
                  _buildFileInfo(file),
                  if (widget.showClearButton &&
                      !widget.isDisabled &&
                      !widget.isReadOnly)
                    IconButton(
                      icon: const Icon(Icons.close, size: 18.0),
                      onPressed: () {
                        setState(() {
                          _selectedFiles.removeAt(index);
                        });
                        _validateFiles();
                      },
                      tooltip: 'Remove',
                    ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  /// Builds the processing indicator
  Widget _buildProcessingIndicator() {
    if (!_isProcessing && _processingProgress == 0.0) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _isProcessing ? 'Processing...' : 'Processing complete',
            style: TextStyle(
              color: widget.textColor,
              fontSize: widget.fontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8.0),
          LinearProgressIndicator(
            value: _processingProgress,
            backgroundColor: Colors.grey.shade300,
            valueColor: AlwaysStoppedAnimation<Color>(
              widget.isDarkTheme ? Colors.blue.shade300 : Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the results section
  Widget _buildResults() {
    if (!widget.showResults ||
        (_ocrResult == null &&
            _classificationResult == null &&
            _extractedData.isEmpty)) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Results',
            style: TextStyle(
              color: widget.textColor,
              fontSize: widget.fontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8.0),
          if (widget.enableClassification && _classificationResult != null) ...[
            Text(
              'Document Type:',
              style: TextStyle(
                color: widget.textColor,
                fontSize: widget.fontSize * 0.9,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              _classificationResult!,
              style: TextStyle(
                color: widget.textColor,
                fontSize: widget.fontSize * 0.9,
              ),
            ),
            const SizedBox(height: 8.0),
          ],
          if (widget.enableOcr && _ocrResult != null) ...[
            Text(
              'OCR Text:',
              style: TextStyle(
                color: widget.textColor,
                fontSize: widget.fontSize * 0.9,
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color:
                    widget.isDarkTheme
                        ? Colors.grey.shade800
                        : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(widget.borderRadius),
              ),
              child: Text(
                _ocrResult!,
                style: TextStyle(
                  color: widget.textColor,
                  fontSize: widget.fontSize * 0.9,
                ),
              ),
            ),
            const SizedBox(height: 8.0),
          ],
          if (widget.enableDataExtraction && _extractedData.isNotEmpty) ...[
            Text(
              'Extracted Data:',
              style: TextStyle(
                color: widget.textColor,
                fontSize: widget.fontSize * 0.9,
                fontWeight: FontWeight.bold,
              ),
            ),
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color:
                    widget.isDarkTheme
                        ? Colors.grey.shade800
                        : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(widget.borderRadius),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children:
                    _extractedData.entries
                        .where((entry) => entry.key != 'items')
                        .map(
                          (entry) => Padding(
                            padding: const EdgeInsets.only(bottom: 4.0),
                            child: Row(
                              children: [
                                Text(
                                  '${entry.key.replaceAll('_', ' ').capitalize()}: ',
                                  style: TextStyle(
                                    color: widget.textColor,
                                    fontSize: widget.fontSize * 0.9,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  '${entry.value}',
                                  style: TextStyle(
                                    color: widget.textColor,
                                    fontSize: widget.fontSize * 0.9,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        .toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 16.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 12.0; // Small (768-1024px)
    } else {
      return 14.0; // Default for very small screens
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Base height for input field
    double baseHeight;
    if (screenWidth > 1920) {
      baseHeight = 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      baseHeight = 52.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      baseHeight = 48.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      baseHeight = 44.0; // Small (768-1024px)
    } else {
      baseHeight = 44.0; // Default for very small screens
    }

    // Add extra height if there's an error message to prevent overlap
    if (_errorText != null && _errorText!.isNotEmpty) {
      baseHeight += 24.0; // Add space for error text
    }

    // Add extra height if there's helper text
    if (widget.helperText != null && widget.helperText!.isNotEmpty) {
      baseHeight += 20.0; // Add space for helper text
    }

    return baseHeight;
  }

  @override
  Widget build(BuildContext context) {
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;

    // Create the file picker button
    final filePickerButton = ElevatedButton.icon(
      onPressed: widget.isDisabled || widget.isReadOnly ? null : _pickFiles,
      icon:
          widget.showIcon && widget.icon != null
              ? Icon(widget.icon)
              : const Icon(Icons.upload_file),
      label: Text(widget.buttonText),
      style: ElevatedButton.styleFrom(
        foregroundColor: Colors.white,
        backgroundColor:
            widget.isDarkTheme ? Colors.blue.shade700 : Color(0xFF0058FF),
        disabledForegroundColor: Colors.grey.shade400,
        disabledBackgroundColor: Colors.grey.shade200,
        textStyle: TextStyle(
          //fontSize: widget.fontSize,
          fontSize: _getResponsiveFontSize(context),
          fontWeight: widget.fontWeight,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
      ),
    );

    // Create the process button if needed
    final processButton =
        !widget.autoProcess && _selectedFiles.isNotEmpty
            ? ElevatedButton.icon(
              onPressed:
                  _isProcessing ||
                          !_isValid ||
                          widget.isDisabled ||
                          widget.isReadOnly
                      ? null
                      : _processFiles,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Process'),
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor:
                    widget.isDarkTheme ? Colors.green.shade700 : Colors.green,
                disabledForegroundColor: Colors.grey.shade400,
                disabledBackgroundColor: Colors.grey.shade200,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
              ),
            )
            : null;

    // Create the clear button if needed
    final clearButton =
        _selectedFiles.isNotEmpty && widget.showClearButton
            ? OutlinedButton.icon(
              onPressed:
                  widget.isDisabled || widget.isReadOnly ? null : _clearFiles,
              icon: const Icon(Icons.clear),
              label: const Text('Clear'),
              style: OutlinedButton.styleFrom(
                foregroundColor:
                    widget.isDarkTheme ? Colors.red.shade300 : Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
              ),
            )
            : null;

    // Create the main content
    final content = Container(
      padding: widget.padding,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.hasBorder
                ? Border.all(
                  color: _errorText != null ? Colors.red : Color(0xFFCCCCCC),
                  width: widget.borderWidth,
                )
                : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label removed as per user request
          if (widget.hint != null && _selectedFiles.isEmpty) ...[
            Text(
              widget.hint!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize,
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 8.0),
          ],
          Row(
            mainAxisAlignment:
                MainAxisAlignment.center, // Center buttons horizontally
            crossAxisAlignment:
                CrossAxisAlignment.center, // Center buttons vertically
            children: [
              filePickerButton,
              if (processButton != null) ...[
                const SizedBox(width: 8.0),
                processButton,
              ],
              if (clearButton != null) ...[
                const SizedBox(width: 8.0),
                clearButton,
              ],
            ],
          ),
          _buildSelectedFiles(),
          if (widget.showProcessingIndicator) _buildProcessingIndicator(),
          _buildResults(),
          if (_errorText != null) ...[
            const SizedBox(height: 8.0),
            Text(
              _errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize * 0.9,
              ),
            ),
          ],
          if (widget.helperText != null && _errorText == null) ...[
            const SizedBox(height: 8.0),
            Text(
              widget.helperText!,
              style: TextStyle(
                color: effectiveTextColor.withAlpha(179),
                fontSize: widget.fontSize * 0.8,
              ),
            ),
          ],
        ],
      ),
    );

    // Apply drag and drop if enabled
    final dragDropContent =
        widget.allowDragDrop ? _buildDragDropArea(content) : content;

    // Apply shadow if needed
    final shadowWidget =
        widget.hasShadow
            ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: dragDropContent,
            )
            : dragDropContent;

    // Create the final widget with the specified size
    final sizedWidget = Container(
      width: widget.width,
      //height: widget.height > 0 ? widget.height : null,
      height: _getResponsiveHeight(context),
      margin: widget.margin,
      child: shadowWidget,
    );

    // Add hover detection
    final hoverWidget = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: sizedWidget,
    );

    // Add gesture detection for tap, double tap, and long press
    return GestureDetector(
      onTap:
          widget.isDisabled || widget.isReadOnly
              ? null
              : () {
                if (widget.onTap != null) {
                  widget.onTap!();
                }
                _executeJsonCallback('onTap');

                // Request focus when tapped
                if (!_focusNode.hasFocus) {
                  _focusNode.requestFocus();
                }
              },
      onDoubleTap:
          widget.isDisabled || widget.isReadOnly
              ? null
              : () {
                if (widget.onDoubleTap != null) {
                  widget.onDoubleTap!();
                }
                _executeJsonCallback('onDoubleTap');
              },
      onLongPress:
          widget.isDisabled || widget.isReadOnly
              ? null
              : () {
                if (widget.onLongPress != null) {
                  widget.onLongPress!();
                }
                _executeJsonCallback('onLongPress');
              },
      child: Focus(focusNode: _focusNode, child: hoverWidget),
    );
  }

  /// Builds a drag and drop area around the given child widget
  Widget _buildDragDropArea(Widget child) {
    return DragTarget<List<PlatformFile>>(
      onWillAcceptWithDetails: (details) {
        setState(() {
          _isDragging = true;
        });
        return true;
      },
      onAcceptWithDetails: (details) {
        final data = details.data;
        setState(() {
          _isDragging = false;
          _selectedFiles = data;
        });
        _validateFiles();

        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(_selectedFiles);
        }

        if (widget.autoProcess && _isValid) {
          _processFiles();
        }
      },
      onLeave: (data) {
        setState(() {
          _isDragging = false;
        });
      },
      builder: (context, candidateData, rejectedData) {
        return Container(
          decoration: BoxDecoration(
            color:
                _isDragging
                    ? (widget.isDarkTheme
                        ? Colors.blue.shade900.withAlpha(77)
                        : Colors.blue.shade100.withAlpha(77))
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border:
                _isDragging
                    ? Border.all(
                      color:
                          widget.isDarkTheme
                              ? Colors.blue.shade300
                              : Colors.blue,
                      width: 2,
                      style: BorderStyle.solid,
                    )
                    : null,
          ),
          child: child,
        );
      },
    );
  }
}
