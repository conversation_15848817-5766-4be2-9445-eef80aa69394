import 'package:flutter/material.dart';
import 'dart:typed_data';

/// Mobile stub implementation of WebAudioRecorderWidget
/// This provides a fallback for mobile platforms where web audio recording is not available
class WebAudioRecorderWidgetImpl extends StatefulWidget {
  /// Callback when recording is complete
  final Function(Uint8List audioData, String fileName)? onRecordingComplete;

  /// Text editing controller to update with transcribed text
  final TextEditingController? chatController;

  /// Callback when recording is cancelled
  final VoidCallback? onCancel;

  /// Callback when loading state changes
  final Function(bool isLoading)? onLoadingChanged;

  const WebAudioRecorderWidgetImpl({
    super.key,
    this.onRecordingComplete,
    this.chatController,
    this.onCancel,
    this.onLoadingChanged,
  });

  @override
  State<WebAudioRecorderWidgetImpl> createState() => _WebAudioRecorderWidgetImplState();
}

class _WebAudioRecorderWidgetImplState extends State<WebAudioRecorderWidgetImpl> {
  @override
  void initState() {
    super.initState();
    // Immediately notify that this feature is not available on mobile
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Web audio recording is not available on mobile platforms'),
            duration: Duration(seconds: 3),
          ),
        );
        
        // Notify parent about cancellation since this feature is not available
        if (widget.onCancel != null) {
          widget.onCancel!();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Return a simple message indicating this feature is not available on mobile
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.mic_off,
            size: 48,
            color: Colors.grey.shade600,
          ),
          const SizedBox(height: 8),
          Text(
            'Web Audio Recording',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Not available on mobile platforms',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              if (widget.onCancel != null) {
                widget.onCancel!();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey.shade300,
              foregroundColor: Colors.grey.shade700,
            ),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
