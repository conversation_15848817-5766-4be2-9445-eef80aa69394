import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// A widget for inputting and displaying integer values.
///
/// This widget provides a text field for entering integer values with
/// optional features like increment/decrement buttons, validation,
/// formatting, and styling options.
class IntegerWidget extends StatefulWidget {
  /// The initial value of the integer input.
  final int? initialValue;

  /// The minimum allowed value.
  final int? minValue;

  /// The maximum allowed value.
  final int? maxValue;

  /// The step value for increment and decrement.
  final int stepValue;

  /// Whether to show increment and decrement buttons.
  final bool showButtons;

  /// The size of the increment and decrement buttons.
  final double buttonSize;

  /// The icon for the increment button.
  final IconData incrementIcon;

  /// The icon for the decrement button.
  final IconData decrementIcon;

  /// The color of the buttons.
  final Color? buttonColor;

  /// The color of the button icons.
  final Color? buttonIconColor;

  /// The width of the text field.
  final double? width;

  /// The height of the text field.
  final double? height;

  /// The text style for the input field.
  final TextStyle? textStyle;

  /// The decoration for the input field.
  final InputDecoration? decoration;

  /// The prefix text to display before the input field.
  final String? prefix;

  /// The suffix text to display after the input field.
  final String? suffix;

  /// The label text to display above the input field.
  final String? label;

  /// The style for the label text.
  final TextStyle? labelStyle;

  /// The hint text to display when the input field is empty.
  final String? hint;

  /// The error text to display when the input is invalid.
  final String? errorText;

  /// Whether the input field is enabled.
  final bool enabled;

  /// Whether the input field is read-only.
  final bool readOnly;

  /// The text alignment within the input field.
  final TextAlign textAlign;

  /// The border radius of the input field.
  final double borderRadius;

  /// The border color of the input field.
  final Color? borderColor;

  /// The border width of the input field.
  final double borderWidth;

  /// The focus border color of the input field.
  final Color? focusBorderColor;

  /// The focus border width of the input field.
  final double focusBorderWidth;

  /// The error border color of the input field.
  final Color? errorBorderColor;

  /// The error border width of the input field.
  final double errorBorderWidth;

  /// The background color of the input field.
  final Color? backgroundColor;

  /// The padding around the input field.
  final EdgeInsetsGeometry padding;

  /// The margin around the widget.
  final EdgeInsetsGeometry margin;

  /// The layout direction of the widget (horizontal or vertical).
  final Axis direction;

  /// Whether to show a thousands separator.
  final bool useThousandsSeparator;

  /// The character to use as a thousands separator.
  final String thousandsSeparator;

  /// Whether to allow negative values.
  final bool allowNegative;

  /// Whether to show a clear button.
  final bool showClearButton;

  /// The callback that is called when the value changes.
  final ValueChanged<int?>? onChanged;

  /// The callback that is called when the user submits the value.
  final ValueChanged<int?>? onSubmitted;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Animation properties
  /// Whether to animate the widget when it changes
  final bool hasAnimation;

  /// Duration of the animation
  final Duration animationDuration;

  /// Curve to use for the animation
  final Curve animationCurve;

  // Input field-specific properties
  /// Whether to auto-validate the input field
  final bool autoValidate;

  /// The validator function for the input field
  final String? Function(String?)? validator;

  /// The helper text to display below the input field
  final String? helperText;

  /// The color of the cursor in the input field
  final Color? cursorColor;

  /// The radius of the cursor in the input field
  final double? cursorRadius;

  /// The width of the cursor in the input field
  final double? cursorWidth;

  /// The height of the cursor in the input field
  final double? cursorHeight;

  // Button-specific properties
  /// The shape of the increment/decrement buttons
  final OutlinedBorder? buttonShape;

  /// The elevation of the buttons
  final double buttonElevation;

  /// The padding of the buttons
  final EdgeInsetsGeometry buttonPadding;

  /// Whether the buttons should have a splash effect
  final bool buttonSplash;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON validation
  final bool useJsonValidation;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Integer-specific JSON configuration
  /// Whether to use JSON integer configuration
  final bool useJsonIntegerConfig;

  /// Integer-specific JSON configuration
  final Map<String, dynamic>? integerConfig;

  /// Creates an integer widget.
  const IntegerWidget({
    super.key,
    this.initialValue,
    this.minValue,
    this.maxValue,
    this.stepValue = 1,
    this.showButtons = true,
    this.buttonSize = 36.0,
    this.incrementIcon = Icons.add,
    this.decrementIcon = Icons.remove,
    this.buttonColor,
    this.buttonIconColor,
    this.width,
    this.height,
    this.textStyle,
    this.decoration,
    this.prefix,
    this.suffix,
    this.label,
    this.labelStyle,
    this.hint,
    this.errorText,
    this.enabled = true,
    this.readOnly = false,
    this.textAlign = TextAlign.center,
    this.borderRadius = 4.0,
    this.borderColor,
    this.borderWidth = 1,
    this.focusBorderColor,
    this.focusBorderWidth = 1.0,
    this.errorBorderColor,
    this.errorBorderWidth = 1.0,
    this.backgroundColor = Colors.white,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = EdgeInsets.zero,
    this.direction = Axis.horizontal,
    this.useThousandsSeparator = false,
    this.thousandsSeparator = ',',
    this.allowNegative = true,
    this.showClearButton = false,
    this.onChanged,
    this.onSubmitted,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Animation properties
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    // Input field-specific properties
    this.autoValidate = false,
    this.validator,
    this.helperText,
    this.cursorColor,
    this.cursorRadius,
    this.cursorWidth,
    this.cursorHeight,
    // Button-specific properties
    this.buttonShape,
    this.buttonElevation = 0.0,
    this.buttonPadding = EdgeInsets.zero,
    this.buttonSplash = true,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Integer-specific JSON configuration
    this.useJsonIntegerConfig = false,
    this.integerConfig,
  });

  /// Creates an IntegerWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the IntegerWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialValue": 50,
  ///   "minValue": 0,
  ///   "maxValue": 100,
  ///   "label": "Quantity"
  /// }
  /// ```
  factory IntegerWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return const Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return EdgeInsets.zero;
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return EdgeInsets.zero;
    }

    // Parse duration
    Duration parseDuration(dynamic durationValue) {
      if (durationValue == null) {
        return const Duration(milliseconds: 300);
      }

      if (durationValue is int) {
        return Duration(milliseconds: durationValue);
      } else if (durationValue is Map<String, dynamic>) {
        final int milliseconds =
            (durationValue['milliseconds'] as num?)?.toInt() ?? 0;
        final int seconds = (durationValue['seconds'] as num?)?.toInt() ?? 0;
        final int minutes = (durationValue['minutes'] as num?)?.toInt() ?? 0;

        return Duration(
          milliseconds: milliseconds,
          seconds: seconds,
          minutes: minutes,
        );
      } else if (durationValue is String) {
        // Parse strings like "300ms", "2s", "1m"
        final RegExp durationRegExp = RegExp(r'(\d+)(ms|s|m)');
        final match = durationRegExp.firstMatch(durationValue);

        if (match != null) {
          final int value = int.parse(match.group(1)!);
          final String unit = match.group(2)!;

          switch (unit) {
            case 'ms':
              return Duration(milliseconds: value);
            case 's':
              return Duration(seconds: value);
            case 'm':
              return Duration(minutes: value);
            default:
              return const Duration(milliseconds: 300);
          }
        }
      }

      return const Duration(milliseconds: 300);
    }

    // Parse curve
    Curve parseCurve(dynamic curveValue) {
      if (curveValue == null) return Curves.easeInOut;

      if (curveValue is String) {
        switch (curveValue.toLowerCase()) {
          case 'linear':
            return Curves.linear;
          case 'decelerate':
            return Curves.decelerate;
          case 'ease':
            return Curves.ease;
          case 'easein':
          case 'ease_in':
            return Curves.easeIn;
          case 'easeout':
          case 'ease_out':
            return Curves.easeOut;
          case 'easeinout':
          case 'ease_in_out':
            return Curves.easeInOut;
          case 'elasticin':
          case 'elastic_in':
            return Curves.elasticIn;
          case 'elasticout':
          case 'elastic_out':
            return Curves.elasticOut;
          case 'elasticinout':
          case 'elastic_in_out':
            return Curves.elasticInOut;
          case 'bouncein':
          case 'bounce_in':
            return Curves.bounceIn;
          case 'bounceout':
          case 'bounce_out':
            return Curves.bounceOut;
          case 'bounceinout':
          case 'bounce_in_out':
            return Curves.bounceInOut;
          default:
            return Curves.easeInOut;
        }
      }

      return Curves.easeInOut;
    }

    // Parse text style
    TextStyle? parseTextStyle(dynamic styleValue) {
      if (styleValue == null) return null;

      if (styleValue is Map<String, dynamic>) {
        final color = parseColor(styleValue['color']);
        final fontSize =
            styleValue['fontSize'] != null
                ? (styleValue['fontSize'] as num).toDouble()
                : null;
        final fontWeight =
            styleValue['fontWeight'] != null
                ? (styleValue['fontWeight'] == 'bold'
                    ? FontWeight.bold
                    : styleValue['fontWeight'] == 'normal'
                    ? FontWeight.normal
                    : FontWeight.normal)
                : null;
        final fontStyle =
            styleValue['fontStyle'] != null
                ? (styleValue['fontStyle'] == 'italic'
                    ? FontStyle.italic
                    : FontStyle.normal)
                : null;
        final letterSpacing =
            styleValue['letterSpacing'] != null
                ? (styleValue['letterSpacing'] as num).toDouble()
                : null;
        final wordSpacing =
            styleValue['wordSpacing'] != null
                ? (styleValue['wordSpacing'] as num).toDouble()
                : null;
        final height =
            styleValue['height'] != null
                ? (styleValue['height'] as num).toDouble()
                : null;
        final decoration =
            styleValue['decoration'] != null
                ? (styleValue['decoration'] == 'underline'
                    ? TextDecoration.underline
                    : styleValue['decoration'] == 'lineThrough'
                    ? TextDecoration.lineThrough
                    : styleValue['decoration'] == 'overline'
                    ? TextDecoration.overline
                    : null)
                : null;
        final fontFamily = styleValue['fontFamily'] as String?;

        return TextStyle(
          color: color,
          //fontSize: fontSize,
          fontWeight: fontWeight,
          fontStyle: fontStyle,
          letterSpacing: letterSpacing,
          wordSpacing: wordSpacing,
          height: height,
          decoration: decoration,
          fontFamily: fontFamily,
        );
      }

      return null;
    }

    // Parse input decoration
    InputDecoration? parseInputDecoration(dynamic decorationValue) {
      if (decorationValue == null) return null;

      if (decorationValue is Map<String, dynamic>) {
        final labelText = decorationValue['labelText'] as String?;
        final hintText = decorationValue['hintText'] as String?;
        final helperText = decorationValue['helperText'] as String?;
        final errorText = decorationValue['errorText'] as String?;
        final prefixText = decorationValue['prefixText'] as String?;
        final suffixText = decorationValue['suffixText'] as String?;
        final labelStyle = parseTextStyle(decorationValue['labelStyle']);
        final hintStyle = parseTextStyle(decorationValue['hintStyle']);
        final helperStyle = parseTextStyle(decorationValue['helperStyle']);
        final errorStyle = parseTextStyle(decorationValue['errorStyle']);
        final prefixStyle = parseTextStyle(decorationValue['prefixStyle']);
        final suffixStyle = parseTextStyle(decorationValue['suffixStyle']);
        final fillColor = parseColor(decorationValue['fillColor']);
        final filled = decorationValue['filled'] as bool?;
        final isDense = decorationValue['isDense'] as bool?;
        final contentPadding = parseEdgeInsets(
          decorationValue['contentPadding'],
        );

        // Parse border
        InputBorder? parseBorder(dynamic borderValue) {
          if (borderValue == null) return null;

          if (borderValue is Map<String, dynamic>) {
            final borderType = borderValue['type'] as String?;
            final borderRadius =
                borderValue['radius'] != null
                    ? (borderValue['radius'] as num).toDouble()
                    : 4.0;
            final borderColor = parseColor(borderValue['color']) ?? Colors.grey;
            final borderWidth =
                borderValue['width'] != null
                    ? (borderValue['width'] as num).toDouble()
                    : 1.0;

            switch (borderType?.toLowerCase()) {
              case 'outline':
                return OutlineInputBorder(
                  borderRadius: BorderRadius.circular(borderRadius),
                  borderSide: BorderSide(
                    //color: borderColor,
                    color: Colors.red,
                    width: borderWidth,
                  ),
                );
              case 'underline':
                return UnderlineInputBorder(
                  borderSide: BorderSide(
                    color: borderColor,
                    width: borderWidth,
                  ),
                );
              case 'none':
                return InputBorder.none;
              default:
                return OutlineInputBorder(
                  borderRadius: BorderRadius.circular(borderRadius),
                  borderSide: BorderSide(
                    color: borderColor,
                    width: borderWidth,
                  ),
                );
            }
          }

          return null;
        }

        final border = parseBorder(decorationValue['border']);
        final enabledBorder = parseBorder(decorationValue['enabledBorder']);
        final focusedBorder = parseBorder(decorationValue['focusedBorder']);
        final errorBorder = parseBorder(decorationValue['errorBorder']);
        final focusedErrorBorder = parseBorder(
          decorationValue['focusedErrorBorder'],
        );
        final disabledBorder = parseBorder(decorationValue['disabledBorder']);

        return InputDecoration(
          labelText: labelText,
          hintText: hintText,
          helperText: helperText,
          errorText: errorText,
          prefixText: prefixText,
          suffixText: suffixText,
          labelStyle: labelStyle,
          hintStyle: hintStyle,
          helperStyle: helperStyle,
          errorStyle: errorStyle,
          prefixStyle: prefixStyle,
          suffixStyle: suffixStyle,
          fillColor: fillColor,
          filled: filled,
          isDense: isDense,
          contentPadding: contentPadding as EdgeInsetsGeometry?,
          border: border,
          enabledBorder: enabledBorder,
          focusedBorder: focusedBorder,
          errorBorder: errorBorder,
          focusedErrorBorder: focusedErrorBorder,
          disabledBorder: disabledBorder,
        );
      }

      return null;
    }

    // Parse shape border
    OutlinedBorder? parseShapeBorder(dynamic shapeValue) {
      if (shapeValue == null) return null;

      if (shapeValue is Map<String, dynamic>) {
        final shapeType = shapeValue['type'] as String?;
        final borderRadius =
            shapeValue['radius'] != null
                ? (shapeValue['radius'] as num).toDouble()
                : 4.0;

        switch (shapeType?.toLowerCase()) {
          case 'rounded':
            return RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            );
          case 'circle':
            return const CircleBorder();
          case 'stadium':
            return const StadiumBorder();
          case 'beveled':
            return BeveledRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            );
          default:
            return RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius),
            );
        }
      }

      return null;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onChanged'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onChanged'] = json['onChanged'];
      useJsonCallbacks = true;
    }

    if (json['onSubmitted'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onSubmitted'] = json['onSubmitted'];
      useJsonCallbacks = true;
    }

    // Parse Integer-specific configuration
    Map<String, dynamic>? integerConfig;
    bool useJsonIntegerConfig = json['useJsonIntegerConfig'] as bool? ?? false;

    if (json['integerConfig'] != null) {
      if (json['integerConfig'] is Map) {
        integerConfig = Map<String, dynamic>.from(json['integerConfig'] as Map);
        useJsonIntegerConfig = true;
      } else if (json['integerConfig'] is String) {
        try {
          integerConfig =
              jsonDecode(json['integerConfig'] as String)
                  as Map<String, dynamic>;
          useJsonIntegerConfig = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON
    return IntegerWidget(
      // Basic properties
      initialValue: json['initialValue'] as int?,
      minValue: json['minValue'] as int?,
      maxValue: json['maxValue'] as int?,
      stepValue: json['stepValue'] as int? ?? 1,
      showButtons: json['showButtons'] as bool? ?? true,
      buttonSize:
          json['buttonSize'] != null
              ? (json['buttonSize'] as num).toDouble()
              : 36.0,
      incrementIcon:
          json['incrementIcon'] == 'add'
              ? Icons.add
              : json['incrementIcon'] == 'arrow_upward'
              ? Icons.arrow_upward
              : json['incrementIcon'] == 'expand_less'
              ? Icons.expand_less
              : Icons.add,
      decrementIcon:
          json['decrementIcon'] == 'remove'
              ? Icons.remove
              : json['decrementIcon'] == 'arrow_downward'
              ? Icons.arrow_downward
              : json['decrementIcon'] == 'expand_more'
              ? Icons.expand_more
              : Icons.remove,
      buttonColor: parseColor(json['buttonColor']),
      buttonIconColor: parseColor(json['buttonIconColor']),
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      textStyle: parseTextStyle(json['textStyle']),
      decoration: parseInputDecoration(json['decoration']),
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      label: json['label'] as String?,
      labelStyle: parseTextStyle(json['labelStyle']),
      hint: json['hint'] as String?,
      errorText: json['errorText'] as String?,
      enabled: json['enabled'] as bool? ?? true,
      readOnly: json['readOnly'] as bool? ?? false,
      textAlign:
          json['textAlign'] == 'left'
              ? TextAlign.left
              : json['textAlign'] == 'right'
              ? TextAlign.right
              : TextAlign.center,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      borderColor: parseColor(json['borderColor']),
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.5,
      focusBorderColor: parseColor(json['focusBorderColor']),
      focusBorderWidth:
          json['focusBorderWidth'] != null
              ? (json['focusBorderWidth'] as num).toDouble()
              : 2.0,
      errorBorderColor: parseColor(json['errorBorderColor']),
      errorBorderWidth:
          json['errorBorderWidth'] != null
              ? (json['errorBorderWidth'] as num).toDouble()
              : 2.0,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      direction:
          json['direction'] == 'vertical' ? Axis.vertical : Axis.horizontal,
      useThousandsSeparator: json['useThousandsSeparator'] as bool? ?? false,
      thousandsSeparator: json['thousandsSeparator'] as String? ?? ',',
      allowNegative: json['allowNegative'] as bool? ?? true,
      showClearButton: json['showClearButton'] as bool? ?? false,

      // Advanced interaction properties
      onHover: null, // Cannot be created from JSON directly
      onFocus: null, // Cannot be created from JSON directly
      focusNode: null, // Cannot be created from JSON directly
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onDoubleTap: null, // Cannot be created from JSON directly
      onLongPress: null, // Cannot be created from JSON directly
      // Animation properties
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      animationDuration: parseDuration(json['animationDuration']),
      animationCurve: parseCurve(json['animationCurve']),

      // Input field-specific properties
      autoValidate: json['autoValidate'] as bool? ?? false,
      validator: null, // Cannot be created from JSON directly
      helperText: json['helperText'] as String?,
      cursorColor: parseColor(json['cursorColor']),
      cursorRadius:
          json['cursorRadius'] != null
              ? (json['cursorRadius'] as num).toDouble()
              : null,
      cursorWidth:
          json['cursorWidth'] != null
              ? (json['cursorWidth'] as num).toDouble()
              : null,
      cursorHeight:
          json['cursorHeight'] != null
              ? (json['cursorHeight'] as num).toDouble()
              : null,

      // Button-specific properties
      buttonShape: parseShapeBorder(json['buttonShape']),
      buttonElevation:
          json['buttonElevation'] != null
              ? (json['buttonElevation'] as num).toDouble()
              : 0.0,
      buttonPadding: parseEdgeInsets(json['buttonPadding']),
      buttonSplash: json['buttonSplash'] as bool? ?? true,

      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : null,
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,

      // Integer-specific JSON configuration
      useJsonIntegerConfig: useJsonIntegerConfig,
      integerConfig: integerConfig,
    );
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'initialValue': initialValue,
      'minValue': minValue,
      'maxValue': maxValue,
      'stepValue': stepValue,
      'showButtons': showButtons,
      'buttonSize': buttonSize,
      'width': width,
      'height': height,
      'prefix': prefix,
      'suffix': suffix,
      'label': label,
      'hint': hint,
      'errorText': errorText,
      'enabled': enabled,
      'readOnly': readOnly,
      'textAlign':
          textAlign == TextAlign.left
              ? 'left'
              : textAlign == TextAlign.right
              ? 'right'
              : 'center',
      'borderRadius': borderRadius,
      'borderWidth': borderWidth,
      'focusBorderWidth': focusBorderWidth,
      'errorBorderWidth': errorBorderWidth,
      'useThousandsSeparator': useThousandsSeparator,
      'thousandsSeparator': thousandsSeparator,
      'allowNegative': allowNegative,
      'showClearButton': showClearButton,

      // Colors
      'buttonColor':
          buttonColor != null ? '#${buttonColor!.toHexString()}' : null,
      'buttonIconColor':
          buttonIconColor != null ? '#${buttonIconColor!.toHexString()}' : null,
      'borderColor':
          borderColor != null ? '#${borderColor!.toHexString()}' : null,
      'focusBorderColor':
          focusBorderColor != null
              ? '#${focusBorderColor!.toHexString()}'
              : null,
      'errorBorderColor':
          errorBorderColor != null
              ? '#${errorBorderColor!.toHexString()}'
              : null,
      'backgroundColor':
          backgroundColor != null ? '#${backgroundColor!.toHexString()}' : null,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,
      'cursorColor':
          cursorColor != null ? '#${cursorColor!.toHexString()}' : null,

      // Advanced properties
      'direction': direction == Axis.vertical ? 'vertical' : 'horizontal',
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,

      // Animation properties
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,

      // Input field-specific properties
      'autoValidate': autoValidate,
      'helperText': helperText,
      'cursorWidth': cursorWidth,
      'cursorHeight': cursorHeight,
      'cursorRadius': cursorRadius,

      // Button-specific properties
      'buttonElevation': buttonElevation,
      'buttonSplash': buttonSplash,

      // JSON configuration
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonValidation': useJsonValidation,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonIntegerConfig': useJsonIntegerConfig,
    };
  }

  @override
  IntegerWidgetState createState() => IntegerWidgetState();
}

class IntegerWidgetState extends State<IntegerWidget>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  int? _currentValue;
  String? _errorText;
  bool _isValid = true;
  bool _isHovered = false; // Track hover state
  Map<String, dynamic>? _parsedJsonConfig;

  // Animation controller for animated effects
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initialValue;
    _controller = TextEditingController(text: _formatValue(_currentValue));

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );

    // Initialize with full opacity if not animating
    if (!widget.hasAnimation) {
      _animationController.value = 1.0;
    } else {
      _animationController.forward();
    }

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = widget.jsonConfig;
    }

    // Apply JSON validation if enabled
    if (widget.useJsonValidation) {
      _validateFromJson(_currentValue);
    }
  }

  @override
  void didUpdateWidget(IntegerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation duration and curve if changed
    if (oldWidget.animationDuration != widget.animationDuration) {
      _animationController.duration = widget.animationDuration;
    }

    if (oldWidget.animationCurve != widget.animationCurve) {
      _animation = CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      );
    }

    // Handle animation state changes
    if (!oldWidget.hasAnimation && widget.hasAnimation) {
      _animationController.forward(from: 0.0);
    } else if (oldWidget.hasAnimation && !widget.hasAnimation) {
      _animationController.value = 1.0;
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    // Update JSON configuration if changed
    if (widget.jsonConfig != null &&
        widget.jsonConfig != oldWidget.jsonConfig) {
      _parsedJsonConfig = widget.jsonConfig;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value ?? _currentValue,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  /// Validates a value using JSON validation rules
  bool _validateFromJson(int? value) {
    if (!widget.useJsonValidation ||
        widget.integerConfig == null ||
        value == null) {
      return true;
    }

    final config = widget.integerConfig!;

    // Check min/max constraints from JSON
    if (config.containsKey('min') && value < (config['min'] as num)) {
      _errorText =
          config['minErrorText'] as String? ??
          'Value must be at least ${config['min']}';
      _isValid = false;
      return false;
    }

    if (config.containsKey('max') && value > (config['max'] as num)) {
      _errorText =
          config['maxErrorText'] as String? ??
          'Value must be at most ${config['max']}';
      _isValid = false;
      return false;
    }

    // Check step constraints from JSON
    if (config.containsKey('step')) {
      final step = (config['step'] as num).toInt();
      final base =
          config.containsKey('stepBase')
              ? (config['stepBase'] as num).toInt()
              : 0;

      if ((value - base) % step != 0) {
        _errorText =
            config['stepErrorText'] as String? ??
            'Value must be a multiple of $step from $base';
        _isValid = false;
        return false;
      }
    }

    // Check custom validation function from JSON
    if (config.containsKey('customValidation')) {
      final validationRules =
          config['customValidation'] as Map<String, dynamic>?;
      if (validationRules != null) {
        // Example: Check if value is even
        if (validationRules.containsKey('isEven') &&
            validationRules['isEven'] as bool) {
          if (value % 2 != 0) {
            _errorText =
                validationRules['errorText'] as String? ?? 'Value must be even';
            _isValid = false;
            return false;
          }
        }

        // Example: Check if value is odd
        if (validationRules.containsKey('isOdd') &&
            validationRules['isOdd'] as bool) {
          if (value % 2 == 0) {
            _errorText =
                validationRules['errorText'] as String? ?? 'Value must be odd';
            _isValid = false;
            return false;
          }
        }

        // Example: Check if value is prime
        if (validationRules.containsKey('isPrime') &&
            validationRules['isPrime'] as bool) {
          if (!_isPrime(value)) {
            _errorText =
                validationRules['errorText'] as String? ??
                'Value must be prime';
            _isValid = false;
            return false;
          }
        }
      }
    }

    _errorText = null;
    _isValid = true;
    return true;
  }

  /// Checks if a number is prime
  bool _isPrime(int n) {
    if (n <= 1) return false;
    if (n <= 3) return true;
    if (n % 2 == 0 || n % 3 == 0) return false;

    int i = 5;
    while (i * i <= n) {
      if (n % i == 0 || n % (i + 2) == 0) return false;
      i += 6;
    }

    return true;
  }

  String _formatValue(int? value) {
    if (value == null) return '';

    if (widget.useThousandsSeparator) {
      // Format with thousands separator
      final String valueStr = value.toString();
      final StringBuffer result = StringBuffer();

      if (value < 0) {
        result.write('-');
      }

      final String digits = valueStr.replaceAll(RegExp(r'[^0-9]'), '');
      final int length = digits.length;

      for (int i = 0; i < length; i++) {
        if (i > 0 && (length - i) % 3 == 0) {
          result.write(widget.thousandsSeparator);
        }
        result.write(digits[i]);
      }

      return result.toString();
    } else {
      // No formatting
      return value.toString();
    }
  }

  int? _parseValue(String text) {
    if (text.isEmpty) return null;

    // Remove thousands separators if used
    if (widget.useThousandsSeparator) {
      text = text.replaceAll(widget.thousandsSeparator, '');
    }

    return int.tryParse(text);
  }

  void _validateAndUpdateValue(int? value) {
    if (value == null) {
      _errorText = null;
      _currentValue = null;

      // Execute JSON callback if defined
      if (widget.useJsonCallbacks) {
        _executeJsonCallback('onChanged', null);
      }

      widget.onChanged?.call(null);
      return;
    }

    // Execute before change callback if defined
    if (widget.useJsonCallbacks) {
      _executeJsonCallback('onBeforeChange', value);
    }

    // Store old value for animation
    final oldValue = _currentValue;

    // Check min/max constraints
    if (widget.minValue != null && value < widget.minValue!) {
      _errorText = 'Value must be at least ${widget.minValue}';
      _currentValue = widget.minValue;
    } else if (widget.maxValue != null && value > widget.maxValue!) {
      _errorText = 'Value must be at most ${widget.maxValue}';
      _currentValue = widget.maxValue;
    } else {
      _errorText = null;
      _currentValue = value;
    }

    // Apply JSON validation if enabled
    if (widget.useJsonValidation) {
      _validateFromJson(_currentValue);
    }

    // Update text field if needed
    final String formattedValue = _formatValue(_currentValue);
    if (_controller.text != formattedValue) {
      _controller.value = TextEditingValue(
        text: formattedValue,
        selection: TextSelection.collapsed(offset: formattedValue.length),
      );
    }

    // Animate if value changed and animation is enabled
    if (widget.hasAnimation && oldValue != _currentValue) {
      _animationController.reset();
      _animationController.forward();
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks) {
      _executeJsonCallback('onChanged', _currentValue);
    }

    // Call standard callback
    widget.onChanged?.call(_currentValue);

    // Update UI
    setState(() {});
  }

  void _incrementValue() {
    if (!widget.enabled || widget.readOnly) return;

    // Execute before increment callback if defined
    if (widget.useJsonCallbacks) {
      _executeJsonCallback('onBeforeIncrement');
    }

    final int newValue = (_currentValue ?? 0) + widget.stepValue;
    _validateAndUpdateValue(newValue);

    // Execute increment callback if defined
    if (widget.useJsonCallbacks) {
      _executeJsonCallback('onIncrement', _currentValue);
    }
  }

  void _decrementValue() {
    if (!widget.enabled || widget.readOnly) return;

    // Execute before decrement callback if defined
    if (widget.useJsonCallbacks) {
      _executeJsonCallback('onBeforeDecrement');
    }

    final int newValue = (_currentValue ?? 0) - widget.stepValue;

    // Check if negative values are allowed
    if (!widget.allowNegative && newValue < 0) {
      _validateAndUpdateValue(0);
    } else {
      _validateAndUpdateValue(newValue);
    }

    // Execute decrement callback if defined
    if (widget.useJsonCallbacks) {
      _executeJsonCallback('onDecrement', _currentValue);
    }
  }

  void _clearValue() {
    if (!widget.enabled || widget.readOnly) return;

    // Execute before clear callback if defined
    if (widget.useJsonCallbacks) {
      _executeJsonCallback('onBeforeClear');
    }

    setState(() {
      _currentValue = null;
      _controller.clear();
      _errorText = null;
      _isValid = true;
    });

    // Execute clear callback if defined
    if (widget.useJsonCallbacks) {
      _executeJsonCallback('onClear');
    }

    widget.onChanged?.call(null);
  }

  Widget _buildTextField() {
    // Determine background color based on hover state
    final Color effectiveBackgroundColor =
        _isHovered ? Colors.white : (widget.backgroundColor ?? Colors.white);

    // Create enhanced input decoration
    final InputDecoration effectiveDecoration =
        widget.decoration ??
        InputDecoration(
          //labelText: widget.label,
          //labelStyle: widget.labelStyle,
          hintText: widget.hint,
          errorText: widget.errorText ?? _errorText,
          prefixText: widget.prefix,
          suffixText: widget.suffix,
          filled: true,
          fillColor: effectiveBackgroundColor,
          helperText: widget.helperText,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color: widget.borderColor ?? Colors.pink,
              width: widget.borderWidth,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color:
                  _isHovered
                      ? const Color(0xFF0058FF)
                      : (widget.borderColor ?? const Color(0xFFCCCCCC)),
              width: widget.borderWidth,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              //color:widget.focusBorderColor ??widget.focusColor ??Theme.of(context).primaryColor,\
              color: Color(0xFF0058FF),
              width: widget.focusBorderWidth,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color:
                  widget.errorBorderColor ??
                  Theme.of(context).colorScheme.error,
              width: widget.errorBorderWidth,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color:
                  widget.errorBorderColor ??
                  Theme.of(context).colorScheme.error,
              width: widget.errorBorderWidth,
            ),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            borderSide: BorderSide(
              color: Colors.grey.shade200,
              width: widget.borderWidth * 0.7,
            ),
          ),
          contentPadding: widget.padding,
          suffixIcon:
              widget.showClearButton && _currentValue != null
                  ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: _clearValue,
                    iconSize: 18.0,
                    tooltip: 'Clear',
                    focusColor: widget.focusColor,
                    hoverColor: widget.hoverColor,
                    enableFeedback: widget.enableFeedback,
                  )
                  : null,
        );

    // Create the text field with responsive font size
    final responsiveFontSize = _getResponsiveInputFontSize(context);
    final effectiveTextStyle =
        widget.textStyle?.copyWith(
          fontSize: widget.textStyle?.fontSize ?? responsiveFontSize,
        ) ??
        TextStyle(fontSize: responsiveFontSize);

    Widget textField = TextField(
      controller: _controller,
      keyboardType: TextInputType.numberWithOptions(
        decimal: false,
        signed: widget.allowNegative,
      ),
      textAlign: widget.textAlign,
      style: effectiveTextStyle,
      decoration: effectiveDecoration,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      focusNode: widget.focusNode,
      cursorColor: widget.cursorColor,
      cursorWidth: widget.cursorWidth ?? 2.0,
      cursorHeight: widget.cursorHeight,
      cursorRadius:
          widget.cursorRadius != null
              ? Radius.circular(widget.cursorRadius!)
              : null,
      onTap: () {
        // Execute onTap callback if defined in JSON
        if (widget.useJsonCallbacks) {
          _executeJsonCallback('onTap');
        }
      },
      inputFormatters: [
        // Allow only digits, minus sign (if negative allowed), and thousands separator
        TextInputFormatter.withFunction((oldValue, newValue) {
          // Allow empty value
          if (newValue.text.isEmpty) return newValue;

          // Create a pattern that matches valid input
          String pattern = r'^';

          // Add optional minus sign if negative values are allowed
          if (widget.allowNegative) {
            pattern += r'-?';
          }

          // Add pattern for digits with optional thousands separators
          if (widget.useThousandsSeparator) {
            final escapedSeparator = RegExp.escape(widget.thousandsSeparator);
            pattern += r'\d{1,3}(?:' + escapedSeparator + r'\d{3})*';
          } else {
            pattern += r'\d+';
          }

          pattern += r'\$';

          if (RegExp(pattern).hasMatch(newValue.text)) {
            return newValue;
          }

          return oldValue;
        }),
      ],
      onChanged: (text) {
        final int? value = _parseValue(text);
        _validateAndUpdateValue(value);
      },
      onSubmitted: (text) {
        final int? value = _parseValue(text);
        _validateAndUpdateValue(value);

        // Execute onSubmitted callback if defined in JSON
        if (widget.useJsonCallbacks) {
          _executeJsonCallback('onSubmitted', _currentValue);
        }

        widget.onSubmitted?.call(_currentValue);
      },
    );

    // Apply validation if enabled
    if (widget.autoValidate && widget.validator != null) {
      textField = FormField<String>(
        initialValue: _controller.text,
        validator: widget.validator,
        autovalidateMode: AutovalidateMode.always,
        builder: (FormFieldState<String> state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              textField,
              if (state.hasError)
                Padding(
                  padding: const EdgeInsets.only(top: 4.0, left: 12.0),
                  child: Text(
                    state.errorText!,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                      fontSize: _getResponsiveFontSize(context) - 2.0,
                    ),
                  ),
                ),
            ],
          );
        },
      );
    }

    // Apply animation if needed
    if (widget.hasAnimation) {
      textField = FadeTransition(opacity: _animation, child: textField);
    }

    // Apply advanced interaction properties - Always wrap with MouseRegion for hover tracking
    textField = MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
        if (widget.onHover != null) {
          widget.onHover!(true);
        }
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
        if (widget.onHover != null) {
          widget.onHover!(false);
        }
      },
      cursor:
          widget.enabled
              ? SystemMouseCursors.text
              : SystemMouseCursors.forbidden,
      child: GestureDetector(
        onDoubleTap:
            widget.onDoubleTap != null
                ? () {
                  // Execute onDoubleTap callback if defined in JSON
                  if (widget.useJsonCallbacks) {
                    _executeJsonCallback('onDoubleTap');
                  }

                  // Call standard callback
                  widget.onDoubleTap!();
                }
                : null,
        onLongPress:
            widget.onLongPress != null
                ? () {
                  // Execute onLongPress callback if defined in JSON
                  if (widget.useJsonCallbacks) {
                    _executeJsonCallback('onLongPress');
                  }

                  // Call standard callback
                  widget.onLongPress!();
                }
                : null,
        child: textField,
      ),
    );

    return SizedBox(
      width: widget.width,
      //height: widget.height,
      height: _getResponsiveHeight(context),
      child: textField,
    );
  }

  Widget _buildButton(IconData icon, VoidCallback onPressed) {
    // Create the button
    Widget button = SizedBox(
      width: widget.buttonSize,
      //height: widget.buttonSize,
      height: _getResponsiveHeight(context),
      child: Material(
        color: widget.buttonColor ?? Color(0xFF0058FF),

        borderRadius:
            widget.buttonShape != null
                ? null
                : BorderRadius.circular(widget.borderRadius),
        shape: widget.buttonShape,
        elevation: widget.buttonElevation,
        child: InkWell(
          onTap: widget.enabled && !widget.readOnly ? onPressed : null,
          borderRadius:
              widget.buttonShape != null
                  ? null
                  : BorderRadius.circular(widget.borderRadius),
          enableFeedback: widget.enableFeedback,
          hoverColor: widget.hoverColor,
          focusColor: widget.focusColor,
          splashFactory: widget.buttonSplash ? null : NoSplash.splashFactory,
          child: Center(
            child: Icon(
              icon,
              color: widget.buttonIconColor ?? Colors.white,
              size: widget.buttonSize * 0.5,
            ),
          ),
        ),
      ),
    );

    // Apply animation if needed
    if (widget.hasAnimation) {
      button = FadeTransition(opacity: _animation, child: button);
    }

    // Apply padding if specified
    if (widget.buttonPadding != EdgeInsets.zero) {
      button = Padding(padding: widget.buttonPadding, child: button);
    }

    return button;
  }

  Widget _buildButtons() {
    if (!widget.showButtons) return const SizedBox.shrink();

    final decrementButton = _buildButton(widget.decrementIcon, _decrementValue);
    final incrementButton = _buildButton(widget.incrementIcon, _incrementValue);

    if (widget.direction == Axis.horizontal) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          decrementButton,
          const SizedBox(width: 8.0),
          incrementButton,
        ],
      );
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          incrementButton,
          const SizedBox(height: 8.0),
          decrementButton,
        ],
      );
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 16.0; // Extra Large (>1920px) - Reduced for better fit
    } else if (screenWidth >= 1440) {
      return 15.0; // Large (1440-1920px) - Reduced for better fit
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium (1280-1366px) - Standard size
    } else if (screenWidth >= 768) {
      return 12.0; // Small (768-1024px) - Increased for readability
    } else {
      return 12.0; // Default for very small screens - Consistent
    }
  }

  double _getResponsiveInputFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 16.0; // Extra Large (>1920px) - Reduced for better fit
    } else if (screenWidth >= 1440) {
      return 15.0; // Large (1440-1920px) - Reduced for better fit
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium (1280-1366px) - Standard size
    } else if (screenWidth >= 768) {
      return 14.0; // Small (768-1024px) - Increased for readability
    } else {
      return 14.0; // Default for very small screens - Consistent
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Base height for input field
    double baseHeight;
    if (screenWidth > 1920) {
      baseHeight = 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      baseHeight = 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      baseHeight = 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      baseHeight = 32.0; // Small (768-1024px)
    } else {
      baseHeight = 32.0; // Default for very small screens
    }

    // Add extra height if there's an error message to prevent overlap
    if (_errorText != null && _errorText!.isNotEmpty) {
      baseHeight += 24.0; // Add space for error text
    }

    // Add extra height if there's helper text
    if (widget.helperText != null && widget.helperText!.isNotEmpty) {
      baseHeight += 20.0; // Add space for helper text
    }

    return baseHeight;
  }

  @override
  Widget build(BuildContext context) {
    // Create the main content based on direction
    Widget content;

    if (widget.direction == Axis.horizontal) {
      content = Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Minus button on the left
          if (widget.showButtons) ...[
            _buildButton(widget.decrementIcon, _decrementValue),
            const SizedBox(width: 8.0),
          ],
          // Text field in the middle
          Flexible(child: _buildTextField()),
          // Plus button on the right
          if (widget.showButtons) ...[
            const SizedBox(width: 8.0),
            _buildButton(widget.incrementIcon, _incrementValue),
          ],
        ],
      );
    } else {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (widget.label != null && widget.decoration == null)
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  widget.label!,
                  style:
                      widget.labelStyle ??
                      Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontSize: _getResponsiveFontSize(context),
                      ) ??
                      TextStyle(
                        fontSize: _getResponsiveFontSize(context),
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ),
            ),
          _buildTextField(),
          if (widget.showButtons) ...[
            const SizedBox(height: 8.0),
            _buildButtons(),
          ],
        ],
      );
    }

    // Apply validation if enabled
    if (widget.useJsonValidation && !_isValid) {
      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          content,
          Padding(
            padding: const EdgeInsets.only(top: 4.0, left: 12.0),
            child: Text(
              _errorText ?? 'Invalid value',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
                fontSize: _getResponsiveFontSize(context) - 2.0,
              ),
            ),
          ),
        ],
      );
    }

    // Apply JSON styling if enabled
    if (widget.useJsonStyling && widget.jsonConfig != null) {
      // This would be implemented to apply dynamic styling from JSON
      // Not fully implemented in this example
    }

    // Apply animation to the entire widget if needed
    if (widget.hasAnimation) {
      content = AnimatedOpacity(
        opacity: 1.0,
        duration: widget.animationDuration,
        curve: widget.animationCurve,
        child: content,
      );
    }

    // Apply margin
    return Padding(padding: widget.margin, child: content);
  }
}
