import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import '../utils/callback_interpreter.dart';
import 'utils/file_widget_json_parser.dart';
import 'components/file_widget_components.dart';

// Define BorderStyle extension for dashed border
extension BorderStyleExtension on BorderStyle {
  static const BorderStyle dashed = BorderStyle.solid;
}

// We're now using the actual file_picker package

/// A configurable file upload widget that handles file selection and display.
class FileWidget extends StatefulWidget {
  // Basic properties
  final bool isRequired;
  final bool allowMultiple;
  final List<String>? allowedExtensions;
  final FileType fileType;
  final int? maxFileSizeBytes;
  final int? maxFiles;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final String buttonText;

  // Icon properties
  final bool showIcon;
  final IconData? icon;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool showFileName;
  final bool showFileSize;
  final bool showFileType;
  final bool showClearButton;
  final bool showPreview;
  final bool uploadImmediately;
  final bool showProgressBar;
  final bool allowDragDrop;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callback
  final Function(List<PlatformFile>)? onFilesSelected;
  final Function()? onClear;
  final Function(List<PlatformFile>)? onUpload;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  // File-specific JSON configuration
  final bool useJsonFileHandling;
  final Map<String, dynamic>? fileHandlingConfig;

  const FileWidget({
    super.key,
    this.isRequired = false,
    this.allowMultiple = false,
    this.allowedExtensions,
    this.fileType = FileType.any,
    this.maxFileSizeBytes,
    this.maxFiles,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.buttonText = 'Choose File',
    this.showIcon = true,
    this.icon = Icons.upload_file,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.showFileName = true,
    this.showFileSize = true,
    this.showFileType = true,
    this.showClearButton = true,
    this.showPreview = false,
    this.uploadImmediately = false,
    this.showProgressBar = false,
    this.allowDragDrop = false,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.onFilesSelected,
    this.onClear,
    this.onUpload,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // File-specific JSON configuration
    this.useJsonFileHandling = false,
    this.fileHandlingConfig,
  });

  /// Creates a FileWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the FileWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "allowMultiple": true,
  ///   "allowedExtensions": ["jpg", "png", "pdf"],
  ///   "fileType": "custom",
  ///   "maxFileSizeBytes": 5242880,
  ///   "buttonText": "Upload Files",
  ///   "showPreview": true
  /// }
  /// ```
  factory FileWidget.fromJson(Map<String, dynamic> json) {
    // Parse JSON callbacks using utility
    final callbackData = FileWidgetJsonParser.parseJsonCallbacks(json);
    var jsonCallbacks = callbackData['jsonCallbacks'] as Map<String, dynamic>?;
    var useJsonCallbacks = callbackData['useJsonCallbacks'] as bool;

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    // Parse file-specific configuration
    Map<String, dynamic>? fileHandlingConfig;
    bool useJsonFileHandling = json['useJsonFileHandling'] as bool? ?? false;

    if (json['fileHandlingConfig'] != null) {
      if (json['fileHandlingConfig'] is Map) {
        fileHandlingConfig = Map<String, dynamic>.from(
          json['fileHandlingConfig'] as Map,
        );
        useJsonFileHandling = true;
      } else if (json['fileHandlingConfig'] is String) {
        try {
          fileHandlingConfig =
              jsonDecode(json['fileHandlingConfig'] as String)
                  as Map<String, dynamic>;
          useJsonFileHandling = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Create the widget with all properties from JSON using utility parsers
    return FileWidget(
      isRequired: FileWidgetJsonParser.parseBool(json['isRequired']),
      allowMultiple: FileWidgetJsonParser.parseBool(json['allowMultiple']),
      allowedExtensions: FileWidgetJsonParser.parseStringList(
        json['allowedExtensions'],
      ),
      fileType: FileWidgetJsonParser.parseFileType(json['fileType']),
      maxFileSizeBytes: FileWidgetJsonParser.parseInt(json['maxFileSizeBytes']),
      maxFiles: FileWidgetJsonParser.parseInt(json['maxFiles']),
      textColor:
          FileWidgetJsonParser.parseColor(json['textColor']) ?? Colors.black,
      backgroundColor:
          FileWidgetJsonParser.parseColor(json['backgroundColor']) ??
          Colors.white,
      borderColor:
          FileWidgetJsonParser.parseColor(json['borderColor']) ?? Colors.grey,
      borderWidth: FileWidgetJsonParser.parseDouble(
        json['borderWidth'],
        defaultValue: 1.0,
      ),
      borderRadius: FileWidgetJsonParser.parseDouble(
        json['borderRadius'],
        defaultValue: 4.0,
      ),
      hasBorder: FileWidgetJsonParser.parseBool(
        json['hasBorder'],
        defaultValue: true,
      ),
      fontSize: FileWidgetJsonParser.parseDouble(
        json['fontSize'],
        defaultValue: 16.0,
      ),
      fontWeight: FileWidgetJsonParser.parseFontWeight(json['fontWeight']),
      isCompact: FileWidgetJsonParser.parseBool(json['isCompact']),
      hasShadow: FileWidgetJsonParser.parseBool(json['hasShadow']),
      elevation: FileWidgetJsonParser.parseDouble(
        json['elevation'],
        defaultValue: 2.0,
      ),
      isDarkTheme: FileWidgetJsonParser.parseBool(json['isDarkTheme']),
      textAlign: FileWidgetJsonParser.parseTextAlign(json['textAlign']),
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      buttonText: json['buttonText'] as String? ?? 'Choose File',
      showIcon: FileWidgetJsonParser.parseBool(
        json['showIcon'],
        defaultValue: true,
      ),
      icon:
          FileWidgetJsonParser.parseIconData(json['icon']) ?? Icons.upload_file,
      isReadOnly: FileWidgetJsonParser.parseBool(json['isReadOnly']),
      isDisabled: FileWidgetJsonParser.parseBool(json['isDisabled']),
      showFileName: FileWidgetJsonParser.parseBool(
        json['showFileName'],
        defaultValue: true,
      ),
      showFileSize: FileWidgetJsonParser.parseBool(
        json['showFileSize'],
        defaultValue: true,
      ),
      showFileType: FileWidgetJsonParser.parseBool(
        json['showFileType'],
        defaultValue: true,
      ),
      showClearButton: FileWidgetJsonParser.parseBool(
        json['showClearButton'],
        defaultValue: true,
      ),
      showPreview: FileWidgetJsonParser.parseBool(json['showPreview']),
      uploadImmediately: FileWidgetJsonParser.parseBool(
        json['uploadImmediately'],
      ),
      showProgressBar: FileWidgetJsonParser.parseBool(json['showProgressBar']),
      allowDragDrop: FileWidgetJsonParser.parseBool(json['allowDragDrop']),
      width: FileWidgetJsonParser.parseDouble(
        json['width'],
        defaultValue: double.infinity,
      ),
      height: FileWidgetJsonParser.parseDouble(json['height']),
      padding: FileWidgetJsonParser.parseEdgeInsets(json['padding']),
      margin: FileWidgetJsonParser.parseEdgeInsets(json['margin']),
      // Advanced interaction properties
      hoverColor: FileWidgetJsonParser.parseColor(json['hoverColor']),
      focusColor: FileWidgetJsonParser.parseColor(json['focusColor']),
      enableFeedback: FileWidgetJsonParser.parseBool(
        json['enableFeedback'],
        defaultValue: true,
      ),
      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : {},
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      // File-specific JSON configuration
      useJsonFileHandling: useJsonFileHandling,
      fileHandlingConfig: fileHandlingConfig,
    );
  }

  /// Converts the widget configuration to a JSON map
  ///
  /// This method allows for serializing the widget's configuration to JSON,
  /// which can be useful for saving configurations or sending them to a server.
  Map<String, dynamic> toJson() {
    // Convert color to hex string
    String? colorToHex(Color? color) {
      if (color == null) return null;

      // Convert to hex format
      final r = color.red;
      final g = color.green;
      final b = color.blue;
      return '#${r.toRadixString(16).padLeft(2, '0')}${g.toRadixString(16).padLeft(2, '0')}${b.toRadixString(16).padLeft(2, '0')}';

      // Note: The above code uses deprecated APIs but is kept for compatibility.
      // A better approach would be to use the newer APIs like color.r, color.g, color.b
      // but that would require a minimum Flutter version that supports these APIs.
    }

    // Convert text alignment to string
    String textAlignToString(TextAlign align) {
      switch (align) {
        case TextAlign.center:
          return 'center';
        case TextAlign.end:
          return 'end';
        case TextAlign.left:
          return 'left';
        case TextAlign.right:
          return 'right';
        case TextAlign.justify:
          return 'justify';
        case TextAlign.start:
          return 'start';
      }
    }

    // Convert font weight to int
    int fontWeightToInt(FontWeight weight) {
      if (weight == FontWeight.w100) return 100;
      if (weight == FontWeight.w200) return 200;
      if (weight == FontWeight.w300) return 300;
      if (weight == FontWeight.w400 || weight == FontWeight.normal) return 400;
      if (weight == FontWeight.w500) return 500;
      if (weight == FontWeight.w600) return 600;
      if (weight == FontWeight.w700 || weight == FontWeight.bold) return 700;
      if (weight == FontWeight.w800) return 800;
      if (weight == FontWeight.w900) return 900;
      return 400;
    }

    // Convert icon data to string
    String? iconDataToString(IconData? icon) {
      if (icon == null) return null;

      if (icon == Icons.upload_file) return 'upload_file';
      if (icon == Icons.file_upload) return 'file_upload';
      if (icon == Icons.attach_file) return 'attach_file';
      if (icon == Icons.cloud_upload) return 'cloud_upload';
      if (icon == Icons.add) return 'add';
      if (icon == Icons.add_circle) return 'add_circle';
      if (icon == Icons.add_circle_outline) return 'add_circle_outline';
      if (icon == Icons.folder) return 'folder';
      if (icon == Icons.folder_open) return 'folder_open';
      if (icon == Icons.description) return 'description';
      if (icon == Icons.insert_drive_file) return 'insert_drive_file';
      if (icon == Icons.picture_as_pdf) return 'picture_as_pdf';
      if (icon == Icons.image) return 'image';
      if (icon == Icons.photo) return 'photo';
      if (icon == Icons.video_file) return 'video_file';
      if (icon == Icons.audio_file) return 'audio_file';
      if (icon == Icons.text_snippet) return 'text_snippet';
      if (icon == Icons.table_chart) return 'table_chart';
      if (icon == Icons.slideshow) return 'slideshow';
      if (icon == Icons.folder_zip) return 'folder_zip';

      return null;
    }

    // Convert edge insets to map
    Map<String, dynamic>? edgeInsetsToMap(EdgeInsetsGeometry insets) {
      if (insets is EdgeInsets) {
        if (insets.left == insets.top &&
            insets.left == insets.right &&
            insets.left == insets.bottom) {
          return {'all': insets.left};
        } else if (insets.left == insets.right && insets.top == insets.bottom) {
          return {'horizontal': insets.left, 'vertical': insets.top};
        } else {
          return {
            'left': insets.left,
            'top': insets.top,
            'right': insets.right,
            'bottom': insets.bottom,
          };
        }
      }
      return null;
    }

    // Convert file type to string
    String fileTypeToString(FileType fileType) {
      switch (fileType) {
        case FileType.any:
          return 'any';
        case FileType.image:
          return 'image';
        case FileType.video:
          return 'video';
        case FileType.audio:
          return 'audio';
        case FileType.media:
          return 'media';
        case FileType.custom:
          return 'custom';
      }
      return 'any'; // Default fallback
    }

    // Create the JSON map
    final Map<String, dynamic> json = {
      'isRequired': isRequired,
      'allowMultiple': allowMultiple,
      'allowedExtensions': allowedExtensions,
      'fileType': fileTypeToString(fileType),
      'maxFileSizeBytes': maxFileSizeBytes,
      'maxFiles': maxFiles,
      'textColor': colorToHex(textColor),
      'backgroundColor': colorToHex(backgroundColor),
      'borderColor': colorToHex(borderColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'fontSize': fontSize,
      'fontWeight': fontWeightToInt(fontWeight),
      'isCompact': isCompact,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isDarkTheme': isDarkTheme,
      'textAlign': textAlignToString(textAlign),
      'label': label,
      'hint': hint,
      'helperText': helperText,
      'errorText': errorText,
      'buttonText': buttonText,
      'showIcon': showIcon,
      'icon': iconDataToString(icon),
      'isReadOnly': isReadOnly,
      'isDisabled': isDisabled,
      'showFileName': showFileName,
      'showFileSize': showFileSize,
      'showFileType': showFileType,
      'showClearButton': showClearButton,
      'showPreview': showPreview,
      'uploadImmediately': uploadImmediately,
      'showProgressBar': showProgressBar,
      'allowDragDrop': allowDragDrop,
      'width': width == double.infinity ? 'infinity' : width,
      'height': height,
      'padding': edgeInsetsToMap(padding),
      'margin': edgeInsetsToMap(margin),
      'hoverColor': colorToHex(hoverColor),
      'focusColor': colorToHex(focusColor),
      'enableFeedback': enableFeedback,
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonValidation': useJsonValidation,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonFileHandling': useJsonFileHandling,
    };

    // Add callbacks if they exist
    if (jsonCallbacks != null && jsonCallbacks!.isNotEmpty) {
      json['callbacks'] = jsonCallbacks;
    }

    // Add file handling config if it exists
    if (fileHandlingConfig != null && fileHandlingConfig!.isNotEmpty) {
      json['fileHandlingConfig'] = fileHandlingConfig;
    }

    return json;
  }

  @override
  State<FileWidget> createState() => _FileWidgetState();
}

class _FileWidgetState extends State<FileWidget> {
  List<PlatformFile> _selectedFiles = [];
  String? _errorText;
  bool _isValid = true;
  bool _isDragging = false;
  double _uploadProgress = 0.0;
  bool _isUploading = false;

  // Map to store dynamic state for callbacks
  Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;

  // Map to store file handling configuration from JSON
  Map<String, dynamic>? _fileHandlingConfig;

  @override
  void initState() {
    super.initState();

    // Initialize error text
    _errorText = widget.errorText;

    // Initialize callback state
    _callbackState =
        widget.callbackState != null
            ? Map<String, dynamic>.from(widget.callbackState!)
            : {};

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Parse file handling configuration if provided
    if (widget.fileHandlingConfig != null) {
      _fileHandlingConfig = Map<String, dynamic>.from(
        widget.fileHandlingConfig!,
      );

      // Apply initial file handling configuration if enabled
      if (widget.useJsonFileHandling) {
        _applyFileHandlingConfig();
      }
    }

    // Execute onInit callback if defined in JSON
    _executeJsonCallback('onInit');
  }

  /// Executes a callback defined in JSON
  ///
  /// This method interprets and executes a callback defined in the JSON configuration.
  /// It supports various callback types and provides access to the current state.
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      _callbackState['selectedFiles'] =
          _selectedFiles
              .map(
                (file) => {
                  'name': file.name,
                  'size': file.size,
                  'path': file.path,
                  'extension': file.extension,
                },
              )
              .toList();
      _callbackState['isValid'] = _isValid;
      _callbackState['isUploading'] = _isUploading;
      _callbackState['uploadProgress'] = _uploadProgress;

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue = _selectedFiles.map((file) => file.name).toList();
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the selected files
  ///
  /// This method applies validation rules defined in the JSON configuration.
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    // Example: Apply validation rules
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply required validation
        if (rules.containsKey('required') && rules['required'] == true) {
          if (_selectedFiles.isEmpty) {
            setState(() {
              _errorText = 'File selection is required';
              _isValid = false;
            });
            return;
          }
        }

        // Apply max files validation
        if (rules.containsKey('maxFiles') && rules['maxFiles'] is int) {
          final maxFiles = rules['maxFiles'] as int;
          if (_selectedFiles.length > maxFiles) {
            setState(() {
              _errorText = 'Maximum $maxFiles files allowed';
              _isValid = false;
            });
            return;
          }
        }

        // Apply max file size validation
        if (rules.containsKey('maxFileSizeBytes') &&
            rules['maxFileSizeBytes'] is int) {
          final maxFileSizeBytes = rules['maxFileSizeBytes'] as int;
          for (final file in _selectedFiles) {
            if (file.size > maxFileSizeBytes) {
              setState(() {
                _errorText =
                    'File size exceeds ${_formatFileSize(maxFileSizeBytes)}';
                _isValid = false;
              });
              return;
            }
          }
        }

        // Apply allowed extensions validation
        if (rules.containsKey('allowedExtensions') &&
            rules['allowedExtensions'] is List) {
          final allowedExtensions = List<String>.from(
            (rules['allowedExtensions'] as List).map((e) => e.toString()),
          );
          for (final file in _selectedFiles) {
            final extension = path
                .extension(file.name)
                .toLowerCase()
                .replaceFirst('.', '');
            if (!allowedExtensions
                .map((e) => e.toLowerCase().replaceFirst('.', ''))
                .contains(extension)) {
              setState(() {
                _errorText =
                    'Invalid file type. Allowed: ${allowedExtensions.join(', ')}';
                _isValid = false;
              });
              return;
            }
          }
        }

        // Apply blocked extensions validation
        if (rules.containsKey('blockedExtensions') &&
            rules['blockedExtensions'] is List) {
          final blockedExtensions = List<String>.from(
            (rules['blockedExtensions'] as List).map((e) => e.toString()),
          );
          for (final file in _selectedFiles) {
            final extension = path
                .extension(file.name)
                .toLowerCase()
                .replaceFirst('.', '');
            if (blockedExtensions
                .map((e) => e.toLowerCase().replaceFirst('.', ''))
                .contains(extension)) {
              setState(() {
                _errorText = 'File type not allowed: .$extension';
                _isValid = false;
              });
              return;
            }
          }
        }

        // Apply custom validation
        if (rules.containsKey('custom') && rules['custom'] is String) {
          final customRule = rules['custom'] as String;

          // Example: Check if all files have the same extension
          if (customRule == 'sameExtension' && _selectedFiles.length > 1) {
            final firstExtension =
                path.extension(_selectedFiles.first.name).toLowerCase();
            for (final file in _selectedFiles.skip(1)) {
              final extension = path.extension(file.name).toLowerCase();
              if (extension != firstExtension) {
                setState(() {
                  _errorText = 'All files must have the same extension';
                  _isValid = false;
                });
                return;
              }
            }
          }
        }
      }
    }

    setState(() {
      _errorText = widget.errorText;
      _isValid = true;
    });
  }

  /// Applies JSON styling to the widget
  ///
  /// This method applies styling rules defined in the JSON configuration.
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current value
  ///
  /// This method applies formatting rules defined in the JSON configuration.
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  /// Applies file handling configuration to the widget
  ///
  /// This method applies file handling rules defined in the JSON configuration.
  void _applyFileHandlingConfig() {
    if (_fileHandlingConfig == null || !widget.useJsonFileHandling) return;

    // Example: Apply file handling rules
    if (_fileHandlingConfig!.containsKey('autoUpload')) {
      // This would be implemented to auto-upload files
      // Not fully implemented in this example
    }

    if (_fileHandlingConfig!.containsKey('fileProcessing')) {
      // This would be implemented to process files (e.g., resize images)
      // Not fully implemented in this example
    }

    if (_fileHandlingConfig!.containsKey('fileNaming')) {
      // This would be implemented to rename files
      // Not fully implemented in this example
    }
  }

  /// Validates the selected files and updates the error text
  bool _validateFiles() {
    if (_selectedFiles.isEmpty) {
      if (widget.isRequired) {
        setState(() {
          _errorText = 'File selection is required';
          _isValid = false;
        });
        return false;
      } else {
        setState(() {
          _errorText = widget.errorText;
          _isValid = true;
        });
        return true;
      }
    }

    // Check max files
    if (widget.maxFiles != null && _selectedFiles.length > widget.maxFiles!) {
      setState(() {
        _errorText = 'Maximum ${widget.maxFiles} files allowed';
        _isValid = false;
      });
      return false;
    }

    // Check file size
    if (widget.maxFileSizeBytes != null) {
      for (final file in _selectedFiles) {
        if (file.size > widget.maxFileSizeBytes!) {
          setState(() {
            _errorText =
                'File size exceeds ${_formatFileSize(widget.maxFileSizeBytes!)}';
            _isValid = false;
          });
          return false;
        }
      }
    }

    // Check file extensions
    if (widget.allowedExtensions != null &&
        widget.allowedExtensions!.isNotEmpty) {
      for (final file in _selectedFiles) {
        final extension = path
            .extension(file.name)
            .toLowerCase()
            .replaceFirst('.', '');
        if (!widget.allowedExtensions!
            .map((e) => e.toLowerCase().replaceFirst('.', ''))
            .contains(extension)) {
          setState(() {
            _errorText =
                'Invalid file type. Allowed: ${widget.allowedExtensions!.join(', ')}';
            _isValid = false;
          });
          return false;
        }
      }
    }

    setState(() {
      _errorText = widget.errorText;
      _isValid = true;
    });
    return true;
  }

  /// Formats file size in bytes to a human-readable format
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Opens the file picker dialog
  Future<void> _pickFiles() async {
    if (widget.isDisabled || widget.isReadOnly) return;

    // Execute onBeforePickFiles callback if defined in JSON
    _executeJsonCallback('onBeforePickFiles');

    try {
      // Get file type and allowed extensions from JSON config if available
      FileType effectiveFileType = widget.fileType;
      List<String>? effectiveAllowedExtensions = widget.allowedExtensions;

      if (widget.useJsonFileHandling && _fileHandlingConfig != null) {
        if (_fileHandlingConfig!.containsKey('fileType')) {
          final fileTypeStr = _fileHandlingConfig!['fileType'] as String?;
          if (fileTypeStr != null) {
            switch (fileTypeStr.toLowerCase()) {
              case 'any':
                effectiveFileType = FileType.any;
                break;
              case 'image':
                effectiveFileType = FileType.image;
                break;
              case 'video':
                effectiveFileType = FileType.video;
                break;
              case 'audio':
                effectiveFileType = FileType.audio;
                break;
              case 'media':
                effectiveFileType = FileType.media;
                break;
              case 'custom':
                effectiveFileType = FileType.custom;
                break;
            }
          }
        }

        if (_fileHandlingConfig!.containsKey('allowedExtensions')) {
          final extensions = _fileHandlingConfig!['allowedExtensions'];
          if (extensions is List) {
            effectiveAllowedExtensions = List<String>.from(
              extensions.map((e) => e.toString()),
            );
          } else if (extensions is String) {
            effectiveAllowedExtensions =
                extensions.split(',').map((e) => e.trim()).toList();
          }
        }
      }

      final result = await FilePicker.platform.pickFiles(
        type: effectiveFileType,
        allowMultiple: widget.allowMultiple,
        allowedExtensions:
            effectiveFileType == FileType.custom
                ? effectiveAllowedExtensions
                : null,
      );

      if (result != null) {
        setState(() {
          _selectedFiles = result.files;
        });

        // Validate files using JSON validation if enabled
        if (widget.useJsonValidation) {
          _applyJsonValidation();
        } else {
          _validateFiles();
        }

        // Call standard callback
        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(_selectedFiles);
        }

        // Execute onFilesSelected callback if defined in JSON
        _executeJsonCallback(
          'onFilesSelected',
          _selectedFiles.map((file) => file.name).toList(),
        );

        if (widget.uploadImmediately && _isValid) {
          _uploadFiles();
        }
      } else {
        // Execute onCancelled callback if defined in JSON
        _executeJsonCallback('onCancelled');
      }
    } catch (e) {
      setState(() {
        _errorText = 'Error picking files: $e';
        _isValid = false;
      });

      // Execute onError callback if defined in JSON
      _executeJsonCallback('onError', 'Error picking files: $e');
    }
  }

  /// Clears the selected files
  void _clearFiles() {
    // Execute onBeforeClear callback if defined in JSON
    _executeJsonCallback('onBeforeClear');

    // Store the files being cleared for the callback
    final clearedFiles = List<PlatformFile>.from(_selectedFiles);

    setState(() {
      _selectedFiles = [];
      _errorText = widget.errorText;
      _isValid = true;
      _uploadProgress = 0.0;
      _isUploading = false;
    });

    // Call standard callback
    if (widget.onClear != null) {
      widget.onClear!();
    }

    // Execute onClear callback if defined in JSON
    _executeJsonCallback(
      'onClear',
      clearedFiles.map((file) => file.name).toList(),
    );
  }

  /// Simulates file upload
  Future<void> _uploadFiles() async {
    if (_selectedFiles.isEmpty || widget.isDisabled || widget.isReadOnly)
      return;

    // Execute onBeforeUpload callback if defined in JSON
    _executeJsonCallback('onBeforeUpload');

    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
    });

    // Execute onUploadStart callback if defined in JSON
    _executeJsonCallback(
      'onUploadStart',
      _selectedFiles.map((file) => file.name).toList(),
    );

    try {
      // Get upload configuration from JSON if available
      int progressSteps = 20; // Default: update progress in 5% increments
      int baseDelayMs = 100; // Default base delay

      if (widget.useJsonFileHandling && _fileHandlingConfig != null) {
        if (_fileHandlingConfig!.containsKey('progressSteps')) {
          progressSteps = _fileHandlingConfig!['progressSteps'] as int? ?? 20;
        }

        if (_fileHandlingConfig!.containsKey('baseDelayMs')) {
          baseDelayMs = _fileHandlingConfig!['baseDelayMs'] as int? ?? 100;
        }
      }

      // Calculate step size
      final stepSize = 100 / progressSteps;

      // Simulate upload progress with a more realistic timing
      for (int i = 0; i <= progressSteps; i++) {
        // Simulate network latency and file size impact on upload speed
        final totalSize = _selectedFiles.fold(
          0,
          (sum, file) => sum + file.size,
        );
        final sizeFactor = totalSize > 1024 * 1024 * 5 ? 1.5 : 1.0;
        final delay = Duration(
          milliseconds: (baseDelayMs * sizeFactor).toInt(),
        );

        await Future.delayed(delay);

        final progress = i * stepSize / 100;
        setState(() {
          _uploadProgress = progress;
        });

        // Execute onUploadProgress callback if defined in JSON
        _executeJsonCallback('onUploadProgress', {
          'progress': progress,
          'percentage': (progress * 100).toInt(),
        });
      }

      // Show a completed state briefly before finishing
      await Future.delayed(const Duration(milliseconds: 500));

      // In a real app, you would make an HTTP request to upload the file here
      // For example:
      // final response = await http.post(
      //   Uri.parse('https://example.com/upload'),
      //   body: await File(file.path!).readAsBytes(),
      // );

      setState(() {
        _isUploading = false;
      });

      // Call standard callback
      if (widget.onUpload != null) {
        widget.onUpload!(_selectedFiles);
      }

      // Execute onUploadComplete callback if defined in JSON
      _executeJsonCallback(
        'onUploadComplete',
        _selectedFiles.map((file) => file.name).toList(),
      );

      // Show a success message or perform additional actions after successful upload
    } catch (e) {
      // Handle upload errors
      setState(() {
        _isUploading = false;
        _errorText = 'Upload failed: $e';
        _isValid = false;
      });

      // Execute onUploadError callback if defined in JSON
      _executeJsonCallback('onUploadError', 'Upload failed: $e');
    }
  }

  /// Builds the file preview widget
  Widget _buildFilePreview(PlatformFile file) {
    final extension = path.extension(file.name).toLowerCase();

    // Check if it's an image file
    if ([
          '.jpg',
          '.jpeg',
          '.png',
          '.gif',
          '.webp',
          '.bmp',
        ].contains(extension) &&
        file.path != null) {
      try {
        return ClipRRect(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          child: Image.file(
            File(file.path!),
            width: 60,
            height: 60,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // Fallback if image can't be loaded
              return Container(
                width: 60,
                height: 60,
                color: Colors.grey.shade300,
                child: const Icon(Icons.image, color: Colors.grey),
              );
            },
          ),
        );
      } catch (e) {
        // Fallback if there's an error
        return Container(
          width: 60,
          height: 60,
          color: Colors.grey.shade300,
          child: const Icon(Icons.image, color: Colors.grey),
        );
      }
    }

    // For other file types, show an icon based on extension
    IconData iconData;
    Color iconColor;

    if (['.pdf'].contains(extension)) {
      iconData = Icons.picture_as_pdf;
      iconColor = Colors.red;
    } else if (['.doc', '.docx'].contains(extension)) {
      iconData = Icons.description;
      iconColor = Colors.blue;
    } else if (['.xls', '.xlsx', '.csv'].contains(extension)) {
      iconData = Icons.table_chart;
      iconColor = Colors.green;
    } else if (['.ppt', '.pptx'].contains(extension)) {
      iconData = Icons.slideshow;
      iconColor = Colors.orange;
    } else if (['.zip', '.rar', '.7z'].contains(extension)) {
      iconData = Icons.folder_zip;
      iconColor = Colors.purple;
    } else if (['.txt', '.rtf'].contains(extension)) {
      iconData = Icons.text_snippet;
      iconColor = Colors.grey;
    } else {
      iconData = Icons.insert_drive_file;
      iconColor = Colors.grey;
    }

    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: iconColor.withAlpha(25), // 0.1 opacity is approximately 25 alpha
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Center(child: Icon(iconData, color: iconColor, size: 30)),
    );
  }

  /// Builds the file info widget
  Widget _buildFileInfo(PlatformFile file) {
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showFileName)
            Text(
              file.name,
              style: TextStyle(
                color: effectiveTextColor,
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          if (widget.showFileSize || widget.showFileType)
            Row(
              children: [
                if (widget.showFileSize)
                  Text(
                    _formatFileSize(file.size),
                    style: TextStyle(
                      color: effectiveTextColor.withAlpha(
                        179,
                      ), // 0.7 opacity is approximately 179 alpha
                      fontSize: widget.fontSize * 0.8,
                    ),
                  ),
                if (widget.showFileSize && widget.showFileType)
                  Text(
                    ' • ',
                    style: TextStyle(
                      color: effectiveTextColor.withAlpha(
                        179,
                      ), // 0.7 opacity is approximately 179 alpha
                      fontSize: widget.fontSize * 0.8,
                    ),
                  ),
                if (widget.showFileType)
                  Text(
                    path
                        .extension(file.name)
                        .toUpperCase()
                        .replaceFirst('.', ''),
                    style: TextStyle(
                      color: effectiveTextColor.withAlpha(
                        179,
                      ), // 0.7 opacity is approximately 179 alpha
                      fontSize: widget.fontSize * 0.8,
                    ),
                  ),
              ],
            ),
        ],
      ),
    );
  }

  /// Builds the selected files list
  Widget _buildSelectedFiles() {
    if (_selectedFiles.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 0),
        ...List.generate(_selectedFiles.length, (index) {
          final file = _selectedFiles[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color:
                  widget.isDarkTheme
                      ? Colors.grey.shade800
                      : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(widget.borderRadius),
            ),
            child: Row(
              children: [
                if (widget.showPreview) ...[
                  _buildFilePreview(file),
                  const SizedBox(width: 8),
                ],
                _buildFileInfo(file),
                if (widget.showClearButton &&
                    !widget.isDisabled &&
                    !widget.isReadOnly)
                  IconButton(
                    icon: const Icon(Icons.close, size: 18),
                    onPressed: () {
                      setState(() {
                        _selectedFiles.removeAt(index);
                      });
                      _validateFiles();
                    },
                    tooltip: 'Remove',
                  ),
              ],
            ),
          );
        }),
        if (_isUploading && widget.showProgressBar) ...[
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: _uploadProgress,
            backgroundColor:
                widget.isDarkTheme
                    ? Colors.grey.shade700
                    : Colors.grey.shade300,
            valueColor: AlwaysStoppedAnimation<Color>(
              widget.isDarkTheme ? Colors.blue.shade300 : Colors.blue,
            ),
          ),
          const SizedBox(height: 2), // Reduced from 4 to 2 to prevent overflow
          Text(
            'Uploading... ${(_uploadProgress * 100).toInt()}%',
            style: TextStyle(
              color: widget.isDarkTheme ? Colors.white70 : Colors.black54,
              fontSize: widget.fontSize * 0.8,
            ),
          ),
        ],
      ],
    );
  }

  /// Builds the drag and drop area
  Widget _buildDragDropArea(Widget child) {
    if (!widget.allowDragDrop || widget.isDisabled || widget.isReadOnly) {
      return child;
    }

    return DragTarget<List<PlatformFile>>(
      // Using the newer API for drag and drop
      onWillAcceptWithDetails: (details) {
        setState(() {
          _isDragging = true;
        });
        return true;
      },
      onAcceptWithDetails: (details) {
        final data = details.data;
        setState(() {
          _isDragging = false;
          _selectedFiles = data;
        });
        _validateFiles();

        if (widget.onFilesSelected != null) {
          widget.onFilesSelected!(_selectedFiles);
        }

        if (widget.uploadImmediately && _isValid) {
          _uploadFiles();
        }
      },
      onLeave: (data) {
        setState(() {
          _isDragging = false;
        });
      },
      builder: (context, candidateData, rejectedData) {
        return Container(
          decoration: BoxDecoration(
            color:
                _isDragging
                    ? (widget.isDarkTheme
                        ? Colors.blue.shade900.withAlpha(77)
                        : Colors.blue.shade100.withAlpha(
                          77,
                        )) // 0.3 opacity is approximately 77 alpha
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border:
                _isDragging
                    ? Border.all(
                      color:
                          widget.isDarkTheme
                              ? Colors.blue.shade300
                              : Colors.blue,
                      width: 2,
                      style:
                          BorderStyle
                              .solid, // Use solid instead of dashed for compatibility
                    )
                    : null,
          ),
          child: child,
        );
      },
    );
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 16.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 12.0; // Small (768-1024px)
    } else {
      return 14.0; // Default for very small screens
    }
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;
    final effectiveBorderColor =
        widget.isDarkTheme ? Colors.grey.shade600 : Color(0xFFCCCCCC);

    // Create the file picker button
    final filePickerButton = ElevatedButton.icon(
      onPressed: widget.isDisabled || widget.isReadOnly ? null : _pickFiles,
      icon:
          widget.showIcon && widget.icon != null
              ? Icon(
                widget.icon,
                color: widget.isDarkTheme ? Colors.white : null,
              )
              : const SizedBox.shrink(),
      label: Text(widget.buttonText),
      style: ElevatedButton.styleFrom(
        //backgroundColor: widget.isDarkTheme ? Colors.blue.shade700 : null,
        backgroundColor: const Color(0xFF0058FF),
        foregroundColor: widget.isDarkTheme ? Colors.white : null,
        disabledBackgroundColor:
            widget.isDarkTheme ? Colors.grey.shade700 : null,
        disabledForegroundColor:
            widget.isDarkTheme ? Colors.grey.shade400 : null,
        padding:
            widget.isCompact
                ? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0)
                : const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        textStyle: TextStyle(
          //fontSize: widget.fontSize,
          fontSize: _getResponsiveFontSize(context),
          fontWeight: widget.fontWeight,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
      ),
    );

    // Create the upload button if needed
    final uploadButton =
        !widget.uploadImmediately &&
                _selectedFiles.isNotEmpty &&
                !widget.isDisabled &&
                !widget.isReadOnly
            ? ElevatedButton.icon(
              onPressed: _isUploading ? null : _uploadFiles,
              icon: const Icon(Icons.cloud_upload),
              label: Text(_isUploading ? 'Uploading...' : 'Upload'),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    widget.isDarkTheme ? Colors.green.shade700 : Colors.green,
                foregroundColor: Colors.white,
                disabledBackgroundColor:
                    widget.isDarkTheme ? Colors.grey.shade700 : null,
                disabledForegroundColor:
                    widget.isDarkTheme ? Colors.grey.shade400 : null,
                padding:
                    widget.isCompact
                        ? const EdgeInsets.symmetric(
                          horizontal: 8.0,
                          vertical: 4.0,
                        )
                        : const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 8.0,
                        ),
                textStyle: TextStyle(
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                ),
              ),
            )
            : null;

    // Create the clear button if needed
    final clearButton =
        _selectedFiles.isNotEmpty &&
                widget.showClearButton &&
                !widget.isDisabled &&
                !widget.isReadOnly
            ? TextButton.icon(
              onPressed: _clearFiles,
              icon: const Icon(Icons.delete_outline),
              label: const Text('Clear'),
              style: TextButton.styleFrom(
                foregroundColor:
                    widget.isDarkTheme ? Colors.red.shade300 : Colors.red,
                padding:
                    widget.isCompact
                        ? const EdgeInsets.symmetric(
                          horizontal: 8.0,
                          vertical: 4.0,
                        )
                        : const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 8.0,
                        ),
                textStyle: TextStyle(
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
              ),
            )
            : null;

    // Create the main content
    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label removed as per user request
        Container(
          padding: widget.padding,
          decoration: BoxDecoration(
            color:
                widget.isDisabled
                    ? (widget.isDarkTheme
                        ? Colors.grey.shade900
                        : Colors.grey.shade200)
                    : effectiveBackgroundColor,
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border:
                widget.hasBorder
                    ? Border.all(
                      color: _isValid ? effectiveBorderColor : Colors.red,
                      width: widget.borderWidth,
                    )
                    : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // if (widget.label != null) ...[
              //   Text(
              //     widget.label!,
              //     style: TextStyle(
              //       color: effectiveTextColor,
              //       fontSize: widget.fontSize,
              //       fontWeight: FontWeight.bold,
              //     ),
              //   ),
              //   const SizedBox(height: 8.0),
              // ],
              Row(
                children: [
                  filePickerButton,
                  if (uploadButton != null) ...[
                    const SizedBox(width: 8),
                    uploadButton,
                  ],
                  if (clearButton != null) ...[
                    const SizedBox(width: 8),
                    clearButton,
                  ],
                ],
              ),

              _buildSelectedFiles(),
            ],
          ),
        ),

        if (_errorText != null) ...[
          const SizedBox(height: 2), // Reduced from 4 to 2 to prevent overflow
          Text(
            _errorText!,
            style: TextStyle(
              color: Colors.red,
              fontSize: widget.fontSize * 0.8,
            ),
          ),
        ] else if (widget.helperText != null) ...[
          const SizedBox(height: 2), // Reduced from 4 to 2 to prevent overflow
          Text(
            widget.helperText!,
            style: TextStyle(
              color: effectiveTextColor.withAlpha(
                179,
              ), // 0.7 opacity is approximately 179 alpha
              fontSize: widget.fontSize * 0.8,
            ),
          ),
        ],
      ],
    );

    // Apply drag and drop if enabled
    final dragDropContent = _buildDragDropArea(content);

    // Apply shadow if needed
    final shadowWidget =
        widget.hasShadow
            ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(
                      25,
                    ), // 0.1 opacity is approximately 25 alpha
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: dragDropContent,
            )
            : dragDropContent;

    // Create the final widget with the specified size
    return Container(
      width: widget.width,
      // Remove fixed height to prevent overflow - let content determine height
      //height: widget.height > 0 ? widget.height : null,
      height: _getResponsiveHeight(context),
      margin: widget.margin,
      child: shadowWidget,
    );
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Base height for input field
    double baseHeight;
    if (screenWidth > 1920) {
      baseHeight = 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      baseHeight = 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      baseHeight = 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      baseHeight = 32.0; // Small (768-1024px)
    } else {
      baseHeight = 32.0; // Default for very small screens
    }

    // Add extra height if there's an error message to prevent overlap
    if (_errorText != null && _errorText!.isNotEmpty) {
      baseHeight += 24.0; // Add space for error text
    }

    // Add extra height if there's helper text
    if (widget.helperText != null && widget.helperText!.isNotEmpty) {
      baseHeight += 20.0; // Add space for helper text
    }

    return baseHeight;
  }
}
