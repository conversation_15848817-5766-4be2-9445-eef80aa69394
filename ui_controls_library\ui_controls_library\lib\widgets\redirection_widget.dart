import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension RedirectionColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// Redirection types supported by the widget
enum RedirectionType {
  /// External URL (opens in browser)
  external,

  /// Internal app navigation
  internal,

  /// Email link (mailto:)
  email,

  /// Phone link (tel:)
  phone,

  /// SMS link (sms:)
  sms,

  /// File link (file:)
  file,

  /// Custom scheme (e.g., whatsapp:, telegram:)
  customScheme,
}

/// Launch modes for external URLs
enum ExternalLaunchMode {
  /// Use platform default behavior
  platformDefault,

  /// Open in external application
  externalApplication,

  /// Open in web view within the app
  inAppWebView,
}

/// A comprehensive redirection widget that handles various types of navigation and URL schemes.
class RedirectionWidget extends StatefulWidget {
  // Content properties
  final String text;
  final String destination;
  final RedirectionType redirectionType;
  final Map<String, dynamic>? parameters;
  final IconData? icon;
  final bool showIcon;
  final bool iconLeading;
  final Widget? customChild;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color? hoverColor;
  final Color? activeColor;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isItalic;
  final bool isUnderlined;
  final String? fontFamily;
  final bool hasShadow;
  final double elevation;
  final double? width;
  final double? height;

  // Layout properties
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final bool hasBorder;
  final Color borderColor;
  final double borderWidth;
  final Alignment alignment;

  // Behavior properties
  final ExternalLaunchMode externalLaunchMode;
  final bool showConfirmationDialog;
  final String? confirmationTitle;
  final String? confirmationMessage;
  final String? tooltip;
  final bool isDisabled;
  final bool showLoadingIndicator;
  final Duration? redirectDelay;
  final bool showSnackbarOnError;
  final String? errorMessage;
  final bool trackAnalytics;
  final bool preventMultipleTaps;

  // Animation properties
  final bool hasAnimation;
  final Duration animationDuration;
  final Curve animationCurve;

  // Callbacks
  final Function(String)? onTap;
  final Function(String)? onLongPress;
  final Function(String)? beforeRedirect;
  final Function(String, bool)? afterRedirect;
  final Function(String, dynamic)? onError;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Redirection-specific JSON configuration
  /// Whether to use JSON redirection configuration
  final bool useJsonRedirectionConfig;

  /// Redirection-specific JSON configuration
  final Map<String, dynamic>? redirectionConfig;

  const RedirectionWidget({
    super.key,
    required this.text,
    required this.destination,
    this.redirectionType = RedirectionType.external,
    this.parameters,
    this.icon,
    this.showIcon = false,
    this.iconLeading = true,
    this.customChild,
    this.textColor = Colors.blue,
    this.backgroundColor = Colors.transparent,
    this.hoverColor,
    this.activeColor,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.normal,
    this.isItalic = false,
    this.isUnderlined = true,
    this.fontFamily,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(4.0),
    this.margin = const EdgeInsets.all(0),
    this.borderRadius = 4.0,
    this.hasBorder = false,
    this.borderColor = Colors.blue,
    this.borderWidth = 1.0,
    this.alignment = Alignment.center,
    this.externalLaunchMode = ExternalLaunchMode.platformDefault,
    this.showConfirmationDialog = false,
    this.confirmationTitle,
    this.confirmationMessage,
    this.tooltip,
    this.isDisabled = false,
    this.showLoadingIndicator = false,
    this.redirectDelay,
    this.showSnackbarOnError = true,
    this.errorMessage,
    this.trackAnalytics = false,
    this.preventMultipleTaps = true,
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    this.onTap,
    this.onLongPress,
    this.beforeRedirect,
    this.afterRedirect,
    this.onError,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Redirection-specific JSON configuration
    this.useJsonRedirectionConfig = false,
    this.redirectionConfig,
  });

  /// Creates a RedirectionWidget from a JSON map
  ///
  /// This factory constructor allows for creating a RedirectionWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory RedirectionWidget.fromJson(Map<String, dynamic> json) {
    // Parse redirection type
    RedirectionType redirectionType = RedirectionType.external;
    if (json.containsKey('redirectionType')) {
      final String typeStr = json['redirectionType'].toString().toLowerCase();
      if (typeStr == 'internal') {
        redirectionType = RedirectionType.internal;
      } else if (typeStr == 'email') {
        redirectionType = RedirectionType.email;
      } else if (typeStr == 'phone') {
        redirectionType = RedirectionType.phone;
      } else if (typeStr == 'sms') {
        redirectionType = RedirectionType.sms;
      } else if (typeStr == 'file') {
        redirectionType = RedirectionType.file;
      } else if (typeStr == 'customscheme') {
        redirectionType = RedirectionType.customScheme;
      }
    }

    // Parse external launch mode
    ExternalLaunchMode externalLaunchMode = ExternalLaunchMode.platformDefault;
    if (json.containsKey('externalLaunchMode')) {
      final String modeStr =
          json['externalLaunchMode'].toString().toLowerCase();
      if (modeStr == 'externalapplication') {
        externalLaunchMode = ExternalLaunchMode.externalApplication;
      } else if (modeStr == 'inappwebview') {
        externalLaunchMode = ExternalLaunchMode.inAppWebView;
      }
    }

    // Parse font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json.containsKey('fontWeight')) {
      if (json['fontWeight'] is String) {
        switch ((json['fontWeight'] as String).toLowerCase()) {
          case 'thin':
            fontWeight = FontWeight.w100;
            break;
          case 'extralight':
            fontWeight = FontWeight.w200;
            break;
          case 'light':
            fontWeight = FontWeight.w300;
            break;
          case 'regular':
            fontWeight = FontWeight.w400;
            break;
          case 'medium':
            fontWeight = FontWeight.w500;
            break;
          case 'semibold':
            fontWeight = FontWeight.w600;
            break;
          case 'bold':
            fontWeight = FontWeight.w700;
            break;
          case 'extrabold':
            fontWeight = FontWeight.w800;
            break;
          case 'black':
            fontWeight = FontWeight.w900;
            break;
        }
      } else if (json['fontWeight'] is int) {
        final int weight = json['fontWeight'] as int;
        switch (weight) {
          case 100:
            fontWeight = FontWeight.w100;
            break;
          case 200:
            fontWeight = FontWeight.w200;
            break;
          case 300:
            fontWeight = FontWeight.w300;
            break;
          case 400:
            fontWeight = FontWeight.w400;
            break;
          case 500:
            fontWeight = FontWeight.w500;
            break;
          case 600:
            fontWeight = FontWeight.w600;
            break;
          case 700:
            fontWeight = FontWeight.w700;
            break;
          case 800:
            fontWeight = FontWeight.w800;
            break;
          case 900:
            fontWeight = FontWeight.w900;
            break;
        }
      }
    }

    // Parse colors
    Color textColor = Colors.blue;
    if (json.containsKey('textColor')) {
      textColor = _parseColor(json['textColor']);
    }

    Color backgroundColor = Colors.transparent;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color? hoverColor;
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color? activeColor;
    if (json.containsKey('activeColor')) {
      activeColor = _parseColor(json['activeColor']);
    }

    Color borderColor = Colors.blue;
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.all(4.0);
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = EdgeInsets.zero;
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    // Parse alignment
    Alignment alignment = Alignment.center;
    if (json.containsKey('alignment')) {
      alignment = _parseAlignment(json['alignment']);
    }

    // Parse animation curve
    Curve animationCurve = Curves.easeInOut;
    if (json.containsKey('animationCurve')) {
      final String curveStr = json['animationCurve'].toString().toLowerCase();
      if (curveStr == 'linear') {
        animationCurve = Curves.linear;
      } else if (curveStr == 'decelerate') {
        animationCurve = Curves.decelerate;
      } else if (curveStr == 'ease') {
        animationCurve = Curves.ease;
      } else if (curveStr == 'easein') {
        animationCurve = Curves.easeIn;
      } else if (curveStr == 'easeout') {
        animationCurve = Curves.easeOut;
      } else if (curveStr == 'elasticin') {
        animationCurve = Curves.elasticIn;
      } else if (curveStr == 'elasticout') {
        animationCurve = Curves.elasticOut;
      } else if (curveStr == 'elasticinout') {
        animationCurve = Curves.elasticInOut;
      }
    }

    // Parse animation duration
    Duration animationDuration = const Duration(milliseconds: 300);
    if (json.containsKey('animationDuration')) {
      animationDuration = Duration(
        milliseconds: json['animationDuration'] as int? ?? 300,
      );
    }

    // Parse redirect delay
    Duration? redirectDelay;
    if (json.containsKey('redirectDelay')) {
      redirectDelay = Duration(
        milliseconds: json['redirectDelay'] as int? ?? 0,
      );
    }

    // Parse parameters
    Map<String, String>? parameters;
    if (json.containsKey('parameters') && json['parameters'] is Map) {
      parameters = Map<String, String>.from(
        (json['parameters'] as Map).map(
          (key, value) => MapEntry(key.toString(), value.toString()),
        ),
      );
    }

    // Parse icon
    IconData? icon;
    if (json.containsKey('icon')) {
      icon = _parseIconData(json['icon']);
    }

    return RedirectionWidget(
      // Content properties
      text: json['text'] as String? ?? '',
      destination: json['destination'] as String? ?? '',
      redirectionType: redirectionType,
      parameters: parameters,
      icon: icon,
      showIcon: json['showIcon'] as bool? ?? false,
      iconLeading: json['iconLeading'] as bool? ?? true,

      // Appearance properties
      textColor: textColor,
      backgroundColor: backgroundColor,
      hoverColor: hoverColor,
      activeColor: activeColor,
      fontSize:
          json['fontSize'] != null
              ? (json['fontSize'] as num).toDouble()
              : 14.0,
      fontWeight: fontWeight,
      isItalic: json['isItalic'] as bool? ?? false,
      isUnderlined: json['isUnderlined'] as bool? ?? true,
      fontFamily: json['fontFamily'] as String?,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,

      // Layout properties
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      padding: padding,
      margin: margin,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? false,
      borderColor: borderColor,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      alignment: alignment,

      // Behavior properties
      externalLaunchMode: externalLaunchMode,
      showConfirmationDialog: json['showConfirmationDialog'] as bool? ?? false,
      confirmationTitle: json['confirmationTitle'] as String?,
      confirmationMessage: json['confirmationMessage'] as String?,
      tooltip: json['tooltip'] as String?,
      isDisabled: json['isDisabled'] as bool? ?? false,
      showLoadingIndicator: json['showLoadingIndicator'] as bool? ?? false,
      redirectDelay: redirectDelay,
      showSnackbarOnError: json['showSnackbarOnError'] as bool? ?? true,
      errorMessage: json['errorMessage'] as String?,
      trackAnalytics: json['trackAnalytics'] as bool? ?? false,
      preventMultipleTaps: json['preventMultipleTaps'] as bool? ?? true,
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      animationDuration: animationDuration,
      animationCurve: animationCurve,

      // Advanced interaction properties
      autofocus: json['autofocus'] as bool? ?? false,
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonRedirectionConfig:
          json['useJsonRedirectionConfig'] as bool? ?? false,
      jsonConfig: json,
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'transparent':
            return Colors.transparent;
          default:
            return Colors.blue;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Colors.blue; // Default color
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') ||
          value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal:
              value.containsKey('horizontal')
                  ? (value['horizontal'] as num).toDouble()
                  : 0.0,
          vertical:
              value.containsKey('vertical')
                  ? (value['vertical'] as num).toDouble()
                  : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom')
              ? (value['bottom'] as num).toDouble()
              : 0.0,
        );
      }
    }
    return const EdgeInsets.all(0.0); // Default padding
  }

  /// Parses an alignment from a string
  static Alignment _parseAlignment(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'topleft':
          return Alignment.topLeft;
        case 'topcenter':
          return Alignment.topCenter;
        case 'topright':
          return Alignment.topRight;
        case 'centerleft':
          return Alignment.centerLeft;
        case 'center':
          return Alignment.center;
        case 'centerright':
          return Alignment.centerRight;
        case 'bottomleft':
          return Alignment.bottomLeft;
        case 'bottomcenter':
          return Alignment.bottomCenter;
        case 'bottomright':
          return Alignment.bottomRight;
        default:
          return Alignment.center;
      }
    }
    return Alignment.center;
  }

  /// Parses icon data from a string
  static IconData? _parseIconData(String? iconName) {
    if (iconName == null) return null;

    switch (iconName.toLowerCase()) {
      case 'link':
        return Icons.link;
      case 'open_in_new':
        return Icons.open_in_new;
      case 'open_in_browser':
        return Icons.open_in_browser;
      case 'launch':
        return Icons.launch;
      case 'arrow_forward':
        return Icons.arrow_forward;
      case 'arrow_right':
        return Icons.arrow_right;
      case 'email':
        return Icons.email;
      case 'phone':
        return Icons.phone;
      case 'message':
        return Icons.message;
      case 'file_open':
        return Icons.file_open;
      case 'file_download':
        return Icons.file_download;
      default:
        return Icons.link;
    }
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Content properties
      'text': text,
      'destination': destination,
      'redirectionType': redirectionType.toString().split('.').last,
      'parameters': parameters,
      'showIcon': showIcon,
      'iconLeading': iconLeading,

      // Appearance properties
      'textColor': '#${textColor.toHexString()}',
      'backgroundColor': '#${backgroundColor.toHexString()}',
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'activeColor':
          activeColor != null ? '#${activeColor!.toHexString()}' : null,
      'fontSize': fontSize,
      'fontWeight': fontWeight.index * 100 + 100,
      'isItalic': isItalic,
      'isUnderlined': isUnderlined,
      'fontFamily': fontFamily,
      'hasShadow': hasShadow,
      'elevation': elevation,

      // Layout properties
      'width': width,
      'height': height,
      'padding': _edgeInsetsToJson(padding),
      'margin': _edgeInsetsToJson(margin),
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'borderColor': '#${borderColor.toHexString()}',
      'borderWidth': borderWidth,
      'alignment': _alignmentToString(alignment),

      // Behavior properties
      'externalLaunchMode': externalLaunchMode.toString().split('.').last,
      'showConfirmationDialog': showConfirmationDialog,
      'confirmationTitle': confirmationTitle,
      'confirmationMessage': confirmationMessage,
      'tooltip': tooltip,
      'isDisabled': isDisabled,
      'showLoadingIndicator': showLoadingIndicator,
      'redirectDelay': redirectDelay?.inMilliseconds,
      'showSnackbarOnError': showSnackbarOnError,
      'errorMessage': errorMessage,
      'trackAnalytics': trackAnalytics,
      'preventMultipleTaps': preventMultipleTaps,
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,
      'animationCurve': _curveToString(animationCurve),

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonRedirectionConfig': useJsonRedirectionConfig,
    };
  }

  /// Converts EdgeInsetsGeometry to a JSON representation
  static Map<String, dynamic> _edgeInsetsToJson(EdgeInsetsGeometry edgeInsets) {
    if (edgeInsets is EdgeInsets) {
      if (edgeInsets.left == edgeInsets.top &&
          edgeInsets.left == edgeInsets.right &&
          edgeInsets.left == edgeInsets.bottom) {
        return {'all': edgeInsets.left};
      } else if (edgeInsets.left == edgeInsets.right &&
          edgeInsets.top == edgeInsets.bottom) {
        return {'horizontal': edgeInsets.left, 'vertical': edgeInsets.top};
      } else {
        return {
          'left': edgeInsets.left,
          'top': edgeInsets.top,
          'right': edgeInsets.right,
          'bottom': edgeInsets.bottom,
        };
      }
    }
    return {'all': 0.0}; // Default
  }

  /// Converts Alignment to a string representation
  static String _alignmentToString(Alignment alignment) {
    if (alignment == Alignment.topLeft) return 'topLeft';
    if (alignment == Alignment.topCenter) return 'topCenter';
    if (alignment == Alignment.topRight) return 'topRight';
    if (alignment == Alignment.centerLeft) return 'centerLeft';
    if (alignment == Alignment.center) return 'center';
    if (alignment == Alignment.centerRight) return 'centerRight';
    if (alignment == Alignment.bottomLeft) return 'bottomLeft';
    if (alignment == Alignment.bottomCenter) return 'bottomCenter';
    if (alignment == Alignment.bottomRight) return 'bottomRight';
    return 'center';
  }

  /// Converts Curve to a string representation
  static String _curveToString(Curve curve) {
    if (curve == Curves.linear) return 'linear';
    if (curve == Curves.decelerate) return 'decelerate';
    if (curve == Curves.ease) return 'ease';
    if (curve == Curves.easeIn) return 'easeIn';
    if (curve == Curves.easeOut) return 'easeOut';
    if (curve == Curves.easeInOut) return 'easeInOut';
    if (curve == Curves.elasticIn) return 'elasticIn';
    if (curve == Curves.elasticOut) return 'elasticOut';
    if (curve == Curves.elasticInOut) return 'elasticInOut';
    return 'easeInOut';
  }

  @override
  State<RedirectionWidget> createState() => _RedirectionWidgetState();
}

class _RedirectionWidgetState extends State<RedirectionWidget>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  bool _isActive = false;
  bool _isLoading = false;
  bool _isRedirecting = false;
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Interaction state
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );
    _animation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      ),
    );

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(RedirectionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Create the text style
    final textStyle = TextStyle(
      color: _getTextColor(),
      fontSize: widget.fontSize,
      fontWeight: widget.fontWeight,
      fontStyle: widget.isItalic ? FontStyle.italic : FontStyle.normal,
      decoration:
          widget.isUnderlined ? TextDecoration.underline : TextDecoration.none,
      fontFamily: widget.fontFamily,
      decorationColor: widget.textColor,
    );

    // Create the icon if needed
    Widget? iconWidget;
    if (widget.showIcon && widget.icon != null) {
      iconWidget = Icon(
        widget.icon,
        size: widget.fontSize * 1.2,
        color: _getTextColor(),
      );
    }

    // Create the content widget
    Widget contentWidget;
    if (widget.customChild != null) {
      contentWidget = widget.customChild!;
    } else {
      // Create text with icon if needed
      if (iconWidget != null) {
        contentWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children:
              widget.iconLeading
                  ? [
                    iconWidget,
                    const SizedBox(width: 4.0),
                    Flexible(child: Text(widget.text, style: textStyle)),
                  ]
                  : [
                    Flexible(child: Text(widget.text, style: textStyle)),
                    const SizedBox(width: 4.0),
                    iconWidget,
                  ],
        );
      } else {
        contentWidget = Text(widget.text, style: textStyle);
      }
    }

    // Create the styled widget with background, border, etc.
    Widget styledWidget = Container(
      width: widget.width,
      height: widget.height,
      padding: widget.padding,
      margin: widget.margin,
      alignment: widget.alignment,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.hasBorder
                ? Border.all(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                )
                : null,
        boxShadow:
            widget.hasShadow
                ? [
                  BoxShadow(
                    color: Colors.black.withAlpha(26),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ]
                : null,
      ),
      child:
          _isLoading
              ? SizedBox(
                width: widget.fontSize * 1.2,
                height: widget.fontSize * 1.2,
                child: CircularProgressIndicator(
                  strokeWidth: 2.0,
                  valueColor: AlwaysStoppedAnimation<Color>(widget.textColor),
                ),
              )
              : contentWidget,
    );

    // Apply animation if needed
    if (widget.hasAnimation) {
      styledWidget = ScaleTransition(scale: _animation, child: styledWidget);
    }

    // Create the interactive widget
    Widget interactiveWidget = MouseRegion(
      cursor:
          widget.isDisabled
              ? SystemMouseCursors.forbidden
              : SystemMouseCursors.click,
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap:
            widget.isDisabled || _isRedirecting
                ? null
                : () async {
                  if (widget.preventMultipleTaps && _isRedirecting) {
                    return;
                  }

                  setState(() {
                    _isActive = true;
                    if (widget.hasAnimation) {
                      _animationController.forward().then((_) {
                        _animationController.reverse();
                      });
                    }
                  });

                  // Call standard callback
                  if (widget.onTap != null) {
                    widget.onTap!(widget.destination);
                  }

                  // Execute JSON callback if defined
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onTap')) {
                    _executeJsonCallback('onTap', widget.destination);
                  }

                  await _handleRedirection();

                  setState(() {
                    _isActive = false;
                  });
                },
        onLongPress:
            widget.isDisabled ||
                    (widget.onLongPress == null &&
                        !(widget.useJsonCallbacks &&
                            widget.jsonCallbacks != null &&
                            widget.jsonCallbacks!.containsKey('onLongPress')))
                ? null
                : () {
                  // Call standard callback
                  if (widget.onLongPress != null) {
                    widget.onLongPress!(widget.destination);
                  }

                  // Execute JSON callback if defined
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onLongPress')) {
                    _executeJsonCallback('onLongPress', widget.destination);
                  }
                },
        child: styledWidget,
      ),
    );

    // Add tooltip if needed
    if (widget.tooltip != null) {
      interactiveWidget = Tooltip(
        message: widget.tooltip!,
        child: interactiveWidget,
      );
    }

    Widget content = interactiveWidget;

    // Apply focus handling
    if (widget.autofocus || widget.onFocus != null) {
      content = Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        onFocusChange: widget.onFocus,
        child: content,
      );
    }

    // Apply double tap detection
    if (widget.onDoubleTap != null) {
      content = GestureDetector(
        onDoubleTap: () {
          // Execute onDoubleTap callback if defined in JSON
          if (widget.useJsonCallbacks &&
              widget.jsonCallbacks != null &&
              widget.jsonCallbacks!.containsKey('onDoubleTap')) {
            _executeJsonCallback('onDoubleTap');
          }

          // Call standard callback
          widget.onDoubleTap!();
        },
        child: content,
      );
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      content = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: content,
      );
    }

    return content;
  }

  Color _getTextColor() {
    if (widget.isDisabled) {
      return Colors.grey;
    }
    if (_isActive && widget.activeColor != null) {
      return widget.activeColor!;
    }
    if (_isHovered && widget.hoverColor != null) {
      return widget.hoverColor!;
    }
    return widget.textColor;
  }

  Future<void> _handleRedirection() async {
    if (_isRedirecting) {
      return;
    }

    setState(() {
      _isRedirecting = true;
      if (widget.showLoadingIndicator) {
        _isLoading = true;
      }
    });

    try {
      // Call beforeRedirect callback if provided
      if (widget.beforeRedirect != null) {
        widget.beforeRedirect!(widget.destination);
      }

      // Execute JSON callback if defined
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('beforeRedirect')) {
        _executeJsonCallback('beforeRedirect', widget.destination);
      }

      // Track analytics if enabled
      if (widget.trackAnalytics) {
        _trackRedirection();
      }

      // Apply redirect delay if specified
      if (widget.redirectDelay != null) {
        await Future.delayed(widget.redirectDelay!);
      }

      // Show confirmation dialog if needed
      bool shouldProceed = true;
      if (widget.showConfirmationDialog) {
        shouldProceed = await _showConfirmationDialog();
      }

      if (shouldProceed) {
        // Handle redirection based on type
        bool success = false;

        switch (widget.redirectionType) {
          case RedirectionType.internal:
            success = await _handleInternalNavigation();
            break;
          case RedirectionType.external:
            success = await _handleExternalUrl();
            break;
          case RedirectionType.email:
            success = await _handleEmailLink();
            break;
          case RedirectionType.phone:
            success = await _handlePhoneLink();
            break;
          case RedirectionType.sms:
            success = await _handleSmsLink();
            break;
          case RedirectionType.file:
            success = await _handleFileLink();
            break;
          case RedirectionType.customScheme:
            success = await _handleCustomScheme();
            break;
        }

        // Call afterRedirect callback if provided
        if (widget.afterRedirect != null) {
          widget.afterRedirect!(widget.destination, success);
        }

        // Execute JSON callback if defined
        if (widget.useJsonCallbacks &&
            widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('afterRedirect')) {
          _executeJsonCallback('afterRedirect', {
            'destination': widget.destination,
            'success': success,
          });
        }
      }
    } catch (e) {
      // Handle error
      if (widget.onError != null) {
        widget.onError!(widget.destination, e);
      }

      // Execute JSON callback if defined
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onError')) {
        _executeJsonCallback('onError', {
          'destination': widget.destination,
          'error': e.toString(),
        });
      }

      if (widget.showSnackbarOnError && mounted) {
        _showErrorSnackbar(e);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRedirecting = false;
          _isLoading = false;
        });
      }
    }
  }

  Future<bool> _showConfirmationDialog() async {
    final title = widget.confirmationTitle ?? 'Confirm Navigation';
    final message =
        widget.confirmationMessage ??
        'Are you sure you want to navigate to ${widget.destination}?';

    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Continue'),
              ),
            ],
          ),
    );

    return result ?? false;
  }

  void _showErrorSnackbar(dynamic error) {
    final message =
        widget.errorMessage ?? 'Could not navigate to: ${widget.destination}';

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    });
  }

  void _trackRedirection() {
    // In a real app, this would integrate with an analytics service
    debugPrint(
      'Analytics: Redirection to ${widget.destination} (${widget.redirectionType})',
    );
  }

  Future<bool> _handleInternalNavigation() async {
    // This would be implemented to handle internal navigation using Navigator
    // For now, we'll just print a debug message
    debugPrint('Internal navigation to: ${widget.destination}');
    return true;
  }

  Future<bool> _handleExternalUrl() async {
    final Uri uri = Uri.parse(widget.destination);

    try {
      LaunchMode mode;
      switch (widget.externalLaunchMode) {
        case ExternalLaunchMode.platformDefault:
          mode = LaunchMode.platformDefault;
          break;
        case ExternalLaunchMode.externalApplication:
          mode = LaunchMode.externalApplication;
          break;
        case ExternalLaunchMode.inAppWebView:
          mode = LaunchMode.inAppWebView;
          break;
      }

      final success = await launchUrl(
        uri,
        mode: mode,
        webViewConfiguration: WebViewConfiguration(
          enableJavaScript: true,
          enableDomStorage: true,
          headers: <String, String>{'Referer': 'app'},
        ),
      );

      return success;
    } catch (e) {
      debugPrint('Could not launch $uri: $e');
      rethrow;
    }
  }

  Future<bool> _handleEmailLink() async {
    String emailUrl = widget.destination;

    // Add mailto: prefix if not present
    if (!emailUrl.startsWith('mailto:')) {
      emailUrl = 'mailto:$emailUrl';
    }

    // Add parameters if provided
    if (widget.parameters != null && widget.parameters!.isNotEmpty) {
      final buffer = StringBuffer(emailUrl);

      // Add ? if not already present
      if (!emailUrl.contains('?')) {
        buffer.write('?');
      }

      // Add subject if provided
      if (widget.parameters!.containsKey('subject')) {
        buffer.write(
          'subject=${Uri.encodeComponent(widget.parameters!['subject'])}',
        );
      }

      // Add body if provided
      if (widget.parameters!.containsKey('body')) {
        if (buffer.toString().endsWith('?')) {
          buffer.write(
            'body=${Uri.encodeComponent(widget.parameters!['body'])}',
          );
        } else {
          buffer.write(
            '&body=${Uri.encodeComponent(widget.parameters!['body'])}',
          );
        }
      }

      // Add cc if provided
      if (widget.parameters!.containsKey('cc')) {
        if (buffer.toString().endsWith('?')) {
          buffer.write('cc=${Uri.encodeComponent(widget.parameters!['cc'])}');
        } else {
          buffer.write('&cc=${Uri.encodeComponent(widget.parameters!['cc'])}');
        }
      }

      // Add bcc if provided
      if (widget.parameters!.containsKey('bcc')) {
        if (buffer.toString().endsWith('?')) {
          buffer.write('bcc=${Uri.encodeComponent(widget.parameters!['bcc'])}');
        } else {
          buffer.write(
            '&bcc=${Uri.encodeComponent(widget.parameters!['bcc'])}',
          );
        }
      }

      emailUrl = buffer.toString();
    }

    final Uri uri = Uri.parse(emailUrl);

    try {
      final success = await launchUrl(uri);
      return success;
    } catch (e) {
      debugPrint('Could not launch $uri: $e');
      rethrow;
    }
  }

  Future<bool> _handlePhoneLink() async {
    String phoneUrl = widget.destination;

    // Add tel: prefix if not present
    if (!phoneUrl.startsWith('tel:')) {
      phoneUrl = 'tel:$phoneUrl';
    }

    final Uri uri = Uri.parse(phoneUrl);

    try {
      final success = await launchUrl(uri);
      return success;
    } catch (e) {
      debugPrint('Could not launch $uri: $e');
      rethrow;
    }
  }

  Future<bool> _handleSmsLink() async {
    String smsUrl = widget.destination;

    // Add sms: prefix if not present
    if (!smsUrl.startsWith('sms:')) {
      smsUrl = 'sms:$smsUrl';
    }

    // Add parameters if provided
    if (widget.parameters != null && widget.parameters!.containsKey('body')) {
      smsUrl =
          '$smsUrl?body=${Uri.encodeComponent(widget.parameters!['body'])}';
    }

    final Uri uri = Uri.parse(smsUrl);

    try {
      final success = await launchUrl(uri);
      return success;
    } catch (e) {
      debugPrint('Could not launch $uri: $e');
      rethrow;
    }
  }

  Future<bool> _handleFileLink() async {
    String fileUrl = widget.destination;

    // Add file: prefix if not present
    if (!fileUrl.startsWith('file:')) {
      fileUrl = 'file://$fileUrl';
    }

    final Uri uri = Uri.parse(fileUrl);

    try {
      final success = await launchUrl(uri);
      return success;
    } catch (e) {
      debugPrint('Could not launch $uri: $e');
      rethrow;
    }
  }

  Future<bool> _handleCustomScheme() async {
    final Uri uri = Uri.parse(widget.destination);

    try {
      final success = await launchUrl(uri);
      return success;
    } catch (e) {
      debugPrint('Could not launch $uri: $e');
      rethrow;
    }
  }
}
