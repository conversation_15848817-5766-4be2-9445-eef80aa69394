// Custom ExpansionTile that separates title tap from expansion toggle
import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';

class CustomExpansionTileNew extends StatefulWidget {
  final Widget title;
  final List<Widget> children;
  final bool initiallyExpanded;
  final Function(bool) onExpansionChanged;
  final VoidCallback onTitleTap;
  final VoidCallback? onThreeDotsPressed;
  final Color backgroundColor;
  final bool showArrow;
  final bool showThreeDots;

  const CustomExpansionTileNew({
    super.key,
    required this.title,
    required this.children,
    this.initiallyExpanded = false,
    required this.onExpansionChanged,
    required this.onTitleTap,
    this.onThreeDotsPressed,
    this.backgroundColor = Colors.white,
    this.showArrow = true,
    this.showThreeDots =true,
  });

  @override
  State<CustomExpansionTileNew> createState() => CustomExpansionTileNewState();
}

class CustomExpansionTileNewState extends State<CustomExpansionTileNew>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightFactor;
  late Animation<double> _iconTurn;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _heightFactor = _controller.drive(CurveTween(curve: Curves.easeIn));
    _iconTurn = _controller.drive(Tween<double>(begin: 0.0, end: 0.5)
        .chain(CurveTween(curve: Curves.easeIn)));
    _isExpanded = widget.initiallyExpanded;
    if (_isExpanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(CustomExpansionTileNew oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initiallyExpanded != oldWidget.initiallyExpanded) {
      if (widget.initiallyExpanded) {
        _isExpanded = true;
        _controller.forward();
      } else {
        _isExpanded = false;
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    // Only allow expansion if arrow is shown
    if (!widget.showArrow) return;
    
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
      widget.onExpansionChanged(_isExpanded);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Title row with conditional tap handlers
        InkWell(
          onTap: () {
            // Always call onTitleTap
            widget.onTitleTap();
            // Only toggle expansion if arrow is shown
            if (widget.showArrow) {
              _toggleExpansion();
            }
          },
          child: Container(
            color: Colors
                .transparent, // Make it transparent to let parent color show through
            padding: EdgeInsets.symmetric(horizontal: AppSpacing.xs,),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: widget.title),
                // Conditionally show arrow icon
                if (widget.showArrow)
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 7.0),
                    child: HoverArrowIcon(
                      onTap: _toggleExpansion,
                      iconTurn: _iconTurn,
                    ),
                  ),
                  // HoverArrowIcon(
                  //   onTap: _toggleExpansion,
                  //   iconTurn: _iconTurn,
                  // ),
                // Always show three dots icon
                widget.showThreeDots? HoverThreeDotsIcon(
                  onTap: widget.onThreeDotsPressed ?? () {},
                ):SizedBox(),
              ],
            ),
          ),
        ),
        // Expandable content
        AnimatedBuilder(
          animation: _controller.view,
          builder: (context, child) {
            return ClipRect(
              child: Align(
                heightFactor: _heightFactor.value,
                alignment:
                    Alignment.topLeft, // Align content to the start (left)
                // Align content to the start (left)
                child: child,
              ),
            );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: widget.children,
          ),
        ),
      ],
    );
  }
}

class HoverArrowIcon extends StatefulWidget {
  final VoidCallback onTap;
  final Animation<double> iconTurn;

  const HoverArrowIcon({super.key, 
    required this.onTap,
    required this.iconTurn,
  });

  @override
  State<HoverArrowIcon> createState() => _HoverArrowIconState();
}

class _HoverArrowIconState extends State<HoverArrowIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: RotationTransition(
            turns: widget.iconTurn,
            child: Icon(
              Icons.keyboard_arrow_down,
              size: 20,
              color: isHovered ? Color(0xff0058FF) : Colors.grey.shade800,
            ),
          ),
        ),
      ),
    );
  }
}

class HoverThreeDotsIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverThreeDotsIcon({super.key, 
    required this.onTap,
  });

  @override
  State<HoverThreeDotsIcon> createState() => _HoverThreeDotsIconState();
}

class _HoverThreeDotsIconState extends State<HoverThreeDotsIcon> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Icon(
            Icons.more_vert,
            size: 20,
            color: isHovered ? Color(0xff0058FF) : Colors.grey.shade800,
          ),
        ),
      ),
    );
  }
}
