// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:intl/intl.dart' as intl;
// import 'dart:async';
// import 'dart:convert';
// import 'package:ui_controls_library/utils/callback_interpreter.dart';

// /// Format options for the date display
// enum DateFormatOption {
//   /// Standard format (e.g., "Jan 1, 2023")
//   standard,

//   /// Short format (e.g., "01/01/2023")
//   short,

//   /// Long format (e.g., "January 1, 2023")
//   long,

//   /// ISO format (e.g., "2023-01-01")
//   iso,

//   /// Custom format (specified by formatPattern)
//   custom
// }

// /// A comprehensive widget for displaying and selecting dates with overlay calendar.
// ///
// /// This widget provides extensive customization options for displaying
// /// and selecting date values with various formats and styles.
// class DateWidget extends StatefulWidget {
//   /// The initial date value
//   final DateTime? initialDate;

//   /// Whether to allow selecting a date
//   final bool allowDateSelection;

//   /// Format of the date display
//   final DateFormatOption format;

//   /// Custom format pattern (used when format is DateFormatOption.custom)
//   final String? formatPattern;

//   /// Whether to show the weekday
//   final bool showWeekday;

//   /// Whether to show the year
//   final bool showYear;

//   /// Whether to show the month
//   final bool showMonth;

//   /// Whether to show the day
//   final bool showDay;

//   /// Whether to automatically update the date (for current date)
//   final bool autoUpdate;

//   /// Interval in seconds for auto-update
//   final int updateIntervalSeconds;

//   /// Locale for date formatting (e.g., 'en_US', 'fr_FR')
//   final String locale;

//   /// Custom text style
//   final TextStyle? textStyle;

//   /// Text color
//   final Color textColor;

//   /// Background color
//   final Color backgroundColor;

//   /// Font size
//   final double fontSize;

//   /// Font weight
//   final FontWeight fontWeight;

//   /// Font family
//   final String? fontFamily;

//   /// Text alignment
//   final TextAlign textAlign;

//   /// Whether to show a border
//   final bool hasBorder;

//   /// Border radius
//   final double borderRadius;

//   /// Border color
//   final Color borderColor;

//   /// Border width
//   final double borderWidth;

//   /// Whether to show a shadow
//   final bool hasShadow;

//   /// Shadow elevation
//   final double elevation;

//   /// Whether to use a compact layout
//   final bool isCompact;

//   /// Optional label text
//   final String? label;

//   /// Optional prefix text
//   final String? prefix;

//   /// Optional suffix text
//   final String? suffix;

//   /// Optional prefix icon
//   final IconData? prefixIcon;

//   /// Optional suffix icon
//   final IconData? suffixIcon;

//   /// Optional prefix icon color
//   final Color? prefixIconColor;

//   /// Optional suffix icon color
//   final Color? suffixIconColor;

//   /// Whether to show a calendar icon
//   final bool showCalendarIcon;

//   /// Calendar icon
//   final IconData calendarIcon;

//   /// Calendar icon color
//   final Color? calendarIconColor;

//   /// Whether to use dark theme
//   final bool isDarkTheme;

//   /// Whether the widget is enabled
//   final bool enabled;

//   /// Whether the widget is read-only
//   final bool readOnly;

//   /// Minimum selectable date
//   final DateTime? minDate;

//   /// Maximum selectable date
//   final DateTime? maxDate;

//   /// Whether to show a clear button
//   final bool showClearButton;

//   /// Clear button icon
//   final IconData clearIcon;

//   /// Clear button icon color
//   final Color? clearIconColor;

//   /// Whether to show a tooltip
//   final bool showTooltip;

//   /// Tooltip text
//   final String? tooltipText;

//   /// Whether to show relative date (e.g., "Today", "Yesterday")
//   final bool isRelative;

//   /// Whether to animate the widget
//   final bool isAnimated;

//   /// Whether to use bold text
//   final bool isBold;

//   /// Whether to use italic text
//   final bool isItalic;

//   /// Whether to underline the text
//   final bool isUnderlined;

//   /// Whether to show a helper text
//   final bool showHelperText;

//   /// Helper text
//   final String? helperText;

//   /// Helper text color
//   final Color? helperTextColor;

//   /// Whether to show an error text
//   final bool showErrorText;

//   /// Error text
//   final String? errorText;

//   /// Error text color
//   final Color? errorTextColor;

//   /// Callback when date changes
//   final ValueChanged<DateTime>? onChanged;

//   /// Callback when date is selected
//   final ValueChanged<DateTime>? onSelected;

//   // Advanced interaction properties
//   /// Callback for mouse hover
//   final void Function(bool)? onHover;

//   /// Callback for keyboard focus
//   final void Function(bool)? onFocus;

//   /// Color when the widget is hovered
//   final Color? hoverColor;

//   /// Color when the widget is focused
//   final Color? focusColor;

//   /// Whether to enable haptic/audio feedback
//   final bool enableFeedback;

//   /// Callback for tap gesture
//   final VoidCallback? onTap;

//   /// Callback for double tap gesture
//   final VoidCallback? onDoubleTap;

//   /// Callback for long press gesture
//   final VoidCallback? onLongPress;

//   /// Focus node for controlling focus
//   final FocusNode? focusNode;

//   /// Whether the widget should autofocus
//   final bool autofocus;

//   // JSON callback properties
//   /// Dynamic callback definitions from JSON
//   final Map<String, dynamic>? jsonCallbacks;

//   /// Whether to use dynamic callbacks from JSON
//   final bool useJsonCallbacks;

//   /// State map for dynamic callbacks
//   final Map<String, dynamic>? callbackState;

//   /// Custom handlers for dynamic callbacks
//   final Map<String, Function>? customCallbackHandlers;

//   // Advanced date formatting options
//   /// Custom date formatter function
//   final String Function(DateTime)? customFormatter;

//   /// Map of special date formats for specific dates
//   final Map<String, String>? specialDateFormats;

//   /// JSON configuration for the widget
//   final Map<String, dynamic>? jsonConfig;

//   /// Creates a date widget.
//   const DateWidget({
//     super.key,
//     this.initialDate,
//     this.allowDateSelection = true,
//     this.format = DateFormatOption.standard,
//     this.formatPattern,
//     this.showWeekday = false,
//     this.showYear = true,
//     this.showMonth = true,
//     this.showDay = true,
//     this.autoUpdate = false,
//     this.updateIntervalSeconds = 1,
//     this.locale = 'en_US',
//     this.textStyle,
//     this.textColor = Colors.black,
//     this.backgroundColor = Colors.white,
//     this.fontSize = 16.0,
//     this.fontWeight = FontWeight.normal,
//     this.fontFamily,
//     this.textAlign = TextAlign.start,
//     this.hasBorder = true,
//     this.borderRadius = 4.0,
//     this.borderColor = const Color(0xFFE0E0E0),
//     this.borderWidth = 1.0,
//     this.hasShadow = false,
//     this.elevation = 2.0,
//     this.isCompact = false,
//     this.label,
//     this.prefix,
//     this.suffix,
//     this.prefixIcon,
//     this.suffixIcon,
//     this.prefixIconColor,
//     this.suffixIconColor,
//     this.showCalendarIcon = true,
//     this.calendarIcon = Icons.calendar_today,
//     this.calendarIconColor,
//     this.isDarkTheme = false,
//     this.enabled = true,
//     this.readOnly = false,
//     this.minDate,
//     this.maxDate,
//     this.showClearButton = true,
//     this.clearIcon = Icons.clear,
//     this.clearIconColor,
//     this.showTooltip = false,
//     this.tooltipText,
//     this.isRelative = false,
//     this.isAnimated = false,
//     this.isBold = false,
//     this.isItalic = false,
//     this.isUnderlined = false,
//     this.showHelperText = false,
//     this.helperText,
//     this.helperTextColor,
//     this.showErrorText = false,
//     this.errorText,
//     this.errorTextColor = Colors.red,
//     this.onChanged,
//     this.onSelected,
//     // Advanced interaction properties
//     this.onHover,
//     this.onFocus,
//     this.hoverColor = const Color(0xFFFF6B9D),
//     this.focusColor = const Color(0xFFFF6B9D),
//     this.enableFeedback = true,
//     this.onTap,
//     this.onDoubleTap,
//     this.onLongPress,
//     this.focusNode,
//     this.autofocus = false,
//     // JSON callback properties
//     this.jsonCallbacks,
//     this.useJsonCallbacks = false,
//     this.callbackState,
//     this.customCallbackHandlers,
//     // Advanced date formatting options
//     this.customFormatter,
//     this.specialDateFormats,
//     this.jsonConfig,
//   });

//   /// Creates a DateWidget from a JSON map
//   factory DateWidget.fromJson(Map<String, dynamic> json) {
//     // Parse date format
//     DateFormatOption format = DateFormatOption.standard;
//     if (json['format'] != null) {
//       switch (json['format'].toString().toLowerCase()) {
//         case 'short':
//           format = DateFormatOption.short;
//           break;
//         case 'long':
//           format = DateFormatOption.long;
//           break;
//         case 'iso':
//           format = DateFormatOption.iso;
//           break;
//         case 'custom':
//           format = DateFormatOption.custom;
//           break;
//         case 'standard':
//         default:
//           format = DateFormatOption.standard;
//           break;
//       }
//     }

//     // Parse text alignment
//     TextAlign textAlign = TextAlign.start;
//     if (json['textAlign'] != null) {
//       switch (json['textAlign'].toString().toLowerCase()) {
//         case 'center':
//           textAlign = TextAlign.center;
//           break;
//         case 'left':
//         case 'start':
//           textAlign = TextAlign.left;
//           break;
//         case 'right':
//         case 'end':
//           textAlign = TextAlign.right;
//           break;
//         case 'justify':
//           textAlign = TextAlign.justify;
//           break;
//       }
//     }

//     // Parse font weight
//     FontWeight fontWeight = FontWeight.normal;
//     if (json['fontWeight'] != null) {
//       if (json['fontWeight'] == 'bold' || json['fontWeight'] == true) {
//         fontWeight = FontWeight.bold;
//       } else if (json['fontWeight'] == 'light') {
//         fontWeight = FontWeight.w300;
//       }
//     }

//     // Parse colors
//     Color? textColor;
//     if (json['textColor'] != null) {
//       textColor = _colorFromJson(json['textColor']);
//     }

//     Color? backgroundColor;
//     if (json['backgroundColor'] != null) {
//       backgroundColor = _colorFromJson(json['backgroundColor']);
//     }

//     Color? borderColor;
//     if (json['borderColor'] != null) {
//       borderColor = _colorFromJson(json['borderColor']);
//     }

//     Color? hoverColor;
//     if (json['hoverColor'] != null) {
//       hoverColor = _colorFromJson(json['hoverColor']);
//     }

//     Color? focusColor;
//     if (json['focusColor'] != null) {
//       focusColor = _colorFromJson(json['focusColor']);
//     }

//     return DateWidget(
//       allowDateSelection: json['allowDateSelection'] as bool? ?? true,
//       format: format,
//       formatPattern: json['formatPattern'] as String?,
//       showWeekday: json['showWeekday'] as bool? ?? false,
//       showYear: json['showYear'] as bool? ?? true,
//       showMonth: json['showMonth'] as bool? ?? true,
//       showDay: json['showDay'] as bool? ?? true,
//       autoUpdate: json['autoUpdate'] as bool? ?? false,
//       updateIntervalSeconds: json['updateIntervalSeconds'] as int? ?? 1,
//       locale: json['locale'] as String? ?? 'en_US',
//       textColor: textColor ?? Colors.black,
//       backgroundColor: backgroundColor ?? Colors.white,
//       fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
//       fontWeight: fontWeight,
//       fontFamily: json['fontFamily'] as String?,
//       textAlign: textAlign,
//       hasBorder: json['hasBorder'] as bool? ?? true,
//       borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
//       borderColor: borderColor ?? const Color(0xFFE0E0E0),
//       borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
//       hasShadow: json['hasShadow'] as bool? ?? false,
//       elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
//       isCompact: json['isCompact'] as bool? ?? false,
//       label: json['label'] as String?,
//       prefix: json['prefix'] as String?,
//       suffix: json['suffix'] as String?,
//       showCalendarIcon: json['showCalendarIcon'] as bool? ?? true,
//       isDarkTheme: json['isDarkTheme'] as bool? ?? false,
//       enabled: json['enabled'] as bool? ?? true,
//       readOnly: json['readOnly'] as bool? ?? false,
//       showClearButton: json['showClearButton'] as bool? ?? true,
//       showTooltip: json['showTooltip'] as bool? ?? false,
//       tooltipText: json['tooltipText'] as String?,
//       isRelative: json['isRelative'] as bool? ?? false,
//       isAnimated: json['isAnimated'] as bool? ?? false,
//       isBold: json['isBold'] as bool? ?? false,
//       isItalic: json['isItalic'] as bool? ?? false,
//       isUnderlined: json['isUnderlined'] as bool? ?? false,
//       showHelperText: json['showHelperText'] as bool? ?? false,
//       helperText: json['helperText'] as String?,
//       showErrorText: json['showErrorText'] as bool? ?? false,
//       errorText: json['errorText'] as String?,
//       enableFeedback: json['enableFeedback'] as bool? ?? true,
//       autofocus: json['autofocus'] as bool? ?? false,
//       hoverColor: hoverColor,
//       focusColor: focusColor,
//       useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
//       jsonConfig: json,
//     );
//   }

//   /// Converts a JSON color value to a Flutter Color
//   static Color? _colorFromJson(dynamic colorValue) {
//     if (colorValue == null) return null;

//     if (colorValue is String) {
//       // Handle hex strings like "#FF0000"
//       if (colorValue.startsWith('#')) {
//         String hexColor = colorValue.substring(1);

//         // Handle shorthand hex like #RGB
//         if (hexColor.length == 3) {
//           hexColor = hexColor.split('').map((c) => '$c$c').join('');
//         }

//         // Add alpha channel if missing
//         if (hexColor.length == 6) {
//           hexColor = 'FF$hexColor';
//         }

//         // Parse the hex value
//         try {
//           return Color(int.parse('0x$hexColor'));
//         } catch (e) {
//           return null;
//         }
//       }

//       // Handle named colors
//       switch (colorValue.toLowerCase()) {
//         case 'red': return Colors.red;
//         case 'blue': return Colors.blue;
//         case 'green': return Colors.green;
//         case 'yellow': return Colors.yellow;
//         case 'orange': return Colors.orange;
//         case 'purple': return Colors.purple;
//         case 'pink': return Colors.pink;
//         case 'brown': return Colors.brown;
//         case 'grey':
//         case 'gray': return Colors.grey;
//         case 'black': return Colors.black;
//         case 'white': return Colors.white;
//         default: return null;
//       }
//     } else if (colorValue is int) {
//       return Color(colorValue);
//     }

//     return null;
//   }

//   @override
//   State<DateWidget> createState() => _DateWidgetState();
// }

// class _DateWidgetState extends State<DateWidget> {
//   late DateTime _selectedDate;
//   String _formattedDate = '';

//   // State for hover and focus
//   bool _isHovered = false;
//   bool _hasFocus = false;

//   // State for overlay calendar
//   bool _showCalendar = false;
//   late DateTime _displayedMonth;
//   OverlayEntry? _overlayEntry;

//   @override
//   void initState() {
//     super.initState();
//     _selectedDate = widget.initialDate ?? DateTime.now();
//     _displayedMonth = DateTime(_selectedDate.year, _selectedDate.month);
//     _updateFormattedDate();
//   }

//   @override
//   void dispose() {
//     _hideCalendar();
//     super.dispose();
//   }

//   void _updateFormattedDate() {
//     if (_selectedDate != null) {
//       _formattedDate = intl.DateFormat('dd/MM/yyyy').format(_selectedDate);
//     } else {
//       _formattedDate = 'DD/MM/YYYY';
//     }
//   }

//   /// Handles hover state changes
//   void _onHoverChange(bool isHovered) {
//     setState(() {
//       _isHovered = isHovered;
//       if (widget.onHover != null) {
//         widget.onHover!(isHovered);
//       }
//     });
//   }

//   /// Handles focus state changes
//   void _onFocusChange(bool hasFocus) {
//     setState(() {
//       _hasFocus = hasFocus;
//       if (widget.onFocus != null) {
//         widget.onFocus!(hasFocus);
//       }
//     });
//   }

//   void _toggleCalendar() {
//     if (!widget.allowDateSelection || widget.readOnly || !widget.enabled) {
//       return;
//     }

//     if (_showCalendar) {
//       _hideCalendar();
//     } else {
//       _showCalendarOverlay();
//     }
//   }

//   void _showCalendarOverlay() {
//     final RenderBox renderBox = context.findRenderObject() as RenderBox;
//     final Offset offset = renderBox.localToGlobal(Offset.zero);
//     final Size size = renderBox.size;

//     _overlayEntry = OverlayEntry(
//       builder: (context) => Stack(
//         children: [
//           // Transparent barrier to detect taps outside
//           Positioned.fill(
//             child: GestureDetector(
//               onTap: _hideCalendar,
//               child: Container(
//                 color: Colors.transparent,
//               ),
//           ),
//         ),
//       ),
//     );

//     // Add tooltip if needed
//     if (widget.showTooltip && widget.tooltipText != null) {
//       content = Tooltip(
//         message: widget.tooltipText!,
//         child: content,
//       );
//     }

//     // Build the main widget structure - no overlay in build method
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         content,
//         if (widget.showHelperText && widget.helperText != null) ...[ 
//           const SizedBox(height: 4),
//           Text(
//             widget.helperText!,
//             style: TextStyle(
//               color: widget.helperTextColor ?? effectiveTextColor.withOpacity(0.5),
//               fontSize: widget.fontSize * 0.7,
//             ),
//           ),
//         ],
//         if (widget.showErrorText && widget.errorText != null) ...[ 
//           const SizedBox(height: 4),
//           Text(
//             widget.errorText!,
//             style: TextStyle(
//               color: widget.errorTextColor,
//               fontSize: widget.fontSize * 0.7,
//             ),
//           ),
//         ],
//       ],
//     );
//   }
// }
//             ),
//           ),
//           // Calendar positioned below the input field
//           Positioned(
//             left: offset.dx,
//             top: offset.dy + size.height + 4,
//             width: size.width,
//             child: Material(
//               elevation: 8,
//               borderRadius: BorderRadius.circular(8),
//               child: _buildInlineCalendar(),
//             ),
//           ),
//         ],
//       ),
//     );

//     Overlay.of(context).insert(_overlayEntry!);
//     setState(() {
//       _showCalendar = true;
//       _displayedMonth = DateTime(_selectedDate.year, _selectedDate.month);
//     });
//   }

//   void _hideCalendar() {
//     _overlayEntry?.remove();
//     _overlayEntry = null;
//     setState(() {
//       _showCalendar = false;
//     });
//   }

//   void _selectDate(DateTime date) {
//     setState(() {
//       _selectedDate = date;
//       _updateFormattedDate();
//     });

//     _hideCalendar();

//     if (widget.onChanged != null) {
//       widget.onChanged!(_selectedDate);
//     }

//     if (widget.onSelected != null) {
//       widget.onSelected!(_selectedDate);
//     }

//     if (widget.onTap != null) {
//       widget.onTap!();
//     }
//   }

//   void _previousMonth() {
//     setState(() {
//       _displayedMonth = DateTime(_displayedMonth.year, _displayedMonth.month - 1);
//     });
//   }

//   void _nextMonth() {
//     setState(() {
//       _displayedMonth = DateTime(_displayedMonth.year, _displayedMonth.month + 1);
//     });
//   }

//   void _cancelSelection() {
//     _hideCalendar();
//   }

//   void _confirmSelection() {
//     _hideCalendar();
//   }

//   Widget _buildInlineCalendar() {
//     return Container(
//       margin: const EdgeInsets.only(top: 4),
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(8),
//         border: Border.all(color: const Color(0xFFE0E0E0)),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.1),
//             blurRadius: 8,
//             offset: const Offset(0, 2),
//           ),
//         ],
//       ),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           // Month/Year header with navigation
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               IconButton(
//                 onPressed: _previousMonth,
//                 icon: const Icon(Icons.chevron_left),
//                 iconSize: 20,
//               ),
//               Text(
//                 intl.DateFormat('MMM yyyy').format(_displayedMonth),
//                 style: const TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.w500,
//                 ),
//               ),
//               IconButton(
//                 onPressed: _nextMonth,
//                 icon: const Icon(Icons.chevron_right),
//                 iconSize: 20,
//               ),
//             ],
//           ),
//           const SizedBox(height: 16),
          
//           // Weekday headers
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceAround,
//             children: ['S', 'M', 'T', 'W', 'T', 'F', 'S']
//                 .map((day) => SizedBox(
//                       width: 32,
//                       child: Text(
//                         day,
//                         textAlign: TextAlign.center,
//                         style: TextStyle(
//                           fontSize: 12,
//                           fontWeight: FontWeight.w500,
//                           color: Colors.grey[600],
//                         ),
//                       ),
//                     ))
//                 .toList(),
//           ),
//           const SizedBox(height: 8),
          
//           // Calendar grid
//           _buildCalendarGrid(),
          
//           const SizedBox(height: 16),
          
//           // Action buttons
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               IconButton(
//                 onPressed: _cancelSelection,
//                 icon: const Icon(Icons.close, color: Colors.red),
//                 iconSize: 24,
//               ),
//               IconButton(
//                 onPressed: _confirmSelection,
//                 icon: const Icon(Icons.check, color: Colors.green),
//                 iconSize: 24,
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildCalendarGrid() {
//     final firstDayOfMonth = DateTime(_displayedMonth.year, _displayedMonth.month, 1);
//     final lastDayOfMonth = DateTime(_displayedMonth.year, _displayedMonth.month + 1, 0);
//     final firstDayWeekday = firstDayOfMonth.weekday % 7; // Sunday = 0
    
//     List<Widget> dayWidgets = [];
    
//     // Add empty cells for days before the first day of the month
//     for (int i = 0; i < firstDayWeekday; i++) {
//       dayWidgets.add(const SizedBox(width: 32, height: 32));
//     }
    
//     // Add day cells
//     for (int day = 1; day <= lastDayOfMonth.day; day++) {
//       final date = DateTime(_displayedMonth.year, _displayedMonth.month, day);
//       final isSelected = date.year == _selectedDate.year &&
//           date.month == _selectedDate.month &&
//           date.day == _selectedDate.day;
//       final isToday = date.year == DateTime.now().year &&
//           date.month == DateTime.now().month &&
//           date.day == DateTime.now().day;
      
//       dayWidgets.add(
//         GestureDetector(
//           onTap: () => _selectDate(date),
//           child: Container(
//             width: 32,
//             height: 32,
//             decoration: BoxDecoration(
//               color: isSelected 
//                   ? const Color(0xFFFF6B9D)
//                   : isToday 
//                       ? const Color(0xFFFF6B9D).withOpacity(0.2)
//                       : Colors.transparent,
//               borderRadius: BorderRadius.circular(16),
//               border: isToday && !isSelected
//                   ? Border.all(color: const Color(0xFFFF6B9D))
//                   : null,
//             ),
//             child: Center(
//               child: Text(
//                 day.toString(),
//                 style: TextStyle(
//                   fontSize: 14,
//                   fontWeight: isSelected || isToday ? FontWeight.w600 : FontWeight.normal,
//                   color: isSelected 
//                       ? Colors.white
//                       : isToday 
//                           ? const Color(0xFFFF6B9D)
//                           : Colors.black,
//                 ),
//               ),
//             ),
//           ),
//         ),
//       );
//     }
    
//     // Arrange in grid
//     List<Widget> rows = [];
//     for (int i = 0; i < dayWidgets.length; i += 7) {
//       rows.add(
//         Padding(
//           padding: const EdgeInsets.symmetric(vertical: 2),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceAround,
//             children: dayWidgets.skip(i).take(7).toList(),
//           ),
//         ),
//       );
//     }
    
//     return Column(children: rows);
//   }

//   @override
//   Widget build(BuildContext context) {
//     // Apply dark theme if specified
//     final effectiveTextColor = widget.isDarkTheme ? Colors.white : widget.textColor;
//     final effectiveBackgroundColor = widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

//     // Create text style
//     TextStyle textStyle = widget.textStyle ??
//         TextStyle(
//           color: effectiveTextColor,
//           fontSize: widget.fontSize,
//           fontWeight: widget.isBold ? FontWeight.bold : widget.fontWeight,
//           fontStyle: widget.isItalic ? FontStyle.italic : FontStyle.normal,
//           fontFamily: widget.fontFamily,
//           decoration: widget.isUnderlined ? TextDecoration.underline : TextDecoration.none,
//         );

//     // Get responsive font size based on screen width
//     double getResponsiveTitleFontSize(double screenWidth) {
//       if (screenWidth > 1920) {
//         return 18.0; // Extra Large
//       } else if (screenWidth >= 1440) {
//         return 16.0; // Large
//       } else if (screenWidth >= 1280) {
//         return 14.0; // Medium
//       } else if (screenWidth >= 768) {
//         return 12.0; // Small
//       } else {
//         return 10.0; // Extra Small (fallback for very small screens)
//       }
//     }

//     final double screenWidth = MediaQuery.of(context).size.width;
//     final double responsiveTitleFontSize = getResponsiveTitleFontSize(screenWidth);
 
//     // Get responsive icon size based on screen width
//     double getResponsiveIconSize(double screenWidth) {
//       if (screenWidth > 1920) {
//         return 18.0; // Extra Large
//       } else if (screenWidth >= 1440) {
//         return 16.0; // Large
//       } else if (screenWidth >= 1280) {
//         return 14.0; // Medium
//       } else if (screenWidth >= 768) {
//         return 12.0; // Small
//       } else {
//         return 10.0; // Extra Small (fallback for very small screens)
//       }
//     }
//     final double responsiveIconSize = getResponsiveIconSize(screenWidth);

//     // Create the main content widget
//     Widget content = MouseRegion(
//       onEnter: (_) => _onHoverChange(true),
//       onExit: (_) => _onHoverChange(false),
//       cursor: SystemMouseCursors.click,
//       child: Focus(
//         focusNode: widget.focusNode,
//         autofocus: widget.autofocus,
//         onFocusChange: _onFocusChange,
//         child: GestureDetector(
//           onTap: _toggleCalendar,
//           onDoubleTap: widget.onDoubleTap,
//           onLongPress: widget.onLongPress,
//           child: AnimatedContainer(
//             duration: const Duration(milliseconds: 200),
//             padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
//             decoration: BoxDecoration(
//               color: effectiveBackgroundColor,
//               borderRadius: BorderRadius.circular(widget.borderRadius),
//               border: widget.hasBorder
//                   ? Border.all(
//                       color: _isHovered
//                           ? (widget.hoverColor ?? const Color(0xFFFF6B9D))
//                           : _hasFocus
//                               ? (widget.focusColor ?? const Color(0xFFFF6B9D))
//                               : widget.borderColor,
//                       width: widget.borderWidth,
//                     )
//                   : null,
//               boxShadow: widget.hasShadow
//                   ? [
//                       BoxShadow(
//                         color: Colors.black.withOpacity(0.2),
//                         blurRadius: widget.elevation,
//                         offset: Offset(0, widget.elevation / 2),
//                       ),
//                     ]
//                   : null,
//             ),
//             child: Row(
//               children: [
//                 if (widget.prefix != null) ...[ 
//                   Text(
//                     widget.prefix!,
//                     style: TextStyle(
//                       color: effectiveTextColor.withOpacity(0.7),
//                       fontSize: widget.fontSize,
//                     ),
//                   ),
//                   const SizedBox(width: 8),
//                 ],
//                 Expanded(
//                   child: Text(
//                     _formattedDate,
//                     style: textStyle.copyWith(
//                       color: effectiveTextColor.withOpacity(0.6),
//                       fontSize: responsiveTitleFontSize,
//                     ),
//                     textAlign: TextAlign.left,
//                   ),
//                 ),
//                 if (widget.suffix != null) ...[ 
//                   const SizedBox(width: 8),
//                   Text(
//                     widget.suffix!,
//                     style: TextStyle(
//                       color: effectiveTextColor.withOpacity(0.7),
//                       fontSize: widget.fontSize,
//                     ),
//                   ),
//                 ],
//                 if (widget.showCalendarIcon) ...[ 
//                   const SizedBox(width: 8),
//                   SvgPicture.asset( 
//                     _isHovered
//                       ? 'assets/images/icon-date-hover.svg'
//                       : 'assets/images/icon-date.svg',
//                       package: 'ui_controls_library',
//                       width: responsiveIconSize,
//                     ),
//                 ],
//               ],
//             ),
