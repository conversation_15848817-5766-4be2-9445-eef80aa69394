import 'dart:convert';
import 'dart:math' as math;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/models/books/books_model.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/books_provider.dart';
import 'package:nsl/screens/new_design/my_business/module.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class MyBusinessCollection extends StatefulWidget {
  final String screenType; // 'collections' or 'records'

  const MyBusinessCollection({
    super.key,
    this.screenType = 'collections',
  });

  @override
  State<MyBusinessCollection> createState() => _MyBusinessCollectionState();
}

final List<String> bannerImages = [
  'assets/images/my_business/collections/my_business_carousel_one.jpg',
  'assets/images/my_business/collections/my_business_carousel_two.jpg',
  'assets/images/my_business/collections/my_business_carousel_three.jpg',
];
final CarouselSliderController carouselController = CarouselSliderController();
final TextEditingController _searchController = TextEditingController();


int _currentPage = 0;
bool _showSearchBar = false;
bool _isLoading = false;
int itemsPerPage = 10;
int _currentCollectionPage = 1;
int cardsPerRow = 2;
int rowsPerPage = 2;
int cardsPerPage = cardsPerRow * rowsPerPage;

// Collections data - will be loaded dynamically
List<BooksModel> _allCollections = [];

// Filtered collections - this will be updated based on search
List<BooksModel> _filteredCollections = [];
// Get current page items
List<BooksModel> _getCurrentPageItems() {
  if (_filteredCollections.isEmpty) return [];

  final startIndex = (_currentCollectionPage - 1) * itemsPerPage;
  final endIndex = startIndex + itemsPerPage;

  if (startIndex >= _filteredCollections.length) return [];

  return _filteredCollections.sublist(
      startIndex,
      endIndex > _filteredCollections.length
          ? _filteredCollections.length
          : endIndex);
}

class _MyBusinessCollectionState extends State<MyBusinessCollection> {
  late PageController _pageController;
  @override
  void initState() {
    super.initState();
    // Load collections data using the method from web_transaction_collection
    _loadCollectionsData();
    _pageController = PageController();
    // Add listener to search controller
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _pageController.dispose();
    super.dispose();
  }

  /// Load data based on screenType - collections or records
  Future<void> _loadCollectionsData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.screenType == 'collections') {
        // Load collections data from Books API
        try {
          final booksProvider = BooksProvider();
          await booksProvider.fetchBooks();

          if (booksProvider.books.isNotEmpty) {
            setState(() {
              _allCollections = booksProvider.books;
              _filteredCollections = List.from(_allCollections);
              _isLoading = false;
            });
            return;
          }
        } catch (apiError) {
          debugPrint('Error loading from Books API: $apiError');
        }

        // Fallback collections data
        final List<BooksModel> collectionsData = [
          BooksModel(
            bookId: "book_1",
            bookName: "Procurement Management",
            objectiveCount: 5,
            image:
                "assets/images/my_business/collections/collection_procurement_process.png",
          ),
          BooksModel(
            bookId: "book_2",
            bookName: "Leave Management",
            objectiveCount: 3,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "book_3",
            bookName: "Employee Onboarding",
            objectiveCount: 4,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "book_4",
            bookName: "Performance Review",
            objectiveCount: 6,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "book_5",
            bookName: "Training Management",
            objectiveCount: 3,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "book_4",
            bookName: "Performance Review",
            objectiveCount: 6,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "book_5",
            bookName: "Training Management",
            objectiveCount: 3,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "book_4",
            bookName: "Performance Review",
            objectiveCount: 6,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "book_5",
            bookName: "Training Management",
            objectiveCount: 3,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
        ];

        setState(() {
          _allCollections = collectionsData;
          _filteredCollections = List.from(_allCollections);
          _isLoading = false;
        });
      } else if (widget.screenType == 'records') {
        // Load records data (different data set)
        final List<BooksModel> recordsData = [
          BooksModel(
            bookId: "record_1",
            bookName: "My Leave Records",
            objectiveCount: 12,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "record_2",
            bookName: "Attendance Records",
            objectiveCount: 8,
            image:
                "assets/images/my_business/collections/collection_procurement_process.png",
          ),
          BooksModel(
            bookId: "record_3",
            bookName: "Training Records",
            objectiveCount: 15,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "record_4",
            bookName: "Performance Records",
            objectiveCount: 6,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "record_5",
            bookName: "Expense Records",
            objectiveCount: 20,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
          BooksModel(
            bookId: "record_6",
            bookName: "Project Records",
            objectiveCount: 9,
            image:
                "assets/images/my_business/collections/collection_leave_management.png",
          ),
        ];

        setState(() {
          _allCollections = recordsData;
          _filteredCollections = List.from(_allCollections);
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error in _loadCollectionsData: $e');
      setState(() {
        _allCollections = [];
        _filteredCollections = [];
        _isLoading = false;
      });
    }
  }

  void _onSearchChanged() {
    setState(() {
      if (_searchController.text.isEmpty) {
        // Show all collections when search is empty
        _filteredCollections = List.from(_allCollections);
      } else {
        // Filter collections based on search text
        _filteredCollections = _allCollections
            .where((collection) =>
                collection.bookName
                    ?.toLowerCase()
                    .contains(_searchController.text.toLowerCase()) ??
                false)
            .toList();
      }
    });
  }

  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        // Clear search when hiding search bar and reset to all collections
        _searchController.clear();
        _filteredCollections = List.from(_allCollections);
      }
    });
  }

  Widget _buildNoResultsWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            widget.screenType == 'collections'
                ? 'No collections found'
                : 'No records found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
              fontFamily: 'TiemposText',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try searching with different keywords',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
              fontFamily: 'TiemposText',
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = bottomInset > 0;
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Color(0xffF7F9FB),
      appBar: AppBar(
        leading: Builder(
          builder: (context) => IconButton(
            icon: Icon(Icons.menu),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          ),
        ),
        title: Text(''),
        backgroundColor: Color(0xffF7F9FB),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      drawer: CustomDrawer(),
      body: SafeArea(
          child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
        child: Column(
          children: [
            CarouselSlider.builder(
              itemCount: bannerImages.length,
              itemBuilder: (context, index, realIndex) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(AppSpacing.sm),
                  child: Image.asset(
                    bannerImages[index],
                    fit: BoxFit.cover,
                    width: double.infinity,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: double.infinity,
                        height: MediaQuery.of(context).size.height / 2,
                        color: Colors.grey[200],
                        child: Center(
                          child: Icon(
                            Icons.broken_image,
                            color: Colors.grey[400],
                            size: 48,
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
              options: CarouselOptions(
                height: MediaQuery.of(context).size.height / 5.5,
                viewportFraction: 1.0,
                autoPlay: true,
                autoPlayInterval: Duration(seconds: 5),
                onPageChanged: (index, reason) {
                  setState(() {
                    _currentPage = index;
                  });
                },
              ),
              // carouselController: _carouselController,
            ),
            const SizedBox(height: AppSpacing.xs),
            AnimatedSmoothIndicator(
              activeIndex: _currentPage,
              count: bannerImages.length,
              effect: SlideEffect(
                dotHeight: 8,
                dotWidth: 8,
                spacing: 10,
                activeDotColor: Color(0xff0058FF),
                dotColor: Colors.grey.shade300,
              ),
            ),

            SizedBox(height: AppSpacing.xs),
            _showSearchBar
                ? FullWidthSearchBar(
                    controller: _searchController,
                    onClose: _toggleSearchBar,
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        widget.screenType == 'collections'
                            ? AppLocalizations.of(context)
                                .translate('myBusinessCollections.collections')
                            : AppLocalizations.of(context)
                                .translate('myBusinessRecords.records'),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          fontFamily: 'TiemposText',
                        ),
                      ),
                      GestureDetector(
                        onTap: _toggleSearchBar,
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: Container(
                            margin: const EdgeInsets.only(right: AppSpacing.sm),
                            height: 36,
                            child: HoverableSearchIcon(),
                          ),
                        ),
                      ),
                    ],
                  ),
            SizedBox(height: AppSpacing.xs),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredCollections.isEmpty
                      ? _buildNoResultsWidget()
                      : PageView.builder(
                          itemCount:
                              (_filteredCollections.length / cardsPerPage)
                                  .ceil(),
                          controller: _pageController,
                          itemBuilder: (context, pageIndex) {
                            final int startIndex = pageIndex * cardsPerPage;
                            final int endIndex = (startIndex + cardsPerPage)
                                .clamp(0, _filteredCollections.length);
                            final currentItems = _filteredCollections.sublist(
                                startIndex, endIndex);

                            return SingleChildScrollView(
                              child: Column(
                                children:
                                    List.generate(rowsPerPage, (rowIndex) {
                                  final rowStartIndex = rowIndex * cardsPerRow;

                                  return Padding(
                                    padding: const EdgeInsets.only(
                                        bottom: AppSpacing.sm),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: List.generate(cardsPerRow,
                                          (colIndex) {
                                        final index = rowStartIndex + colIndex;
                                        if (index >= currentItems.length) {
                                          return const SizedBox(); // Fixed width placeholder
                                        }

                                        final book = currentItems[index];
                                        return SizedBox(
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width /
                                              2.3,
                                          height: () {
                                            final screenHeight =
                                                MediaQuery.of(context)
                                                    .size
                                                    .height;
                                            // Increased card height since we now have 2 rows instead of 3
                                            if (screenHeight <= 600)
                                              return screenHeight / 4;
                                            if (screenHeight <= 700)
                                              return screenHeight / 3.6;
                                            if (screenHeight <= 800)
                                              return screenHeight / 3.7;
                                            if (screenHeight <= 900)
                                              return screenHeight / 3.5;
                                            return screenHeight /
                                                3.4; // for larger screens
                                          }(),
                                          child: BookCard(
                                            book: book,
                                            collectionPage:
                                                _currentCollectionPage,
                                            screenType: widget.screenType,
                                          ),
                                        );
                                      }),
                                    ),
                                  );
                                }),
                              ),
                            );
                          },
                        ),
            ),
            // const SizedBox(height: AppSpacing.xxs),
            // Only show page indicator if there are collections to display
            if (_filteredCollections.isNotEmpty && !isKeyboardVisible)
              Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.xs),
                child: SmoothPageIndicator(
                  controller: _pageController,
                  count: math.max(
                      1, (_filteredCollections.length / cardsPerPage).ceil()),
                  effect: WormEffect(
                    dotHeight: 8,
                    dotWidth: 8,
                    spacing: 10,
                    activeDotColor: Color(0xff0058FF),
                    dotColor: Colors.grey.shade300,
                  ),
                ),
              ),
          ],
        ),
      )),
    );
  }
}

/// Full width search bar widget that appears when search icon is clicked
class FullWidthSearchBar extends StatefulWidget {
  final TextEditingController controller;
  final VoidCallback onClose;

  const FullWidthSearchBar({
    super.key,
    required this.controller,
    required this.onClose,
  });

  @override
  State<FullWidthSearchBar> createState() => _FullWidthSearchBarState();
}

class _FullWidthSearchBarState extends State<FullWidthSearchBar> {
  final FocusNode _focusNode = FocusNode();
  bool _isSearchFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _isSearchFocused = _focusNode.hasFocus;
      });
    });
    // Auto-focus when search bar is activated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius:
            BorderRadius.circular(AppSpacing.xs), // More rounded border
        border: Border.all(color: Colors.grey.shade300, width: 0.5),
      ),
      child: Row(
        children: [
          // Search input field
          Expanded(
            child: TextField(
              controller: widget.controller,
              focusNode: _focusNode,
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0),
                isDense: true,
                hintText: 'Search',
                hintStyle: TextStyle(
                  color: Color(0xffD0D0D0),
                  fontSize: 14,
                  fontFamily: 'TiemposText',
                ),
              ),
              style: const TextStyle(fontSize: 14, fontFamily: 'TiemposText'),
              // Handle keyboard submit action - only dismiss keyboard, keep search results
              onSubmitted: (value) {
                FocusScope.of(context).unfocus();
              },
            ),
          ),

          // Search/Close icon on the right
          GestureDetector(
            onTap: () {
              if (_isSearchFocused) {
                // When focused, close icon is shown - close search bar
                FocusScope.of(context).unfocus();
                widget.onClose();
              } else {
                // When not focused, search icon is shown - focus search field
                _focusNode.requestFocus();
              }
            },
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: _isSearchFocused
                    ? Icon(
                        Icons.close,
                        size: 20,
                        color: Colors.grey.shade600,
                      )
                    : CustomImage.asset(
                        'assets/images/my_business/search_collection.svg',
                        width: 20,
                        height: 20,
                        fit: BoxFit.contain,
                        color: Colors.grey.shade600,
                      ).toWidget(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HoverableSearchIcon extends StatefulWidget {
  const HoverableSearchIcon({super.key});

  @override
  HoverableSearchIconState createState() => HoverableSearchIconState();
}

class HoverableSearchIconState extends State<HoverableSearchIcon> {
  @override
  Widget build(BuildContext context) {
    return CustomImage.asset('assets/images/my_business/search_collection.svg',
            width: 20,
            height: 20,
            fit: BoxFit.contain,
            color: Colors.black // <-- Change color on hover
            )
        .toWidget();
  }
}

class BookCard extends StatefulWidget {
  final BooksModel book;
  final int collectionPage;
  final String screenType;

  const BookCard({
    super.key,
    required this.book,
    this.collectionPage = 1,
    required this.screenType,
  });

  @override
  State<BookCard> createState() => _BookCardState();
}

class _BookCardState extends State<BookCard> {
  final double horizontalPadding = 12.0;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        if (widget.screenType == 'collections') {
          // Navigate to module screen for collections
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CollectionModule(
                bookId: widget.book.bookId,
                bookName: widget.book.bookName,
              ),
            ),
          );
        } else if (widget.screenType == 'records') {
          // Handle records tap - show some feedback but don't navigate
          debugPrint('Record tapped: ${widget.book.bookName}');

          // You can add any custom behavior here for records
          // For example, show a snackbar, dialog, or perform some action
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Viewing record: ${widget.book.bookName}'),
              duration: Duration(seconds: 2),
            ),
          );

          // Or you could show a bottom sheet with record details
          // _showRecordDetails(context, widget.book);
        }
      },
      child: Card(
        margin: EdgeInsets.zero,
        elevation: 0,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.xs),
          side: BorderSide(
            color: Color(0xffD0D0D0), // Hover color change
            width: 0.5, // Thicker border for hover
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.sm),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                flex: 3,
                child: Container(
                  child: widget.book.image != null
                      ? CustomImage.asset(widget.book.image!)
                          .toWidget(fit: BoxFit.cover)
                      : CustomImage.asset(
                          "assets/images/my_business/collections/collection_procurement_process.png",
                        ).toWidget(fit: BoxFit.cover),
                ),
              ),
              SizedBox(
                height: AppSpacing.sm,
              ),
              Expanded(
                flex: 1,
                child: Text(
                  widget.book.bookName ?? 'Unknown Book',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'TiemposText',
                    height: 1.2,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.left,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
