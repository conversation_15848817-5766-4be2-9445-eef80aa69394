import '../models/workflow.dart';

/// Converter class to transform JSON data into WorkflowInputs
class JsonWorkflowConverter {
  /// Creates a WorkflowInputs object from JSON data
  static WorkflowInputs createWorkflowInputsFromJson(
      Map<String, dynamic> jsonData) {
    // Create lists to hold different types of input fields
    final List<InputField> allInputFields = [];
    final List<InputField> systemInputFields = [];
    final List<InputField> infoInputFields = [];
    final List<InputField> userInputFields = [];
    final List<InputField> dependentInputFields = [];

    // Process user inputs
    if (jsonData.containsKey('user_inputs') &&
        jsonData['user_inputs'] is List) {
      final userInputs = jsonData['user_inputs'] as List;
      for (final inputJson in userInputs) {
        final inputField = _createInputFieldFromJson(inputJson);
        allInputFields.add(inputField);
        userInputFields.add(inputField);
      }
    }

    // Process system inputs
    if (jsonData.containsKey('system_inputs') &&
        jsonData['system_inputs'] is List) {
      final systemInputs = jsonData['system_inputs'] as List;
      for (final inputJson in systemInputs) {
        final inputField = _createInputFieldFromJson(inputJson);
        allInputFields.add(inputField);
        systemInputFields.add(inputField);
      }
    }

    // Process info inputs
    if (jsonData.containsKey('info_inputs') &&
        jsonData['info_inputs'] is List) {
      final infoInputs = jsonData['info_inputs'] as List;
      for (final inputJson in infoInputs) {
        final inputField = _createInputFieldFromJson(inputJson);
        allInputFields.add(inputField);
        infoInputFields.add(inputField);
      }
    }

    // Process dependent inputs
    if (jsonData.containsKey('dependent_inputs') &&
        jsonData['dependent_inputs'] is List) {
      final dependentInputs = jsonData['dependent_inputs'] as List;
      for (final inputJson in dependentInputs) {
        final inputField = _createInputFieldFromJson(inputJson);
        allInputFields.add(inputField);
        dependentInputFields.add(inputField);
      }
    }

    // Create and return WorkflowInputs
    return WorkflowInputs(
      localObjective: jsonData['local_objective'] ?? 'Custom Workflow',
      userInputs: userInputFields,
      systemInputs: systemInputFields,
      infoInputs: infoInputFields,
      dependentInputs: dependentInputFields,
      // dependencies: jsonData['dependencies'] != null ?
      //   Map<String, List<String>>.from(jsonData['dependencies']) : {},
    );
  }

  /// Creates an InputField object from JSON data
  static InputField _createInputFieldFromJson(Map<String, dynamic> json) {
    // Create dropdown options if available
    List<DropdownOption>? dropdownOptions;
    if (json.containsKey('dropdown_options') &&
        json['dropdown_options'] is List) {
      dropdownOptions = (json['dropdown_options'] as List)
          .map((option) => DropdownOption(
                value: option['value']?.toString() ?? '',
                label: option['label']?.toString() ?? '',
              ))
          .toList();
    }

    // Parse dependencies
    List<String>? dependencies;
    if (json.containsKey('dependencies') && json['dependencies'] is List) {
      dependencies =
          (json['dependencies'] as List).map((dep) => dep.toString()).toList();
    }

    // Parse parent IDs
    List<String>? parentIds;
    if (json.containsKey('parent_ids') && json['parent_ids'] is List) {
      parentIds =
          (json['parent_ids'] as List).map((id) => id.toString()).toList();
    }

    // Create metadata
    final metadata = InputFieldMetadata(
      isInformational: json['metadata']?['is_informational'] == true,
    );

    // Create and return InputField
    return InputField(
      inputId: json['input_id'] ?? '',
      attributeId: json['attribute_id'] ?? '',
      entityId: json['entity_id'] ?? 'default-entity',
      displayName: json['display_name'] ?? '',
      dataType: json['data_type'] ?? '',
      sourceType: json['source_type'] ?? '',
      required: json['required'] == true,
      uiControl: json['ui_control'] ?? '',
      isVisible: json['is_visible'] == true,
      readOnly: json['read_only'] == true,
      allowedValues: json['allowed_values'] != null
          ? (json['allowed_values'] as List)
              .map((value) => value.toString())
              .toList()
          : null,
      validations: json['validations'] != null
          ? [Map<String, dynamic>.from(json['validations'])]
          : null,
      contextualId: json['contextual_id'] ?? '',
      inputValue: json['input_value'],
      hasDropdownSource: json['has_dropdown_source'] == true,
      dependencies: dependencies,
      dependencyType: json['dependency_type'],
      metadata: metadata,
      dropdownOptions: dropdownOptions,
      parentIds: parentIds,
    );
  }

  /// Converts an InputField to a JSON map
  static Map<String, dynamic> inputFieldToJson(InputField field) {
    // Create a map to hold the JSON data
    final Map<String, dynamic> json = {
      'input_id': field.inputId,
      'attribute_id': field.attributeId,
      'entity_id': field.entityId,
      'display_name': field.displayName,
      'data_type': field.dataType,
      'source_type': field.sourceType,
      'required': field.required,
      'ui_control': field.uiControl,
      'is_visible': field.isVisible,
      'contextual_id': field.contextualId,
      'input_value': field.inputValue,
      'has_dropdown_source': field.hasDropdownSource,
      'dependency_type': field.dependencyType,
    };

    // Add optional fields if they exist
    if (field.allowedValues != null) {
      json['allowed_values'] = field.allowedValues;
    }

    if (field.validations != null) {
      json['validations'] = field.validations;
    }

    if (field.dependencies != null) {
      json['dependencies'] = field.dependencies;
    }

    if (field.parentIds != null) {
      json['parent_ids'] = field.parentIds;
    }

    if (field.dropdownOptions != null) {
      json['dropdown_options'] = field.dropdownOptions!
          .map((option) => {
                'value': option.value,
                'label': option.label,
              })
          .toList();
    }

    // Add metadata
    json['metadata'] = {
      'is_informational': field.metadata.isInformational,
      'usage': field.metadata.usage,
      'has_dropdown_source': field.metadata.hasDropdownSource,
    };

    return json;
  }
}
