import 'package:flutter/material.dart';

class CheckboxWidget extends StatefulWidget {
  // Configurable properties
  final bool initialValue;
  final Color checkColor;
  final Color activeColor;
  final Color checkOutlineColor;
  final Function(bool)? onChanged; // Callback for checkbox state change
  final double size;
  final String? label;
  final String? displayName;
 
  final bool showLabel;
  final bool isDisabled;
  final bool isRounded;
  final double borderRadius;
  final double borderWidth;
  final TextAlign labelPosition;
  final EdgeInsetsGeometry padding;
  final bool isVerticalLayout;
  final bool? testValue; // For testing purposes only

  const CheckboxWidget({
    super.key,
    this.initialValue = false, // Default value is false
    this.checkColor = Colors.white, // Default checkmark color is white
    this.activeColor = Colors.blue, // Default active color is blue
    this.checkOutlineColor = Colors.black, // Default outline color is black
    this.onChanged, // Callback for state change
    this.size = 40.0, // Default size is 40
    this.label,
    this.displayName, // Optional label text
    this.showLabel = true, // Whether to show the label
    this.isDisabled = false, // Whether the checkbox is disabled
    this.isRounded = false, // Whether to use rounded corners
    this.borderRadius = 4.0, // Border radius for rounded corners
    this.borderWidth = 1.5, // Border width
    this.labelPosition =
        TextAlign.end, // Position of the label (start=left, end=right)
    this.padding = const EdgeInsets.all(8.0), // Padding around the widget
    this.isVerticalLayout =
        false, // Whether to arrange checkbox and label vertically
    this.testValue, // For testing purposes only
  });

  /// Creates a CheckboxWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the CheckboxWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialValue": true,
  ///   "checkColor": "white",
  ///   "activeColor": "blue",
  ///   "checkOutlineColor": "black",
  ///   "size": 40.0,
  ///   "label": "Accept Terms",
  ///   "showLabel": true,
  ///   "isDisabled": false,
  ///   "isRounded": true,
  ///   "borderRadius": 8.0,
  ///   "borderWidth": 1.5,
  ///   "labelPosition": "start",
  ///   "padding": 8.0,
  ///   "isVerticalLayout": false
  /// }
  /// ```
  factory CheckboxWidget.fromJson(Map<String, dynamic> json) {
    // Parse label position
    TextAlign labelPosition = TextAlign.end;
    if (json['labelPosition'] != null) {
      switch (json['labelPosition'].toString().toLowerCase()) {
        case 'start':
        case 'left':
        case 'leading':
          labelPosition = TextAlign.start;
          break;
        case 'end':
        case 'right':
        case 'trailing':
          labelPosition = TextAlign.end;
          break;
      }
    }

    // Parse padding
    EdgeInsetsGeometry padding = const EdgeInsets.all(8.0);
    if (json['padding'] != null) {
      if (json['padding'] is num) {
        double paddingValue = (json['padding'] as num).toDouble();
        padding = EdgeInsets.all(paddingValue);
      }
    }

    return CheckboxWidget(
      initialValue: json['initialValue'] ?? false,
      checkColor: _colorFromJson(json['checkColor']) ?? Colors.white,
      activeColor: _colorFromJson(json['activeColor']) ?? Color(0xFF0058FF),
      checkOutlineColor:
          _colorFromJson(json['checkOutlineColor']) ?? Colors.black,
      size: (json['size'] as num?)?.toDouble() ?? 40.0,
      label: json['label'] as String?,
      displayName: json['displayName'] as String?,
      showLabel: json['showLabel'] ?? true,
      isDisabled: json['isDisabled'] ?? false,
      isRounded: json['isRounded'] ?? false,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.5,
      labelPosition: labelPosition,
      padding: padding,
      isVerticalLayout: json['isVerticalLayout'] ?? false,
      onChanged: (value) {
        // This would be handled by the app in a real implementation
      },
    );
  }

  /// Converts the CheckboxWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    String labelPositionString = 'end';
    if (labelPosition == TextAlign.start) {
      labelPositionString = 'start';
    }

    double paddingValue = 8.0;
    if (padding is EdgeInsets) {
      final edgeInsets = padding as EdgeInsets;
      if (edgeInsets.left == edgeInsets.top &&
          edgeInsets.left == edgeInsets.right &&
          edgeInsets.left == edgeInsets.bottom) {
        paddingValue = edgeInsets.left;
      }
    }

    return {
      'initialValue': initialValue,
      'checkColor': _colorToJson(checkColor),
      'activeColor': _colorToJson(activeColor),
      'checkOutlineColor': _colorToJson(checkOutlineColor),
      'size': size,
      if (label != null) 'label': label,
      'showLabel': showLabel,
      'isDisabled': isDisabled,
      'isRounded': isRounded,
      'borderRadius': borderRadius,
      'borderWidth': borderWidth,
      'labelPosition': labelPositionString,
      'padding': paddingValue,
      'isVerticalLayout': isVerticalLayout,
    };
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors - return MaterialColor for standard colors
      // to ensure round-trip conversion works correctly
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Color(0xFF0058FF);
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        case 'amber':
          return Colors.amber;
        case 'cyan':
          return Colors.cyan;
        case 'indigo':
          return Colors.indigo;
        case 'lime':
          return Colors.lime;
        case 'teal':
          return Colors.teal;
        default:
          return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Converts a Flutter Color to a JSON representation
  ///
  /// Returns a hex string (e.g., "#FF0000") or a color name for standard colors
  static String _colorToJson(Color color) {
    // Handle standard colors by name for better readability and test compatibility
    if (color == Colors.red) return 'red';
    if (color == Color(0xFF0058FF)) return 'blue';
    if (color == Colors.green) return 'green';
    if (color == Colors.yellow) return 'yellow';
    if (color == Colors.orange) return 'orange';
    if (color == Colors.purple) return 'purple';
    if (color == Colors.pink) return 'pink';
    if (color == Colors.brown) return 'brown';
    if (color == Colors.grey) return 'grey';
    if (color == Colors.black) return 'black';
    if (color == Colors.white) return 'white';
    if (color == Colors.amber) return 'amber';
    if (color == Colors.cyan) return 'cyan';
    if (color == Colors.indigo) return 'indigo';
    if (color == Colors.lime) return 'lime';
    if (color == Colors.teal) return 'teal';

    // For MaterialColor, preserve the original color name if possible
    if (color is MaterialColor) {
      // We'll keep the original MaterialColor in the round-trip conversion
      // by using a special format that our fromJson method can recognize
      if (color == Colors.red) return 'red';
      if (color == Color(0xFF0058FF)) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.amber) return 'amber';
      if (color == Colors.cyan) return 'cyan';
      if (color == Colors.indigo) return 'indigo';
      if (color == Colors.lime) return 'lime';
      if (color == Colors.teal) return 'teal';

      // If it's a MaterialColor but not one of the standard ones,
      // fall back to the hex representation of the primary value
      color = color.shade500;
    }

    // Convert to RGB format and create a hex string for other colors
    final r = color.toString().substring(10, 12);
    final g = color.toString().substring(12, 14);
    final b = color.toString().substring(14, 16);

    return '#$r$g$b';
  }

  @override
  State<CheckboxWidget> createState() => _CheckboxWidgetState();
}

class _CheckboxWidgetState extends State<CheckboxWidget> {
  late bool _isChecked;

  @override
  void initState() {
    super.initState();
    // Use test value if provided (for testing purposes)
    if (widget.testValue != null) {
      _isChecked = widget.testValue!;
    } else {
      _isChecked =
          widget.initialValue; // Initialize the checkbox with the given value
    }
  }

  @override
  Widget build(BuildContext context) {
    // Create the checkbox with all the specified properties
    final checkbox = Column(
      children: [
         Text(
  widget.displayName ?? 'Checkbox',
      style: TextStyle(
        fontSize: widget.size / 2.5,
        color: widget.isDisabled ? Colors.grey : Colors.black87,
      ),
    ),
        SizedBox(
          width: widget.size,
          height: widget.size,
          child: Checkbox(
            value: _isChecked, // The current state of the checkbox
            onChanged:
                widget.isDisabled
                    ? null
                    : (bool? newValue) {
                      setState(() {
                        _isChecked =
                            newValue!; // Update the state with the new value
                      });
                      if (widget.onChanged != null) {
                        widget.onChanged!(
                          _isChecked,
                        ); // Trigger callback with new value
                      }
                    },
            checkColor: widget.checkColor, // Set the checkmark color
            activeColor:
                widget.activeColor, // Set the color when checkbox is checked
            side: BorderSide(
              color: widget.checkOutlineColor, // Set the outline color
              width: 1.5,
            ),
            shape:
                widget.isRounded
                    ? RoundedRectangleBorder(borderRadius: BorderRadius.circular(3))
                    : null,
            materialTapTargetSize:
                MaterialTapTargetSize
                    .shrinkWrap, // Shrink tap area for smaller checkboxes
          ),
        ),
      ],
    );

    // Determine the label text to display
    final labelText = widget.label ?? widget.displayName ?? 'Checkbox';

    // If no label or showLabel is false, just return the checkbox
    if (!widget.showLabel) {
      return Padding(padding: widget.padding, child: checkbox);
    }

    // Create the label widget
    final labelWidget =
     Text(
      widget.label!,
      style: TextStyle(
        fontSize: widget.size / 2.5,
        color: widget.isDisabled ? Colors.grey : Colors.black87,
      ),
    );

    // Always arrange checkbox on left and text on right, vertically centered
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        checkbox,
        const SizedBox(width: 8),
        labelWidget,
      ],
    );
  }
}
