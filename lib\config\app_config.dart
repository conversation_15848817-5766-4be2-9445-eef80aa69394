import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../utils/constants.dart';
import '../utils/logger.dart';

/// A class that encapsulates the app configuration
class AppConfig {
  // Private constructor to prevent instantiation
  AppConfig._();

  // App information
  static String get appName => AppConstants.appName;
  static String get appVersionSync => AppConstants.defaultAppVersion;
  static Future<String> getAppVersion() => AppConstants.getAppVersion();

  // Environment
  static bool _isProduction = false;
  static bool _isStaging = false;
  static bool _isDevelopment = true;

  // API endpoints
  static String? _apiBaseUrl;
  static String? _authBaseUrl;
  static String? _chatBaseUrl;
  static String? _buildBaseUrl;
  static String? _transactionBaseUrl;
  static String? _workflowBaseUrl;
  static String? _validateBaseUrl;

  // Getters
  static bool get isProduction => _isProduction;
  static bool get isStaging => _isStaging;
  static bool get isDevelopment => _isDevelopment;

  static String get apiBaseUrl => _apiBaseUrl ?? '';
  static String get authBaseUrl => _authBaseUrl ?? '';
  static String get chatBaseUrl => _chatBaseUrl ?? '';
  static String get buildBaseUrl => _buildBaseUrl ?? '';
  static String get transactionBaseUrl => _transactionBaseUrl ?? '';
  static String get workflowBaseUrl => _workflowBaseUrl ?? '';
  static String get validateBaseUrl => _validateBaseUrl ?? '';

  /// Initialize the app configuration
  static Future<void> init() async {
    try {
      Logger.info('Initializing AppConfig');

      // Load environment variables
      await dotenv.load(fileName: 'assets/env');
      Logger.info('Environment variables loaded');

      // Set environment
      final environment = dotenv.env['ENVIRONMENT'] ?? 'development';
      _isProduction = environment == 'production';
      _isStaging = environment == 'staging';
      _isDevelopment = environment == 'development';

      Logger.info('Environment: $environment');

      // Set API endpoints
      _apiBaseUrl = dotenv.env['API_BASE_URL'];
      _authBaseUrl = dotenv.env['AUTH_BASE_URL'] ?? _apiBaseUrl;
      _chatBaseUrl = dotenv.env['CHAT_API_BASE_URL'] ??
          dotenv.env['CHAT_BASE_URL'] ??
          _apiBaseUrl;
      _buildBaseUrl = dotenv.env['BUILD_API_BASE_URL'] ?? _apiBaseUrl;
      _transactionBaseUrl =
          dotenv.env['TRANSACTION_API_BASE_URL'] ?? _apiBaseUrl;
      _workflowBaseUrl = dotenv.env['WORKFLOW_BASE_URL'] ?? _apiBaseUrl;
      _validateBaseUrl =
          dotenv.env['VALIDATE_API_BASE_URL'] ?? _validateBaseUrl;

      Logger.info('API Base URL: $_apiBaseUrl');

      Logger.info('AppConfig initialized successfully');
    } catch (e) {
      Logger.error('Error initializing AppConfig: $e');
      rethrow;
    }
  }

  /// Get the app theme mode
  static ThemeMode getThemeMode(String? themeModeString) {
    switch (themeModeString) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      default:
        return ThemeMode.system;
    }
  }
}
