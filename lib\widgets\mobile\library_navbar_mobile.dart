import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/new_design/my_library_mobile/books_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/solutions_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/objects_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/agents_library_mobile.dart';

/// A reusable navigation item widget for mobile library screens
/// Used across Books, Solutions, Objects, and Agents library screens
class LibraryNavbarMobile extends StatefulWidget {
  final int selectedTabIndex;
  final Function(int)? onTabChanged;

  const LibraryNavbarMobile({
    super.key,
    required this.selectedTabIndex,
    this.onTabChanged,
  });

  @override
  State<LibraryNavbarMobile> createState() => _LibraryNavbarMobileState();
}

class _LibraryNavbarMobileState extends State<LibraryNavbarMobile> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _LibraryNavItem(
            iconPath: 'assets/images/books-icon.svg',
            label: AppLocalizations.of(context).translate('library.books'),
            isActive: widget.selectedTabIndex == 0,
            onTap: () {
              if (widget.onTabChanged != null) {
                widget.onTabChanged!(0);
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const BooksLibraryMobile(),
                  ),
                );
              }
            },
          ),
          _LibraryNavItem(
            iconPath: 'assets/images/square-box-uncheck.svg',
            label: AppLocalizations.of(context).translate('library.solutions'),
            isActive: widget.selectedTabIndex == 1,
            onTap: () {
              if (widget.onTabChanged != null) {
                widget.onTabChanged!(1);
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SolutionsLibraryMobile(),
                  ),
                );
              }
            },
          ),
          _LibraryNavItem(
            iconPath: 'assets/images/cube-box.svg',
            label: AppLocalizations.of(context).translate('library.objects'),
            isActive: widget.selectedTabIndex == 2,
            onTap: () {
              if (widget.onTabChanged != null) {
                widget.onTabChanged!(2);
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ObjectsLibraryMobile(),
                  ),
                );
              }
            },
          ),
          _LibraryNavItem(
            iconPath: 'assets/images/agent-icon.svg',
            label: AppLocalizations.of(context).translate('library.agents'),
            isActive: widget.selectedTabIndex == 3,
            onTap: () {
              if (widget.onTabChanged != null) {
                widget.onTabChanged!(3);
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AgentsLibraryMobile(),
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }
}

/// Individual navigation item widget
class _LibraryNavItem extends StatefulWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTap;
  final bool isActive;

  const _LibraryNavItem({
    required this.iconPath,
    required this.label,
    required this.onTap,
    this.isActive = false,
  });

  @override
  State<_LibraryNavItem> createState() => _LibraryNavItemState();
}

class _LibraryNavItemState extends State<_LibraryNavItem> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onTap,
      child: Container(
        height: 34,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: widget.isActive
              ? const Color(0xff0058FF)
              : (isPressed ? Colors.white : const Color(0xffEBEBEB)),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: 16,
            height: 16,
            colorFilter: ColorFilter.mode(
              widget.isActive ? Colors.white : Colors.black,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }
}
