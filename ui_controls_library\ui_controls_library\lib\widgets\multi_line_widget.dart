import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A customizable multi-line text input widget.
///
/// This widget provides a rich set of customization options for multi-line text input,
/// including styling, validation, character counting, and more.
class MultiLineWidget extends StatefulWidget {
  /// The initial value of the text input.
  final String? initialValue;

  /// The hint text to show when the input is empty.
  final String? hintText;

  /// The label text to show above the input.
  final String? labelText;

  /// The helper text to show below the input.
  final String? helperText;

  /// The error text to show when validation fails.
  final String? errorText;

  /// The minimum number of lines to show.
  final int minLines;

  /// The maximum number of lines to show.
  final int? maxLines;

  /// The minimum number of characters allowed.
  final int? minLength;

  /// The maximum number of characters allowed.
  final int? maxLength;

  /// Whether to show the character count.
  final bool showCharacterCount;

  /// The position of the character count.
  final CharacterCountPosition characterCountPosition;

  /// Whether the input is required.
  final bool isRequired;

  /// Whether the input is read-only.
  final bool readOnly;

  /// Whether the input is disabled.
  final bool isDisabled;

  /// Whether to auto-validate the input.
  final bool autoValidate;

  /// Whether to expand to fill available space.
  final bool expands;

  /// The text style of the input.
  final TextStyle? textStyle;

  /// The text style of the label.
  final TextStyle? labelStyle;

  /// The text style of the hint.
  final TextStyle? hintStyle;

  /// The text style of the helper text.
  final TextStyle? helperStyle;

  /// The text style of the error text.
  final TextStyle? errorStyle;

  /// The text style of the character count.
  final TextStyle? characterCountStyle;

  /// The color of the input text.
  final Color? textColor;

  /// The color of the label text.
  final Color? labelColor;

  /// The color of the hint text.
  final Color? hintColor;

  /// The color of the helper text.
  final Color? helperColor;

  /// The color of the error text.
  final Color? errorColor;

  /// The color of the character count text.
  final Color? characterCountColor;

  /// The color of the cursor.
  final Color? cursorColor;

  /// The color of the input background.
  final Color? backgroundColor;

  /// The color of the border.
  final Color? borderColor;

  /// The color of the focused border.
  final Color? focusedBorderColor;

  /// The color of the error border.
  final Color? errorBorderColor;

  /// The width of the border.
  final double borderWidth;

  /// The width of the focused border.
  final double focusedBorderWidth;

  /// The width of the error border.
  final double errorBorderWidth;

  /// The radius of the border corners.
  final double borderRadius;

  /// The width of the input.
  final double? width;

  /// The height of the input.
  final double? height;

  /// The padding around the input.
  final EdgeInsetsGeometry padding;

  /// The margin around the input.
  final EdgeInsetsGeometry margin;

  /// The text alignment within the input.
  final TextAlign textAlign;

  /// The text direction within the input.
  final TextDirection? textDirection;

  /// The text capitalization within the input.
  final TextCapitalization textCapitalization;

  /// Whether to enable suggestions.
  final bool enableSuggestions;

  /// Whether to enable IME personalized learning.
  final bool enableIMEPersonalizedLearning;

  /// Whether to enable interactive selection.
  final bool enableInteractiveSelection;

  /// Whether to show a clear button.
  final bool showClearButton;

  /// The color of the clear button icon.
  final Color? clearButtonColor;

  /// The size of the clear button icon.
  final double clearButtonSize;

  /// Whether to show a copy button.
  final bool showCopyButton;

  /// The color of the copy button icon.
  final Color? copyButtonColor;

  /// The size of the copy button icon.
  final double copyButtonSize;

  /// Whether to show a paste button.
  final bool showPasteButton;

  /// The color of the paste button icon.
  final Color? pasteButtonColor;

  /// The size of the paste button icon.
  final double pasteButtonSize;

  /// Whether to show a shadow under the input.
  final bool hasShadow;

  /// The elevation of the shadow.
  final double elevation;

  /// The color of the shadow.
  final Color? shadowColor;

  /// The keyboard type to use for the input.
  final TextInputType keyboardType;

  /// The text input action to use for the input.
  final TextInputAction textInputAction;

  /// The input formatters to use for the input.
  final List<TextInputFormatter>? inputFormatters;

  /// The callback to execute when the input value changes.
  final Function(String value)? onChanged;

  /// The callback to execute when the input is submitted.
  final Function(String value)? onSubmitted;

  /// The callback to execute when the input is validated.
  final bool Function(String value)? validator;

  /// The callback to execute when the clear button is pressed.
  final VoidCallback? onClear;

  /// The callback to execute when the copy button is pressed.
  final VoidCallback? onCopy;

  /// The callback to execute when the paste button is pressed.
  final VoidCallback? onPaste;

  /// The callback to execute when the input is tapped.
  final VoidCallback? onTap;

  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  /// JSON data for additional configuration
  ///
  /// This can include any custom configuration data for the multi-line widget.
  final dynamic jsonConfig;

  /// Whether to use custom validation rules from JSON config
  final bool useJsonValidation;

  /// Whether to use custom styling from JSON config
  final bool useJsonStyling;

  /// Whether to use custom behavior from JSON config
  final bool useJsonBehavior;

  /// Whether to show custom elements from JSON config
  final bool showJsonElements;

  /// Custom validation message from JSON config
  final String? jsonValidationMessage;

  /// Custom placeholder text from JSON config
  final String? jsonPlaceholderText;

  /// Custom content type from JSON config (e.g., "comment", "description", "feedback")
  final String? jsonContentType;

  /// Creates a multi-line text input widget.
  const MultiLineWidget({
    super.key,
    this.initialValue,
    this.hintText = 'Enter text here',
    this.labelText,
    this.helperText,
    this.errorText,
    this.minLines = 3,
    this.maxLines,
    this.minLength,
    this.maxLength,
    this.showCharacterCount = false,
    this.characterCountPosition = CharacterCountPosition.below,
    this.isRequired = false,
    this.readOnly = false,
    this.isDisabled = false,
    this.autoValidate = false,
    this.expands = false,
    this.textStyle,
    this.labelStyle,
    this.hintStyle,
    this.helperStyle,
    this.errorStyle,
    this.characterCountStyle,
    this.textColor,
    this.labelColor,
    this.hintColor,
    this.helperColor,
    this.errorColor,
    this.characterCountColor,
    this.cursorColor,
    this.backgroundColor,
    this.borderColor = Colors.amber,
    this.focusedBorderColor = const Color(0xFF0058FF),
    this.errorBorderColor = Colors.red,
    this.borderWidth = 1.0,
    this.focusedBorderWidth = 2.0,
    this.errorBorderWidth = 1.0,
    this.borderRadius = 4.0,
    this.width,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = EdgeInsets.zero,
    this.textAlign = TextAlign.start,
    this.textDirection,
    this.textCapitalization = TextCapitalization.none,
    this.enableSuggestions = true,
    this.enableIMEPersonalizedLearning = true,
    this.enableInteractiveSelection = true,
    this.showClearButton = false,
    this.clearButtonColor,
    this.clearButtonSize = 20.0,
    this.showCopyButton = false,
    this.copyButtonColor,
    this.copyButtonSize = 20.0,
    this.showPasteButton = false,
    this.pasteButtonColor,
    this.pasteButtonSize = 20.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.shadowColor,
    this.keyboardType = TextInputType.multiline,
    this.textInputAction = TextInputAction.newline,
    this.inputFormatters,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.onClear,
    this.onCopy,
    this.onPaste,
    this.onTap,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.hoverColor,
    this.focusColor,
    this.tooltip,
    this.semanticsLabel,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // JSON configuration properties
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonBehavior = false,
    this.showJsonElements = false,
    this.jsonValidationMessage,
    this.jsonPlaceholderText,
    this.jsonContentType,
  });

  /// Creates a MultiLineWidget from a JSON map
  ///
  /// This factory constructor allows creating a MultiLineWidget from a JSON map,
  /// making it easy to configure the widget from dynamic data.
  factory MultiLineWidget.fromJson(Map<String, dynamic> json) {
    // Parse basic properties
    final initialValue = json['initialValue']?.toString();
    final hintText = json['hintText']?.toString() ?? 'Enter text here';
    final labelText = json['labelText']?.toString();
    final helperText = json['helperText']?.toString();
    final errorText = json['errorText']?.toString();

    // Parse numeric properties
    final minLines =
        json['minLines'] != null
            ? int.tryParse(json['minLines'].toString()) ?? 3
            : 3;
    final maxLines =
        json['maxLines'] != null
            ? int.tryParse(json['maxLines'].toString())
            : null;
    final minLength =
        json['minLength'] != null
            ? int.tryParse(json['minLength'].toString())
            : null;
    final maxLength =
        json['maxLength'] != null
            ? int.tryParse(json['maxLength'].toString())
            : null;

    // Parse boolean properties
    final showCharacterCount = json['showCharacterCount'] == true;
    final isRequired = json['isRequired'] == true;
    final readOnly = json['readOnly'] == true;
    final isDisabled = json['isDisabled'] == true;
    final autoValidate = json['autoValidate'] == true;
    final expands = json['expands'] == true;
    final enableSuggestions = json['enableSuggestions'] != false;
    final enableIMEPersonalizedLearning =
        json['enableIMEPersonalizedLearning'] != false;
    final enableInteractiveSelection =
        json['enableInteractiveSelection'] != false;
    final showClearButton = json['showClearButton'] == true;
    final showCopyButton = json['showCopyButton'] == true;
    final showPasteButton = json['showPasteButton'] == true;
    final hasShadow = json['hasShadow'] == true;
    final enableFeedback = json['enableFeedback'] != false;

    // Parse JSON configuration properties
    final useJsonValidation = json['useJsonValidation'] == true;
    final useJsonStyling = json['useJsonStyling'] == true;
    final useJsonBehavior = json['useJsonBehavior'] == true;
    final showJsonElements = json['showJsonElements'] == true;
    final jsonValidationMessage = json['jsonValidationMessage']?.toString();
    final jsonPlaceholderText = json['jsonPlaceholderText']?.toString();
    final jsonContentType = json['jsonContentType']?.toString();

    // Parse character count position
    CharacterCountPosition characterCountPosition =
        CharacterCountPosition.below;
    if (json['characterCountPosition'] != null) {
      final positionStr =
          json['characterCountPosition'].toString().toLowerCase();
      if (positionStr == 'inside') {
        characterCountPosition = CharacterCountPosition.inside;
      } else if (positionStr == 'above') {
        characterCountPosition = CharacterCountPosition.above;
      }
    }

    // Parse color properties
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Try to parse as hex
        if (colorValue.startsWith('#')) {
          try {
            final hexValue = int.parse(colorValue.substring(1), radix: 16);
            if (colorValue.length == 7) {
              // #RRGGBB format
              return Color(0xFF000000 | hexValue);
            } else if (colorValue.length == 9) {
              // #AARRGGBB format
              return Color(hexValue);
            }
          } catch (e) {
            // Ignore parsing errors and return null
          }
        }

        // Try to match color names
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          default:
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    final textColor = parseColor(json['textColor']);
    final labelColor = parseColor(json['labelColor']);
    final hintColor = parseColor(json['hintColor']);
    final helperColor = parseColor(json['helperColor']);
    final errorColor = parseColor(json['errorColor']);
    final characterCountColor = parseColor(json['characterCountColor']);
    final cursorColor = parseColor(json['cursorColor']);
    final backgroundColor = parseColor(json['backgroundColor']);
    final borderColor = parseColor(json['borderColor']) ?? Colors.amber;
    final focusedBorderColor =
        parseColor(json['focusedBorderColor']) ?? Colors.blue;
    final errorBorderColor = parseColor(json['errorBorderColor']) ?? Colors.red;
    final clearButtonColor = parseColor(json['clearButtonColor']);
    final copyButtonColor = parseColor(json['copyButtonColor']);
    final pasteButtonColor = parseColor(json['pasteButtonColor']);
    final shadowColor = parseColor(json['shadowColor']);
    final hoverColor = parseColor(json['hoverColor']);
    final focusColor = parseColor(json['focusColor']);

    // Parse numeric values
    final borderWidth =
        json['borderWidth'] != null
            ? double.tryParse(json['borderWidth'].toString()) ?? 1.0
            : 1.0;
    final focusedBorderWidth =
        json['focusedBorderWidth'] != null
            ? double.tryParse(json['focusedBorderWidth'].toString()) ?? 2.0
            : 2.0;
    final errorBorderWidth =
        json['errorBorderWidth'] != null
            ? double.tryParse(json['errorBorderWidth'].toString()) ?? 1.0
            : 1.0;
    final borderRadius =
        json['borderRadius'] != null
            ? double.tryParse(json['borderRadius'].toString()) ?? 4.0
            : 4.0;
    final clearButtonSize =
        json['clearButtonSize'] != null
            ? double.tryParse(json['clearButtonSize'].toString()) ?? 20.0
            : 20.0;
    final copyButtonSize =
        json['copyButtonSize'] != null
            ? double.tryParse(json['copyButtonSize'].toString()) ?? 20.0
            : 20.0;
    final pasteButtonSize =
        json['pasteButtonSize'] != null
            ? double.tryParse(json['pasteButtonSize'].toString()) ?? 20.0
            : 20.0;
    final elevation =
        json['elevation'] != null
            ? double.tryParse(json['elevation'].toString()) ?? 2.0
            : 2.0;

    // Parse width and height
    double? width;
    if (json['width'] != null) {
      if (json['width'].toString().toLowerCase() == 'infinity' ||
          json['width'].toString().toLowerCase() == 'double.infinity') {
        width = double.infinity;
      } else {
        width = double.tryParse(json['width'].toString());
      }
    }

    double? height;
    if (json['height'] != null) {
      height = double.tryParse(json['height'].toString());
    }

    // Parse text alignment
    TextAlign textAlign = TextAlign.start;
    if (json['textAlign'] != null) {
      final alignStr = json['textAlign'].toString().toLowerCase();
      switch (alignStr) {
        case 'center':
          textAlign = TextAlign.center;
          break;
        case 'end':
        case 'right':
          textAlign = TextAlign.end;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
        case 'left':
        case 'start':
        default:
          textAlign = TextAlign.start;
          break;
      }
    }

    // Parse text direction
    TextDirection? textDirection;
    if (json['textDirection'] != null) {
      final dirStr = json['textDirection'].toString().toLowerCase();
      switch (dirStr) {
        case 'rtl':
          textDirection = TextDirection.rtl;
          break;
        case 'ltr':
          textDirection = TextDirection.ltr;
          break;
      }
    }

    // Parse text capitalization
    TextCapitalization textCapitalization = TextCapitalization.none;
    if (json['textCapitalization'] != null) {
      final capStr = json['textCapitalization'].toString().toLowerCase();
      switch (capStr) {
        case 'words':
          textCapitalization = TextCapitalization.words;
          break;
        case 'sentences':
          textCapitalization = TextCapitalization.sentences;
          break;
        case 'characters':
          textCapitalization = TextCapitalization.characters;
          break;
        case 'none':
        default:
          textCapitalization = TextCapitalization.none;
          break;
      }
    }

    // Parse keyboard type
    TextInputType keyboardType = TextInputType.multiline;
    if (json['keyboardType'] != null) {
      final typeStr = json['keyboardType'].toString().toLowerCase();
      switch (typeStr) {
        case 'text':
          keyboardType = TextInputType.text;
          break;
        case 'number':
          keyboardType = TextInputType.number;
          break;
        case 'phone':
          keyboardType = TextInputType.phone;
          break;
        case 'email':
          keyboardType = TextInputType.emailAddress;
          break;
        case 'url':
          keyboardType = TextInputType.url;
          break;
        case 'multiline':
        default:
          keyboardType = TextInputType.multiline;
          break;
      }
    }

    // Parse text input action
    TextInputAction textInputAction = TextInputAction.newline;
    if (json['textInputAction'] != null) {
      final actionStr = json['textInputAction'].toString().toLowerCase();
      switch (actionStr) {
        case 'done':
          textInputAction = TextInputAction.done;
          break;
        case 'go':
          textInputAction = TextInputAction.go;
          break;
        case 'search':
          textInputAction = TextInputAction.search;
          break;
        case 'send':
          textInputAction = TextInputAction.send;
          break;
        case 'next':
          textInputAction = TextInputAction.next;
          break;
        case 'previous':
          textInputAction = TextInputAction.previous;
          break;
        case 'newline':
        default:
          textInputAction = TextInputAction.newline;
          break;
      }
    }

    // Parse padding and margin
    EdgeInsetsGeometry parsePadding(dynamic paddingValue) {
      if (paddingValue == null) {
        return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
      }

      if (paddingValue is Map) {
        final left =
            double.tryParse(paddingValue['left']?.toString() ?? '12.0') ?? 12.0;
        final top =
            double.tryParse(paddingValue['top']?.toString() ?? '8.0') ?? 8.0;
        final right =
            double.tryParse(paddingValue['right']?.toString() ?? '12.0') ??
            12.0;
        final bottom =
            double.tryParse(paddingValue['bottom']?.toString() ?? '8.0') ?? 8.0;

        return EdgeInsets.fromLTRB(left, top, right, bottom);
      }

      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
    }

    final padding = parsePadding(json['padding']);
    final margin = parsePadding(json['margin']);

    // Create the widget with all parsed properties
    return MultiLineWidget(
      initialValue: initialValue,
      hintText: hintText,
      labelText: labelText,
      helperText: helperText,
      errorText: errorText,
      minLines: minLines,
      maxLines: maxLines,
      minLength: minLength,
      maxLength: maxLength,
      showCharacterCount: showCharacterCount,
      characterCountPosition: characterCountPosition,
      isRequired: isRequired,
      readOnly: readOnly,
      isDisabled: isDisabled,
      autoValidate: autoValidate,
      expands: expands,
      textColor: textColor,
      labelColor: labelColor,
      hintColor: hintColor,
      helperColor: helperColor,
      errorColor: errorColor,
      characterCountColor: characterCountColor,
      cursorColor: cursorColor,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      focusedBorderColor: focusedBorderColor,
      errorBorderColor: errorBorderColor,
      borderWidth: borderWidth,
      focusedBorderWidth: focusedBorderWidth,
      errorBorderWidth: errorBorderWidth,
      borderRadius: borderRadius,
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      textAlign: textAlign,
      textDirection: textDirection,
      textCapitalization: textCapitalization,
      enableSuggestions: enableSuggestions,
      enableIMEPersonalizedLearning: enableIMEPersonalizedLearning,
      enableInteractiveSelection: enableInteractiveSelection,
      showClearButton: showClearButton,
      clearButtonColor: clearButtonColor,
      clearButtonSize: clearButtonSize,
      showCopyButton: showCopyButton,
      copyButtonColor: copyButtonColor,
      copyButtonSize: copyButtonSize,
      showPasteButton: showPasteButton,
      pasteButtonColor: pasteButtonColor,
      pasteButtonSize: pasteButtonSize,
      hasShadow: hasShadow,
      elevation: elevation,
      shadowColor: shadowColor,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      // Advanced interaction properties
      hoverColor: hoverColor,
      focusColor: focusColor,
      tooltip: json['tooltip']?.toString(),
      semanticsLabel: json['semanticsLabel']?.toString(),
      enableFeedback: enableFeedback,
      // JSON configuration properties
      jsonConfig: json,
      useJsonValidation: useJsonValidation,
      useJsonStyling: useJsonStyling,
      useJsonBehavior: useJsonBehavior,
      showJsonElements: showJsonElements,
      jsonValidationMessage: jsonValidationMessage,
      jsonPlaceholderText: jsonPlaceholderText,
      jsonContentType: jsonContentType,
    );
  }

  @override
  MultiLineWidgetState createState() => MultiLineWidgetState();
}

/// The position of the character count in the multi-line widget.
enum CharacterCountPosition {
  /// Show the character count below the input.
  below,

  /// Show the character count inside the input at the bottom-right.
  inside,

  /// Show the character count above the input.
  above,
}

class MultiLineWidgetState extends State<MultiLineWidget> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  bool _hasFocus = false;
  bool _hasError = false;
  String? _errorText;
  bool _isHovered = false;
  Map<String, dynamic>? _parsedJsonConfig;
  List<String>? _jsonValidationRules;
  List<String>? _jsonCustomElements;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parseJsonConfig();
    }

    // Validate initial value if auto-validate is enabled
    if (widget.autoValidate &&
        widget.initialValue != null &&
        widget.initialValue!.isNotEmpty) {
      _validate(widget.initialValue!);
    }
  }

  /// Parses the JSON configuration
  void _parseJsonConfig() {
    try {
      if (widget.jsonConfig is String) {
        _parsedJsonConfig =
            jsonDecode(widget.jsonConfig as String) as Map<String, dynamic>;
      } else if (widget.jsonConfig is Map) {
        _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig as Map);
      }

      // Extract validation rules if needed
      if (widget.useJsonValidation &&
          _parsedJsonConfig != null &&
          _parsedJsonConfig!.containsKey('validationRules')) {
        final rules = _parsedJsonConfig!['validationRules'];
        if (rules is List) {
          _jsonValidationRules = List<String>.from(
            rules.map((rule) => rule.toString()),
          );
        }
      }

      // Extract custom elements if needed
      if (widget.showJsonElements &&
          _parsedJsonConfig != null &&
          _parsedJsonConfig!.containsKey('customElements')) {
        final elements = _parsedJsonConfig!['customElements'];
        if (elements is List) {
          _jsonCustomElements = List<String>.from(
            elements.map((element) => element.toString()),
          );
        }
      }
    } catch (e) {
      debugPrint('Error parsing JSON config: $e');
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
      if (_hasFocus && widget.onTap != null) {
        widget.onTap!();
      }

      // Call onFocus callback if provided
      if (widget.onFocus != null) {
        widget.onFocus!(_hasFocus);
      }
    });
  }

  /// Handles hover state changes
  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;

      // Call onHover callback if provided
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    });
  }

  bool _validate(String value) {
    // Use JSON validation if enabled
    if (widget.useJsonValidation &&
        _jsonValidationRules != null &&
        _jsonValidationRules!.isNotEmpty) {
      return _validateWithJsonRules(value);
    }

    // Standard validation
    if (widget.validator != null) {
      final isValid = widget.validator!(value);
      setState(() {
        _hasError = !isValid;
        _errorText = isValid ? null : (widget.errorText ?? 'Invalid input');
      });
      return isValid;
    } else if (widget.isRequired && value.isEmpty) {
      setState(() {
        _hasError = true;
        _errorText = 'This field is required';
      });
      return false;
    } else if (widget.minLength != null && value.length < widget.minLength!) {
      setState(() {
        _hasError = true;
        _errorText = 'Minimum ${widget.minLength} characters required';
      });
      return false;
    } else {
      setState(() {
        _hasError = false;
        _errorText = null;
      });
      return true;
    }
  }

  /// Validates the input using JSON validation rules
  bool _validateWithJsonRules(String value) {
    bool isValid = true;
    String? errorMessage;

    // Check if we have a custom validation message from JSON
    final customMessage =
        widget.jsonValidationMessage ??
        (_parsedJsonConfig != null &&
                _parsedJsonConfig!.containsKey('validationMessage')
            ? _parsedJsonConfig!['validationMessage'].toString()
            : null);

    // Apply each validation rule
    for (final rule in _jsonValidationRules!) {
      if (rule.contains('required') && value.isEmpty) {
        isValid = false;
        errorMessage = customMessage ?? 'This field is required';
        break;
      } else if (rule.contains('minLength:')) {
        final minLengthMatch = RegExp(r'minLength:(\d+)').firstMatch(rule);
        if (minLengthMatch != null) {
          final minLength = int.tryParse(minLengthMatch.group(1) ?? '') ?? 0;
          if (value.length < minLength) {
            isValid = false;
            errorMessage =
                customMessage ?? 'Minimum $minLength characters required';
            break;
          }
        }
      } else if (rule.contains('maxLength:')) {
        final maxLengthMatch = RegExp(r'maxLength:(\d+)').firstMatch(rule);
        if (maxLengthMatch != null) {
          final maxLength = int.tryParse(maxLengthMatch.group(1) ?? '') ?? 0;
          if (value.length > maxLength) {
            isValid = false;
            errorMessage =
                customMessage ?? 'Maximum $maxLength characters allowed';
            break;
          }
        }
      } else if (rule.contains('pattern:')) {
        final patternMatch = RegExp(r'pattern:(.+)').firstMatch(rule);
        if (patternMatch != null) {
          final pattern = patternMatch.group(1) ?? '';
          try {
            final regex = RegExp(pattern);
            if (!regex.hasMatch(value)) {
              isValid = false;
              errorMessage = customMessage ?? 'Invalid format';
              break;
            }
          } catch (e) {
            debugPrint('Error parsing regex pattern: $e');
          }
        }
      } else if (rule.contains('email') && value.isNotEmpty) {
        final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
        if (!emailRegex.hasMatch(value)) {
          isValid = false;
          errorMessage = customMessage ?? 'Invalid email format';
          break;
        }
      } else if (rule.contains('url') && value.isNotEmpty) {
        final urlRegex = RegExp(
          r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$',
        );
        if (!urlRegex.hasMatch(value)) {
          isValid = false;
          errorMessage = customMessage ?? 'Invalid URL format';
          break;
        }
      } else if (rule.contains('noSpecialChars') && value.isNotEmpty) {
        final specialCharsRegex = RegExp(r'[!@#$%^&*(),.?":{}|<>]');
        if (specialCharsRegex.hasMatch(value)) {
          isValid = false;
          errorMessage = customMessage ?? 'Special characters are not allowed';
          break;
        }
      } else if (rule.contains('alphanumeric') && value.isNotEmpty) {
        final alphanumericRegex = RegExp(r'^[a-zA-Z0-9]+$');
        if (!alphanumericRegex.hasMatch(value)) {
          isValid = false;
          errorMessage =
              customMessage ?? 'Only alphanumeric characters are allowed';
          break;
        }
      }
    }

    setState(() {
      _hasError = !isValid;
      _errorText = isValid ? null : (errorMessage ?? 'Invalid input');
    });

    return isValid;
  }

  void _handleClear() {
    setState(() {
      _controller.clear();
      _hasError = false;
      _errorText = null;
    });

    if (widget.onClear != null) {
      widget.onClear!();
    }

    if (widget.onChanged != null) {
      widget.onChanged!('');
    }
  }

  Future<void> _handlePaste() async {
    final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data != null && data.text != null) {
      setState(() {
        _controller.text = data.text!;
        if (widget.autoValidate) {
          _validate(_controller.text);
        }
      });

      if (widget.onChanged != null) {
        widget.onChanged!(_controller.text);
      }
    }

    if (widget.onPaste != null) {
      widget.onPaste!();
    }
  }

  void _handleCopy() {
    Clipboard.setData(ClipboardData(text: _controller.text));

    if (widget.onCopy != null) {
      widget.onCopy!();
    }

    // Show a snackbar to indicate the text was copied
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Text copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  Widget _buildCharacterCount() {
    final int currentLength = _controller.text.length;
    final int? maxLength = widget.maxLength;
    final String countText =
        maxLength != null
            ? '$currentLength / $maxLength'
            : '$currentLength characters';

    return Text(
      countText,
      style:
          widget.characterCountStyle ??
          TextStyle(
            fontSize: 12,
            color:
                widget.characterCountColor ??
                (currentLength > (maxLength ?? double.infinity)
                    ? Colors.red
                    : Colors.grey),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Define text styles
    final TextStyle effectiveTextStyle =
        widget.textStyle ?? theme.textTheme.bodyMedium ?? const TextStyle();

    final TextStyle effectiveLabelStyle =
        widget.labelStyle ?? theme.textTheme.bodySmall ?? const TextStyle();

    final TextStyle effectiveHintStyle =
        widget.hintStyle ??
        theme.textTheme.bodySmall?.copyWith(color: theme.hintColor) ??
        TextStyle(color: theme.hintColor);

    final TextStyle effectiveHelperStyle =
        widget.helperStyle ?? theme.textTheme.bodySmall ?? const TextStyle();

    final TextStyle effectiveErrorStyle =
        widget.errorStyle ??
        theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.error) ??
        TextStyle(color: theme.colorScheme.error);

    // Apply colors if specified
    final TextStyle finalTextStyle = effectiveTextStyle.copyWith(
      color:
          widget.isDisabled
              ? theme.disabledColor
              : (widget.textColor ?? effectiveTextStyle.color),
    );

    final TextStyle finalLabelStyle = effectiveLabelStyle.copyWith(
      color: widget.labelColor ?? effectiveLabelStyle.color,
    );

    final TextStyle finalHintStyle = effectiveHintStyle.copyWith(
      color: widget.hintColor ?? effectiveHintStyle.color,
    );

    final TextStyle finalHelperStyle = effectiveHelperStyle.copyWith(
      color: widget.helperColor ?? effectiveHelperStyle.color,
    );

    final TextStyle finalErrorStyle = effectiveErrorStyle.copyWith(
      color: widget.errorColor ?? effectiveErrorStyle.color,
    );

    // Define border colors
    final Color effectiveBorderColor = widget.borderColor ?? theme.dividerColor;
    final Color effectiveFocusedBorderColor =
        widget.focusedBorderColor ?? theme.colorScheme.primary;
    final Color effectiveErrorBorderColor =
        widget.errorBorderColor ?? theme.colorScheme.error;

    // Define icon colors
    final Color effectiveClearButtonColor =
        widget.clearButtonColor ?? theme.iconTheme.color ?? Colors.grey;
    final Color effectiveCopyButtonColor =
        widget.copyButtonColor ?? theme.iconTheme.color ?? Colors.grey;
    final Color effectivePasteButtonColor =
        widget.pasteButtonColor ?? theme.iconTheme.color ?? Colors.grey;

    // Build custom JSON elements if needed
    List<Widget> customElements = [];
    if (widget.showJsonElements &&
        _jsonCustomElements != null &&
        _jsonCustomElements!.isNotEmpty) {
      for (final element in _jsonCustomElements!) {
        customElements.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              element,
              style: TextStyle(
                color: widget.textColor ?? theme.textTheme.bodyMedium?.color,
                fontSize: 14.0,
              ),
            ),
          ),
        );
      }
    }

    // Build the input field
    return MouseRegion(
      onEnter: (_) => _onHoverChange(true),
      onExit: (_) => _onHoverChange(false),
      child: Tooltip(
        message: widget.tooltip ?? '',
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.labelText != null) ...[
              Text(widget.labelText!, style: finalLabelStyle),
              const SizedBox(height: 4),
            ],
            if (widget.characterCountPosition == CharacterCountPosition.above &&
                widget.showCharacterCount) ...[
              Align(
                alignment: Alignment.centerRight,
                child: _buildCharacterCount(),
              ),
              const SizedBox(height: 4),
            ],
            Container(
              width: widget.width,
              height: widget.height,
              margin: widget.margin,
              decoration: BoxDecoration(
                color:
                    widget.isDisabled
                        ? theme.disabledColor.withAlpha(26) // 0.1 opacity
                        : widget.backgroundColor,
                borderRadius: BorderRadius.circular(widget.borderRadius),
                border: Border.all(
                  color:
                      _hasError
                          ? effectiveErrorBorderColor
                          : (_hasFocus
                              ? effectiveFocusedBorderColor
                              : effectiveBorderColor),
                  width:
                      _hasError
                          ? widget.errorBorderWidth
                          : (_hasFocus
                              ? widget.focusedBorderWidth
                              : widget.borderWidth),
                ),
                boxShadow:
                    widget.hasShadow
                        ? [
                          BoxShadow(
                            color:
                                widget.shadowColor ??
                                Colors.black.withAlpha(26),
                            blurRadius: widget.elevation,
                            offset: Offset(0, widget.elevation / 2),
                          ),
                        ]
                        : null,
              ),
              child: Stack(
                children: [
                  TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    style: finalTextStyle,
                    decoration: InputDecoration(
                      hintText: widget.hintText,
                      hintStyle: finalHintStyle,
                      border: InputBorder.none,
                      contentPadding: widget.padding,
                      isDense: true,
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (widget.showCopyButton &&
                              _controller.text.isNotEmpty) ...[
                            IconButton(
                              icon: const Icon(Icons.copy),
                              iconSize: widget.copyButtonSize,
                              color:
                                  widget.isDisabled
                                      ? theme.disabledColor
                                      : effectiveCopyButtonColor,
                              onPressed:
                                  widget.isDisabled || widget.readOnly
                                      ? null
                                      : _handleCopy,
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                            ),
                          ],
                          if (widget.showPasteButton) ...[
                            IconButton(
                              icon: const Icon(Icons.paste),
                              iconSize: widget.pasteButtonSize,
                              color:
                                  widget.isDisabled
                                      ? theme.disabledColor
                                      : effectivePasteButtonColor,
                              onPressed:
                                  widget.isDisabled || widget.readOnly
                                      ? null
                                      : _handlePaste,
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                            ),
                          ],
                          if (widget.showClearButton &&
                              _controller.text.isNotEmpty) ...[
                            IconButton(
                              icon: const Icon(Icons.clear),
                              iconSize: widget.clearButtonSize,
                              color:
                                  widget.isDisabled
                                      ? theme.disabledColor
                                      : effectiveClearButtonColor,
                              onPressed:
                                  widget.isDisabled || widget.readOnly
                                      ? null
                                      : _handleClear,
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    keyboardType: widget.keyboardType,
                    textInputAction: widget.textInputAction,
                    textAlign: widget.textAlign,
                    textDirection: widget.textDirection,
                    textCapitalization: widget.textCapitalization,
                    readOnly: widget.readOnly || widget.isDisabled,
                    enabled: !widget.isDisabled,
                    maxLength:
                        widget.showCharacterCount ? null : widget.maxLength,
                    maxLengthEnforcement: MaxLengthEnforcement.enforced,
                    minLines: widget.minLines,
                    maxLines: widget.maxLines ?? (widget.expands ? null : 5),
                    expands: widget.expands,
                    cursorColor: widget.cursorColor,
                    enableSuggestions: widget.enableSuggestions,
                    enableIMEPersonalizedLearning:
                        widget.enableIMEPersonalizedLearning,
                    enableInteractiveSelection:
                        widget.enableInteractiveSelection,
                    inputFormatters: widget.inputFormatters,
                    onChanged: (value) {
                      if (widget.autoValidate) {
                        _validate(value);
                      }

                      if (widget.onChanged != null) {
                        widget.onChanged!(value);
                      }
                    },
                    onSubmitted: (value) {
                      final isValid = _validate(value);

                      if (widget.onSubmitted != null && isValid) {
                        widget.onSubmitted!(value);
                      }
                    },
                  ),
                  if (widget.characterCountPosition ==
                          CharacterCountPosition.inside &&
                      widget.showCharacterCount) ...[
                    Positioned(
                      right: 8,
                      bottom: 8,
                      child: _buildCharacterCount(),
                    ),
                  ],
                ],
              ),
            ),
            if (_errorText != null) ...[
              const SizedBox(height: 4),
              Text(_errorText!, style: finalErrorStyle),
            ] else if (widget.helperText != null) ...[
              const SizedBox(height: 4),
              Text(widget.helperText!, style: finalHelperStyle),
            ],
            if (widget.characterCountPosition == CharacterCountPosition.below &&
                widget.showCharacterCount) ...[
              const SizedBox(height: 4),
              Align(
                alignment: Alignment.centerRight,
                child: _buildCharacterCount(),
              ),
            ],
            // Add custom JSON elements if available
            ...customElements,
          ],
        ),
      ),
    );
  }
}
