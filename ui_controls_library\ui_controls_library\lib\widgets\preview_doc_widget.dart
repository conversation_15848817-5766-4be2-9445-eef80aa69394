import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// Document preview widget with various customization options
///
/// This widget provides a document preview with features like:
/// - Zoom controls
/// - Pagination
/// - Thumbnail navigation
/// - Search functionality
/// - Customizable appearance
class PreviewDocWidget extends StatefulWidget {
  /// The document source URL or path
  final String? documentSource;

  /// Whether to show zoom controls
  final bool showZoomControls;

  /// Whether to show pagination controls
  final bool showPagination;

  /// Whether to show thumbnail navigation
  final bool showThumbnails;

  /// Whether to show search functionality
  final bool showSearch;

  /// The theme color for the preview
  final Color themeColor;

  /// The background color for the preview
  final Color backgroundColor;

  /// The text color for the preview
  final Color textColor;

  /// The border radius for the preview container
  final double borderRadius;

  /// Whether to show a border around the preview
  final bool hasBorder;

  /// The border color for the preview
  final Color borderColor;

  /// The border width for the preview
  final double borderWidth;

  /// Whether to show a shadow under the preview
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The width of the preview
  final double? width;

  /// The height of the preview
  final double? height;

  /// The padding around the preview
  final EdgeInsetsGeometry padding;

  /// The margin around the preview
  final EdgeInsetsGeometry margin;

  /// The initial zoom level
  final double initialZoom;

  /// The minimum zoom level
  final double minZoom;

  /// The maximum zoom level
  final double maxZoom;

  /// The initial page to display
  final int initialPage;

  /// The total number of pages
  final int totalPages;

  /// Whether to show a toolbar
  final bool showToolbar;

  /// Whether to show a download button
  final bool showDownloadButton;

  /// Whether to show a print button
  final bool showPrintButton;

  /// Whether to show a share button
  final bool showShareButton;

  /// Whether to show a fullscreen button
  final bool showFullscreenButton;

  /// Whether to show page numbers
  final bool showPageNumbers;

  /// Whether to show a loading indicator
  final bool showLoadingIndicator;

  /// The loading indicator color
  final Color loadingIndicatorColor;

  /// Whether to show error messages
  final bool showErrorMessages;

  /// The error message text
  final String errorMessage;

  /// Whether to enable document rotation
  final bool enableRotation;

  /// The initial rotation angle in degrees
  final double initialRotation;

  /// Whether to fit the document to width
  final bool fitToWidth;

  /// Whether to fit the document to height
  final bool fitToHeight;

  /// Whether to show a document title
  final bool showTitle;

  /// The document title
  final String title;

  /// The title text style
  final TextStyle? titleStyle;

  /// Whether to show a document description
  final bool showDescription;

  /// The document description
  final String description;

  /// The description text style
  final TextStyle? descriptionStyle;

  /// Whether to enable continuous scrolling
  final bool continuousScrolling;

  /// Whether to enable horizontal scrolling
  final bool horizontalScrolling;

  /// Whether to show a grid view
  final bool showGridView;

  /// The number of columns in grid view
  final int gridColumns;

  /// Whether to show a document outline
  final bool showOutline;

  /// Whether to show annotations
  final bool showAnnotations;

  /// Whether to enable annotations
  final bool enableAnnotations;

  /// Whether to show a bookmark button
  final bool showBookmarkButton;

  /// Whether to show bookmarked pages
  final bool showBookmarks;

  /// The callback when a page is changed
  final Function(int)? onPageChanged;

  /// The callback when zoom level is changed
  final Function(double)? onZoomChanged;

  /// The callback when a document is downloaded
  final VoidCallback? onDownload;

  /// The callback when a document is printed
  final VoidCallback? onPrint;

  /// The callback when a document is shared
  final VoidCallback? onShare;

  /// The callback when a document is viewed in fullscreen
  final VoidCallback? onFullscreen;

  /// The callback when a document is bookmarked
  final Function(int)? onBookmark;

  /// The callback when a search is performed
  final Function(String)? onSearch;

  /// The callback when an annotation is added
  final VoidCallback? onAnnotate;

  /// The callback when a document fails to load
  final Function(String)? onError;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Document-specific JSON configuration
  /// Whether to use JSON document configuration
  final bool useJsonDocumentConfig;

  /// Document-specific JSON configuration
  final Map<String, dynamic>? documentConfig;

  /// Creates a document preview widget.
  const PreviewDocWidget({
    super.key,
    this.documentSource,
    this.showZoomControls = false,
    this.showPagination = false,
    this.showThumbnails = false,
    this.showSearch = false,
    this.themeColor = const Color(0xFF0058FF),
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black,
    this.borderRadius = 8.0,
    this.hasBorder = true,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(16.0),
    this.margin = const EdgeInsets.all(0),
    this.initialZoom = 1.0,
    this.minZoom = 0.5,
    this.maxZoom = 3.0,
    this.initialPage = 1,
    this.totalPages = 5,
    this.showToolbar = true,
    this.showDownloadButton = false,
    this.showPrintButton = false,
    this.showShareButton = false,
    this.showFullscreenButton = false,
    this.showPageNumbers = true,
    this.showLoadingIndicator = true,
    this.loadingIndicatorColor = const Color(0xFF0058FF),
    this.showErrorMessages = true,
    this.errorMessage = 'Failed to load document',
    this.enableRotation = false,
    this.initialRotation = 0,
    this.fitToWidth = false,
    this.fitToHeight = false,
    this.showTitle = false,
    this.title = 'Document Preview',
    this.titleStyle,
    this.showDescription = false,
    this.description = 'Document description',
    this.descriptionStyle,
    this.continuousScrolling = false,
    this.horizontalScrolling = false,
    this.showGridView = false,
    this.gridColumns = 2,
    this.showOutline = false,
    this.showAnnotations = false,
    this.enableAnnotations = false,
    this.showBookmarkButton = false,
    this.showBookmarks = false,
    this.onPageChanged,
    this.onZoomChanged,
    this.onDownload,
    this.onPrint,
    this.onShare,
    this.onFullscreen,
    this.onBookmark,
    this.onSearch,
    this.onAnnotate,
    this.onError,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Document-specific JSON configuration
    this.useJsonDocumentConfig = false,
    this.documentConfig,
  });

  /// Creates a PreviewDocWidget from a JSON map
  ///
  /// This factory constructor allows for creating a PreviewDocWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory PreviewDocWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color themeColor = Color(0xFF0058FF);
    if (json.containsKey('themeColor')) {
      themeColor = _parseColor(json['themeColor']);
    }

    Color backgroundColor = Colors.white;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color textColor = Colors.black;
    if (json.containsKey('textColor')) {
      textColor = _parseColor(json['textColor']);
    }

    Color borderColor = Colors.grey;
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    Color loadingIndicatorColor = Color(0xFF0058FF);
    if (json.containsKey('loadingIndicatorColor')) {
      loadingIndicatorColor = _parseColor(json['loadingIndicatorColor']);
    }

    Color? hoverColor;
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0);
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = const EdgeInsets.all(0.0);
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    // Parse text styles
    TextStyle? titleStyle;
    if (json.containsKey('titleStyle')) {
      titleStyle = _parseTextStyle(json['titleStyle'], textColor);
    }

    TextStyle? descriptionStyle;
    if (json.containsKey('descriptionStyle')) {
      descriptionStyle = _parseTextStyle(json['descriptionStyle'], textColor);
    }

    return PreviewDocWidget(
      // Basic properties
      documentSource: json['documentSource'] as String?,

      // Display properties
      showZoomControls: json['showZoomControls'] as bool? ?? false,
      showPagination: json['showPagination'] as bool? ?? false,
      showThumbnails: json['showThumbnails'] as bool? ?? false,
      showSearch: json['showSearch'] as bool? ?? false,
      themeColor: themeColor,
      backgroundColor: backgroundColor,
      textColor: textColor,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 8.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      borderColor: borderColor,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      padding: padding,
      margin: margin,

      // Zoom properties
      initialZoom:
          json['initialZoom'] != null
              ? (json['initialZoom'] as num).toDouble()
              : 1.0,
      minZoom:
          json['minZoom'] != null ? (json['minZoom'] as num).toDouble() : 0.5,
      maxZoom:
          json['maxZoom'] != null ? (json['maxZoom'] as num).toDouble() : 3.0,

      // Page properties
      initialPage: json['initialPage'] as int? ?? 1,
      totalPages: json['totalPages'] as int? ?? 5,

      // Toolbar properties
      showToolbar: json['showToolbar'] as bool? ?? true,
      showDownloadButton: json['showDownloadButton'] as bool? ?? false,
      showPrintButton: json['showPrintButton'] as bool? ?? false,
      showShareButton: json['showShareButton'] as bool? ?? false,
      showFullscreenButton: json['showFullscreenButton'] as bool? ?? false,
      showPageNumbers: json['showPageNumbers'] as bool? ?? true,

      // Loading properties
      showLoadingIndicator: json['showLoadingIndicator'] as bool? ?? true,
      loadingIndicatorColor: loadingIndicatorColor,
      showErrorMessages: json['showErrorMessages'] as bool? ?? true,
      errorMessage:
          json['errorMessage'] as String? ?? 'Failed to load document',

      // Rotation properties
      enableRotation: json['enableRotation'] as bool? ?? false,
      initialRotation:
          json['initialRotation'] != null
              ? (json['initialRotation'] as num).toDouble()
              : 0.0,

      // Fit properties
      fitToWidth: json['fitToWidth'] as bool? ?? false,
      fitToHeight: json['fitToHeight'] as bool? ?? false,

      // Title properties
      showTitle: json['showTitle'] as bool? ?? false,
      title: json['title'] as String? ?? 'Document Preview',
      titleStyle: titleStyle,

      // Description properties
      showDescription: json['showDescription'] as bool? ?? false,
      description: json['description'] as String? ?? 'Document description',
      descriptionStyle: descriptionStyle,

      // Scrolling properties
      continuousScrolling: json['continuousScrolling'] as bool? ?? false,
      horizontalScrolling: json['horizontalScrolling'] as bool? ?? false,

      // View properties
      showGridView: json['showGridView'] as bool? ?? false,
      gridColumns: json['gridColumns'] as int? ?? 2,

      // Outline properties
      showOutline: json['showOutline'] as bool? ?? false,

      // Annotation properties
      showAnnotations: json['showAnnotations'] as bool? ?? false,
      enableAnnotations: json['enableAnnotations'] as bool? ?? false,

      // Bookmark properties
      showBookmarkButton: json['showBookmarkButton'] as bool? ?? false,
      showBookmarks: json['showBookmarks'] as bool? ?? false,

      // Callback properties
      onPageChanged: json.containsKey('onPageChanged') ? (_) {} : null,
      onZoomChanged: json.containsKey('onZoomChanged') ? (_) {} : null,
      onDownload: json.containsKey('onDownload') ? () {} : null,
      onPrint: json.containsKey('onPrint') ? () {} : null,
      onShare: json.containsKey('onShare') ? () {} : null,
      onFullscreen: json.containsKey('onFullscreen') ? () {} : null,
      onBookmark: json.containsKey('onBookmark') ? (_) {} : null,
      onSearch: json.containsKey('onSearch') ? (_) {} : null,
      onAnnotate: json.containsKey('onAnnotate') ? () {} : null,
      onError: json.containsKey('onError') ? (_) {} : null,

      // Advanced interaction properties
      onHover: json.containsKey('onHover') ? (_) {} : null,
      onFocus: json.containsKey('onFocus') ? (_) {} : null,
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: hoverColor,
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onDoubleTap: json.containsKey('onDoubleTap') ? () {} : null,
      onLongPress: json.containsKey('onLongPress') ? () {} : null,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      callbackState:
          json.containsKey('callbackState')
              ? json['callbackState'] as Map<String, dynamic>
              : null,
      jsonConfig: json,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonDocumentConfig: json['useJsonDocumentConfig'] as bool? ?? false,
      documentConfig:
          json.containsKey('documentConfig')
              ? json['documentConfig'] as Map<String, dynamic>
              : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          default:
            return Colors.blue;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return Color(0xFF0058FF); // Default color
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') ||
          value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal:
              value.containsKey('horizontal')
                  ? (value['horizontal'] as num).toDouble()
                  : 0.0,
          vertical:
              value.containsKey('vertical')
                  ? (value['vertical'] as num).toDouble()
                  : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom')
              ? (value['bottom'] as num).toDouble()
              : 0.0,
        );
      }
    }
    return const EdgeInsets.all(16.0); // Default padding
  }

  /// Parses a text style from a map
  static TextStyle? _parseTextStyle(dynamic value, Color defaultColor) {
    if (value is! Map) return null;

    Color color = defaultColor;
    if (value.containsKey('color')) {
      color = _parseColor(value['color']);
    }

    double fontSize = 14.0;
    if (value.containsKey('fontSize')) {
      fontSize = (value['fontSize'] as num).toDouble();
    }

    FontWeight fontWeight = FontWeight.normal;
    if (value.containsKey('fontWeight')) {
      if (value['fontWeight'] is String) {
        switch ((value['fontWeight'] as String).toLowerCase()) {
          case 'thin':
            fontWeight = FontWeight.w100;
            break;
          case 'extralight':
            fontWeight = FontWeight.w200;
            break;
          case 'light':
            fontWeight = FontWeight.w300;
            break;
          case 'regular':
            fontWeight = FontWeight.w400;
            break;
          case 'medium':
            fontWeight = FontWeight.w500;
            break;
          case 'semibold':
            fontWeight = FontWeight.w600;
            break;
          case 'bold':
            fontWeight = FontWeight.w700;
            break;
          case 'extrabold':
            fontWeight = FontWeight.w800;
            break;
          case 'black':
            fontWeight = FontWeight.w900;
            break;
        }
      } else if (value['fontWeight'] is int) {
        final int weight = value['fontWeight'] as int;
        switch (weight) {
          case 100:
            fontWeight = FontWeight.w100;
            break;
          case 200:
            fontWeight = FontWeight.w200;
            break;
          case 300:
            fontWeight = FontWeight.w300;
            break;
          case 400:
            fontWeight = FontWeight.w400;
            break;
          case 500:
            fontWeight = FontWeight.w500;
            break;
          case 600:
            fontWeight = FontWeight.w600;
            break;
          case 700:
            fontWeight = FontWeight.w700;
            break;
          case 800:
            fontWeight = FontWeight.w800;
            break;
          case 900:
            fontWeight = FontWeight.w900;
            break;
        }
      }
    }

    FontStyle fontStyle = FontStyle.normal;
    if (value.containsKey('fontStyle')) {
      if (value['fontStyle'] is String &&
          (value['fontStyle'] as String).toLowerCase() == 'italic') {
        fontStyle = FontStyle.italic;
      }
    }

    TextDecoration decoration = TextDecoration.none;
    if (value.containsKey('decoration')) {
      if (value['decoration'] is String) {
        switch ((value['decoration'] as String).toLowerCase()) {
          case 'underline':
            decoration = TextDecoration.underline;
            break;
          case 'overline':
            decoration = TextDecoration.overline;
            break;
          case 'linethrough':
            decoration = TextDecoration.lineThrough;
            break;
        }
      }
    }

    return TextStyle(
      color: color,
      fontSize: fontSize,
      fontWeight: fontWeight,
      fontStyle: fontStyle,
      decoration: decoration,
    );
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'documentSource': documentSource,

      // Display properties
      'showZoomControls': showZoomControls,
      'showPagination': showPagination,
      'showThumbnails': showThumbnails,
      'showSearch': showSearch,
      'themeColor': '#${themeColor.toHexString()}',
      'backgroundColor': '#${backgroundColor.toHexString()}',
      'textColor': '#${textColor.toHexString()}',
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'borderColor': '#${borderColor.toHexString()}',
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'width': width,
      'height': height,

      // Zoom properties
      'initialZoom': initialZoom,
      'minZoom': minZoom,
      'maxZoom': maxZoom,

      // Page properties
      'initialPage': initialPage,
      'totalPages': totalPages,

      // Toolbar properties
      'showToolbar': showToolbar,
      'showDownloadButton': showDownloadButton,
      'showPrintButton': showPrintButton,
      'showShareButton': showShareButton,
      'showFullscreenButton': showFullscreenButton,
      'showPageNumbers': showPageNumbers,

      // Loading properties
      'showLoadingIndicator': showLoadingIndicator,
      'loadingIndicatorColor': '#${loadingIndicatorColor.toHexString()}',
      'showErrorMessages': showErrorMessages,
      'errorMessage': errorMessage,

      // Rotation properties
      'enableRotation': enableRotation,
      'initialRotation': initialRotation,

      // Fit properties
      'fitToWidth': fitToWidth,
      'fitToHeight': fitToHeight,

      // Title properties
      'showTitle': showTitle,
      'title': title,

      // Description properties
      'showDescription': showDescription,
      'description': description,

      // Scrolling properties
      'continuousScrolling': continuousScrolling,
      'horizontalScrolling': horizontalScrolling,

      // View properties
      'showGridView': showGridView,
      'gridColumns': gridColumns,

      // Outline properties
      'showOutline': showOutline,

      // Annotation properties
      'showAnnotations': showAnnotations,
      'enableAnnotations': enableAnnotations,

      // Bookmark properties
      'showBookmarkButton': showBookmarkButton,
      'showBookmarks': showBookmarks,

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonDocumentConfig': useJsonDocumentConfig,
    };
  }

  @override
  State<PreviewDocWidget> createState() => _PreviewDocWidgetState();
}

class _PreviewDocWidgetState extends State<PreviewDocWidget> {
  late double _currentZoom;
  late int _currentPage;
  late double _currentRotation;
  bool _isLoading = true;
  bool _hasError = false;
  bool _isFullscreen = false;
  final TextEditingController _searchController = TextEditingController();
  final List<int> _bookmarkedPages = [];
  List<String> _searchResults = [];
  int _currentSearchIndex = 0;

  // Interaction state
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _currentZoom = widget.initialZoom;
    _currentPage = widget.initialPage;
    _currentRotation = widget.initialRotation;

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    // Simulate document loading
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;

          // Simulate error if no document source
          if (widget.documentSource == null || widget.documentSource!.isEmpty) {
            _hasError = true;
          }
        });
      }
    });
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused && widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  @override
  void didUpdateWidget(PreviewDocWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  void _zoomIn() {
    setState(() {
      _currentZoom = math.min(widget.maxZoom, _currentZoom + 0.25);
      if (widget.onZoomChanged != null) {
        widget.onZoomChanged!(_currentZoom);
      }
    });
  }

  void _zoomOut() {
    setState(() {
      _currentZoom = math.max(widget.minZoom, _currentZoom - 0.25);
      if (widget.onZoomChanged != null) {
        widget.onZoomChanged!(_currentZoom);
      }
    });
  }

  void _resetZoom() {
    setState(() {
      _currentZoom = 1.0;
      if (widget.onZoomChanged != null) {
        widget.onZoomChanged!(_currentZoom);
      }
    });
  }

  void _nextPage() {
    if (_currentPage < widget.totalPages) {
      setState(() {
        _currentPage++;
        if (widget.onPageChanged != null) {
          widget.onPageChanged!(_currentPage);
        }
      });
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
        if (widget.onPageChanged != null) {
          widget.onPageChanged!(_currentPage);
        }
      });
    }
  }

  void _goToPage(int page) {
    if (page >= 1 && page <= widget.totalPages) {
      setState(() {
        _currentPage = page;
        if (widget.onPageChanged != null) {
          widget.onPageChanged!(_currentPage);
        }
      });
    }
  }

  void _rotateLeft() {
    setState(() {
      _currentRotation = (_currentRotation - 90) % 360;
    });
  }

  void _rotateRight() {
    setState(() {
      _currentRotation = (_currentRotation + 90) % 360;
    });
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
      if (widget.onFullscreen != null) {
        widget.onFullscreen!();
      }
    });
  }

  void _toggleBookmark() {
    setState(() {
      if (_bookmarkedPages.contains(_currentPage)) {
        _bookmarkedPages.remove(_currentPage);
      } else {
        _bookmarkedPages.add(_currentPage);
      }
      if (widget.onBookmark != null) {
        widget.onBookmark!(_currentPage);
      }
    });
  }

  void _search(String query) {
    if (query.isEmpty) {
      setState(() {
        _searchResults.clear();
        _currentSearchIndex = 0;
      });
      return;
    }

    // Simulate search results
    setState(() {
      _searchResults = List.generate(
        math.Random().nextInt(10) + 1,
        (index) => 'Result ${index + 1}',
      );
      _currentSearchIndex = 0;
    });

    if (widget.onSearch != null) {
      widget.onSearch!(query);
    }
  }

  void _nextSearchResult() {
    if (_searchResults.isNotEmpty) {
      setState(() {
        _currentSearchIndex = (_currentSearchIndex + 1) % _searchResults.length;
      });
    }
  }

  void _previousSearchResult() {
    if (_searchResults.isNotEmpty) {
      setState(() {
        _currentSearchIndex =
            (_currentSearchIndex - 1 + _searchResults.length) %
            _searchResults.length;
      });
    }
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.hasBorder
                ? Border.all(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                )
                : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.showZoomControls) ...[
            IconButton(
              icon: Icon(Icons.zoom_out, color: widget.themeColor),
              onPressed: _zoomOut,
              tooltip: 'Zoom Out',
            ),
            Text(
              '${(_currentZoom * 100).round()}%',
              style: TextStyle(color: widget.textColor),
            ),
            IconButton(
              icon: Icon(Icons.zoom_in, color: widget.themeColor),
              onPressed: _zoomIn,
              tooltip: 'Zoom In',
            ),
            IconButton(
              icon: Icon(Icons.refresh, color: widget.themeColor),
              onPressed: _resetZoom,
              tooltip: 'Reset Zoom',
            ),
            const SizedBox(width: 8),
            const VerticalDivider(),
            const SizedBox(width: 8),
          ],
          if (widget.showPagination) ...[
            IconButton(
              icon: Icon(Icons.arrow_back, color: widget.themeColor),
              onPressed: _currentPage > 1 ? _previousPage : null,
              tooltip: 'Previous Page',
            ),
            if (widget.showPageNumbers)
              Text(
                '$_currentPage / ${widget.totalPages}',
                style: TextStyle(color: widget.textColor),
              ),
            IconButton(
              icon: Icon(Icons.arrow_forward, color: widget.themeColor),
              onPressed: _currentPage < widget.totalPages ? _nextPage : null,
              tooltip: 'Next Page',
            ),
            const SizedBox(width: 8),
            const VerticalDivider(),
            const SizedBox(width: 8),
          ],
          if (widget.enableRotation) ...[
            IconButton(
              icon: Icon(Icons.rotate_left, color: widget.themeColor),
              onPressed: _rotateLeft,
              tooltip: 'Rotate Left',
            ),
            IconButton(
              icon: Icon(Icons.rotate_right, color: widget.themeColor),
              onPressed: _rotateRight,
              tooltip: 'Rotate Right',
            ),
            const SizedBox(width: 8),
            const VerticalDivider(),
            const SizedBox(width: 8),
          ],
          if (widget.showDownloadButton)
            IconButton(
              icon: Icon(Icons.download, color: widget.themeColor),
              onPressed: widget.onDownload,
              tooltip: 'Download',
            ),
          if (widget.showPrintButton)
            IconButton(
              icon: Icon(Icons.print, color: widget.themeColor),
              onPressed: widget.onPrint,
              tooltip: 'Print',
            ),
          if (widget.showShareButton)
            IconButton(
              icon: Icon(Icons.share, color: widget.themeColor),
              onPressed: widget.onShare,
              tooltip: 'Share',
            ),
          if (widget.showFullscreenButton)
            IconButton(
              icon: Icon(
                _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                color: widget.themeColor,
              ),
              onPressed: _toggleFullscreen,
              tooltip: _isFullscreen ? 'Exit Fullscreen' : 'Fullscreen',
            ),
          if (widget.showBookmarkButton)
            IconButton(
              icon: Icon(
                _bookmarkedPages.contains(_currentPage)
                    ? Icons.bookmark
                    : Icons.bookmark_border,
                color: widget.themeColor,
              ),
              onPressed: _toggleBookmark,
              tooltip:
                  _bookmarkedPages.contains(_currentPage)
                      ? 'Remove Bookmark'
                      : 'Add Bookmark',
            ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.hasBorder
                ? Border.all(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                )
                : null,
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                border: InputBorder.none,
                hintStyle: TextStyle(color: widget.textColor.withAlpha(128)),
              ),
              style: TextStyle(color: widget.textColor),
              onSubmitted: _search,
            ),
          ),
          IconButton(
            icon: Icon(Icons.search, color: widget.themeColor),
            onPressed: () => _search(_searchController.text),
            tooltip: 'Search',
          ),
          if (_searchResults.isNotEmpty) ...[
            IconButton(
              icon: Icon(Icons.arrow_upward, color: widget.themeColor),
              onPressed: _previousSearchResult,
              tooltip: 'Previous Result',
            ),
            Text(
              '${_currentSearchIndex + 1}/${_searchResults.length}',
              style: TextStyle(color: widget.textColor),
            ),
            IconButton(
              icon: Icon(Icons.arrow_downward, color: widget.themeColor),
              onPressed: _nextSearchResult,
              tooltip: 'Next Result',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildThumbnails() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.hasBorder
                ? Border.all(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                )
                : null,
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.totalPages,
        itemBuilder: (context, index) {
          final page = index + 1;
          return GestureDetector(
            onTap: () => _goToPage(page),
            child: Container(
              width: 80,
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(
                  color:
                      _currentPage == page
                          ? widget.themeColor
                          : widget.borderColor,
                  width: _currentPage == page ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Text(
                      'Page $page',
                      style: TextStyle(
                        color: widget.textColor,
                        fontWeight:
                            _currentPage == page
                                ? FontWeight.bold
                                : FontWeight.normal,
                      ),
                    ),
                  ),
                  if (_bookmarkedPages.contains(page))
                    Positioned(
                      top: 0,
                      right: 0,
                      child: Icon(
                        Icons.bookmark,
                        color: widget.themeColor,
                        size: 16,
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDocumentPreview() {
    if (_isLoading && widget.showLoadingIndicator) {
      return Center(
        child: CircularProgressIndicator(color: widget.loadingIndicatorColor),
      );
    }

    if (_hasError && widget.showErrorMessages) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              widget.errorMessage,
              style: TextStyle(color: widget.textColor, fontSize: 16),
            ),
          ],
        ),
      );
    }

    return Center(
      child: Transform.rotate(
        angle: _currentRotation * math.pi / 180,
        child: Transform.scale(
          scale: _currentZoom,
          child: Container(
            width: 300,
            height: 400,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Stack(
              children: [
                Center(
                  child: Text(
                    'Page $_currentPage',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (_searchResults.isNotEmpty)
                  Positioned(
                    top: 100 + (_currentSearchIndex * 30),
                    left: 50,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      color: Colors.yellow,
                      child: Text(
                        _searchResults[_currentSearchIndex],
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final effectiveWidth = widget.width ?? 600;
    final effectiveHeight = widget.height ?? 500;

    Widget content = Container(
      width: _isFullscreen ? double.infinity : effectiveWidth,
      height: _isFullscreen ? double.infinity : effectiveHeight,
      padding: widget.padding,
      margin: widget.margin,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.hasBorder
                ? Border.all(
                  color: widget.borderColor,
                  width: widget.borderWidth,
                )
                : null,
        boxShadow:
            widget.hasShadow
                ? [
                  BoxShadow(
                    color: Colors.black.withAlpha(51),
                    blurRadius: widget.elevation,
                    offset: Offset(0, widget.elevation / 2),
                  ),
                ]
                : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showTitle) ...[
            Text(
              widget.title,
              style:
                  widget.titleStyle ??
                  TextStyle(
                    color: widget.textColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
          ],
          if (widget.showDescription) ...[
            Text(
              widget.description,
              style:
                  widget.descriptionStyle ??
                  TextStyle(
                    color: widget.textColor.withAlpha(179),
                    fontSize: 14,
                  ),
            ),
            const SizedBox(height: 16),
          ],
          if (widget.showToolbar) ...[
            Center(child: _buildToolbar()),
            const SizedBox(height: 16),
          ],
          if (widget.showSearch) ...[
            _buildSearchBar(),
            const SizedBox(height: 16),
          ],
          Expanded(child: _buildDocumentPreview()),
          if (widget.showThumbnails) ...[
            const SizedBox(height: 16),
            _buildThumbnails(),
          ],
        ],
      ),
    );

    // Apply advanced interaction properties
    if (widget.onHover != null) {
      content = MouseRegion(
        onEnter: (event) {
          widget.onHover!(true);
        },
        onExit: (event) {
          widget.onHover!(false);
        },
        child: content,
      );
    }

    // Apply focus handling
    if (widget.autofocus || widget.onFocus != null) {
      content = Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        onFocusChange: widget.onFocus,
        child: content,
      );
    }

    // Apply advanced gesture detection
    if (widget.onDoubleTap != null || widget.onLongPress != null) {
      content = GestureDetector(
        onDoubleTap:
            widget.onDoubleTap != null
                ? () {
                  // Execute onDoubleTap callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                    _executeJsonCallback('onDoubleTap');
                  }

                  // Call standard callback
                  widget.onDoubleTap!();
                }
                : null,
        onLongPress:
            widget.onLongPress != null
                ? () {
                  // Execute onLongPress callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onLongPress')) {
                    _executeJsonCallback('onLongPress');
                  }

                  // Call standard callback
                  widget.onLongPress!();
                }
                : null,
        child: content,
      );
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      content = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: content,
      );
    }

    return content;
  }
}
