import 'dart:convert';
import '../models/solution.dart';
import '../models/mapping.dart';

class ImportServiceImpl {
  /// Import a solution and its mappings from a JSON string
  static Future<Map<String, dynamic>> importSolutionWithMappings(String jsonString) async {
    try {
      // Parse the JSON string
      final Map<String, dynamic> importData = jsonDecode(jsonString) as Map<String, dynamic>;
      
      // Extract solution data
      final Solution solution = Solution.fromJson(importData['solution']);
      
      // Extract mappings data
      final List<Mapping> mappings = [];
      
      // Process hierarchical mappings
      if (importData.containsKey('mappings')) {
        final List<dynamic> mappingsJson = importData['mappings'] as List<dynamic>;
        
        for (final mappingJson in mappingsJson) {
          // Add the parent mapping
          final Mapping parentMapping = Mapping.fromJson(mappingJson);
          mappings.add(parentMapping);
          
          // Process child mappings if present
          if (mappingJson.containsKey('children')) {
            final List<dynamic> childrenJson = mappingJson['children'] as List<dynamic>;
            for (final childJson in childrenJson) {
              // Create a copy of the child JSON with the parent ID
              final Map<String, dynamic> childWithParent = Map<String, dynamic>.from(childJson);
              childWithParent['parentId'] = parentMapping.id;
              
              // Create the child mapping with the parent ID
              final Mapping childMapping = Mapping.fromJson(childWithParent);
              mappings.add(childMapping);
            }
          }
        }
      }
      
      // Extract Java keywords
      final Map<String, String> javaKeywords = {};
      if (importData.containsKey('javaKeywords')) {
        final Map<String, dynamic> keywordsJson = importData['javaKeywords'] as Map<String, dynamic>;
        javaKeywords.addAll(keywordsJson.map((key, value) => MapEntry(key, value.toString())));
      }
      
      // Return the imported data
      return {
        'solution': solution,
        'mappings': mappings,
        'javaKeywords': javaKeywords,
      };
    } catch (e) {
      print('Error importing solution: $e');
      return {
        'error': e.toString(),
      };
    }
  }
}
