import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';

import 'package:nsl/screens/new_design/my_library_mobile/books_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/objects_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/mobile_nav_item.dart';

class SolutionMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;
  final String versionNumber;

  SolutionMobile({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.isDraft = false,
    this.imageWidth = 105.0,
    this.imageHeight = 140.0,
    this.versionNumber = "V00172",
  });

  factory SolutionMobile.fromJson(Map<String, dynamic> json) {
    return SolutionMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 105.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 140.0,
      versionNumber: json['versionNumber'] as String? ?? "V00172",
    );
  }
}

class SolutionsLibraryMobile extends StatefulWidget {
  const SolutionsLibraryMobile({super.key, this.showNavigationBar = true});

  final bool showNavigationBar;

  @override
  State<SolutionsLibraryMobile> createState() => _SolutionsLibraryMobileState();
}

class _SolutionsLibraryMobileState extends State<SolutionsLibraryMobile>
    with TickerProviderStateMixin {
  late List<SolutionMobile> solutions;
  bool isLoading = true;
  int selectedTabIndex = 1; // 0: Books, 1: Solutions, 2: Objects

  // Swiper variables
  int _currentPage = 0;
  List<List<SolutionMobile>> _solutionPages = [];
  List<List<SolutionMobile>> _compactSolutionPages =
      []; // For 3x2 layout when keyboard is open

  // Configuration
  static const int _solutionsPerPage = 4; // 2x2 layout (normal)
  static const int _compactSolutionsPerPage = 6; // 3x2 layout (keyboard open)

  // PageView controller and search focus
  late PageController _pageController;
  final FocusNode _searchFocusNode = FocusNode();
  bool _isKeyboardVisible = false;

  // Animation controllers for loading animation
  late AnimationController _loadingAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // JSON string containing solution data
  static const String solutionsJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Ecommerce Ecommerce",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Fashion & Apparel Fashion & Apparel Fashion & Apparel",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Financial Advisory Financial Advisory Financial Advisory",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals Home Rentals Home Rentals",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Online Grocery Online Grocery",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics Courier & Logistics Courier & Logistics",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Automotive Automotive Automotive",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness Fitness & Wellness Fitness & Wellness",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Real Estate Real Estate",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe Restaurant & Cafe Restaurant & Cafe",
      "subtitle": "(B2C)",
      "versionNumber": "V00172",
      "imageUrl": "assets/images/solution-placeholder.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _searchFocusNode.addListener(_onSearchFocusChange);

    // Initialize loading animation
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _loadSolutions();
  }

  void _onSearchFocusChange() {
    // Detect keyboard visibility by checking if search field is focused
    // and using MediaQuery in build method
    setState(() {
      // This will trigger a rebuild where we can check MediaQuery
    });
  }

  void _loadSolutions() {
    try {
      // Parse the JSON string
      final data = json.decode(solutionsJsonString);

      // Convert to SolutionMobile objects
      final List<SolutionMobile> loadedSolutions = (data['books'] as List)
          .map((solutionJson) => SolutionMobile.fromJson(solutionJson))
          .toList();

      // Create normal 2x2 layout pages
      _solutionPages = [];
      for (int i = 0; i < loadedSolutions.length; i += _solutionsPerPage) {
        int end = (i + _solutionsPerPage < loadedSolutions.length)
            ? i + _solutionsPerPage
            : loadedSolutions.length;
        _solutionPages.add(loadedSolutions.sublist(i, end));
      }

      // Create compact 3x2 layout pages for keyboard mode
      _compactSolutionPages = [];
      for (int i = 0;
          i < loadedSolutions.length;
          i += _compactSolutionsPerPage) {
        int end = (i + _compactSolutionsPerPage < loadedSolutions.length)
            ? i + _compactSolutionsPerPage
            : loadedSolutions.length;
        _compactSolutionPages.add(loadedSolutions.sublist(i, end));
      }

      setState(() {
        solutions = loadedSolutions;
        isLoading = false;
      });

      // Start loading animation
      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        solutions = [];
        isLoading = false;
      });
      // Log error in a production-safe way
      debugPrint('Error loading solutions: $e');
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _searchFocusNode.removeListener(_onSearchFocusChange);
    _searchFocusNode.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Detect keyboard visibility
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    // Update keyboard state if changed
    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _isKeyboardVisible = isKeyboardVisible;
          _currentPage = 0; // Reset to first page when layout changes
        });
      });
    }

    return Scaffold(
      backgroundColor:
          widget.showNavigationBar ? Color(0xfff6f6f6) : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildCustomSwiper(),
        ],
      ),
      floatingActionButton: widget.showNavigationBar
          ? SizedBox(
              width: 46,
              height: 46,
              child: FloatingActionButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateBookMobile(),
                    ),
                  );
                },
                backgroundColor: const Color(0xff0058FF),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(Icons.add),
              ),
            )
          : null,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          // Hamburger menu icon
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          // Expanded widget to center the title
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('websolution.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // Invisible spacer to balance the layout (same width as menu icon)
          const SizedBox(width: 56), // IconButton default width
        ],
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MobileNavItem(
            iconPath: 'assets/images/books-icon.svg',
            label: AppLocalizations.of(context).translate('library.books'),
            isActive: selectedTabIndex == 0,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const BooksLibraryMobile(),
                ),
              );
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/square-box-uncheck.svg',
            label: AppLocalizations.of(context).translate('library.solutions'),
            isActive: selectedTabIndex == 1,
            onTap: () {
              // Already on solutions screen, no navigation needed
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/cube-box.svg',
            label: AppLocalizations.of(context).translate('library.objects'),
            isActive: selectedTabIndex == 2,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ObjectsLibraryMobile(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCustomSwiper() {
    // Choose the appropriate page layout based on keyboard visibility
    final currentPages =
        _isKeyboardVisible ? _compactSolutionPages : _solutionPages;

    return Expanded(
      child: solutions.isEmpty
          ? const Center(child: Text('No solutions found'))
          : Column(
              children: [
                // PageView with dynamic layout (2x2 or 3x2)
                Expanded(
                  child: Container(
                    child: currentPages.isEmpty
                        ? const Center(child: Text('No solutions found'))
                        : PageView.builder(
                            controller: _pageController,
                            onPageChanged: (index) {
                              setState(() {
                                _currentPage = index;
                              });
                            },
                            itemCount: currentPages.length,
                            itemBuilder: (context, pageIndex) {
                              return _buildSolutionPage(
                                currentPages[pageIndex],
                                isCompactMode: _isKeyboardVisible,
                              );
                            },
                          ),
                  ),
                ),
                // Custom Dot Pagination
                if (currentPages.length > 1)
                  _buildCustomPagination(currentPages.length),
              ],
            ),
    );
  }

  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Search bar
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                // Search text field
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0),
                    child: TextField(
                      focusNode: _searchFocusNode,
                      decoration: InputDecoration(
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        hintText: 'Search',
                        border: InputBorder.none,
                        hintStyle:
                            TextStyle(fontSize: 14, color: Colors.grey[500]),
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ),
                // Search and filter icons
                _MobileSolutionSvgButton(
                  iconPath: 'assets/images/search.svg',
                  onPressed: () {},
                  size: 20,
                ),
                Container(
                  height: 24,
                  width: 1,
                  color: Colors.grey.shade200,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                ),
                _MobileSolutionSvgButton(
                  iconPath: 'assets/images/filter-icon.svg',
                  onPressed: () {},
                  size: 24,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          // const SizedBox(height: 12),
          // Create solution button
          // SizedBox(
          //   width: double.infinity,
          //   height: 44,
          //   child: ElevatedButton(
          //     onPressed: () {},
          //     style: ElevatedButton.styleFrom(
          //       backgroundColor: const Color(0xff0058FF),
          //       foregroundColor: Colors.white,
          //       elevation: 0,
          //       shape: RoundedRectangleBorder(
          //         borderRadius: BorderRadius.circular(6),
          //       ),
          //     ),
          //     child: Text(
          //       AppLocalizations.of(context)
          //           .translate('websolution.createButtonText'),
          //       style: const TextStyle(
          //         fontSize: 16,
          //         fontWeight: FontWeight.w500,
          //         fontFamily: 'TiemposText',
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildSolutionPage(List<SolutionMobile> pageSolutions,
      {bool isCompactMode = false}) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Define spacing and account for SVG background overflow
        const double horizontalSpacing = 16.0; // Between cards horizontally
        const double verticalSpacing = 20.0; // Between cards vertically
        const double leftPadding = 16.0; // Left edge padding
        const double rightPadding =
            16.0; // Right edge padding (no SVG overflow for solutions)
        const double topBottomPadding = 16.0; // Top/bottom padding
        const double textSpacePerCard =
            45.0; // Space for title + subtitle below each card

        // Calculate available space for cards
        double availableWidth =
            constraints.maxWidth - leftPadding - rightPadding;
        double availableHeight = constraints.maxHeight - (topBottomPadding * 2);

        // Calculate card width based on layout mode
        double cardWidth;
        if (isCompactMode) {
          // 3x2 layout: 3 cards per row
          cardWidth = (availableWidth - (horizontalSpacing * 2)) / 3;
        } else {
          // 2x2 layout: 2 cards per row
          cardWidth = (availableWidth - horizontalSpacing) / 2;
        }

        // Calculate what the height should be based on width to maintain aspect ratio (105:140)
        double idealHeightFromWidth = (cardWidth * 140) / 105;

        // Calculate available height for cards (leaving space for text and spacing)
        double availableCardHeight =
            availableHeight - (textSpacePerCard * 2) - verticalSpacing;
        double maxCardHeight = availableCardHeight / 2;

        // Use the smaller constraint to prevent overflow
        double cardHeight;
        if (idealHeightFromWidth <= maxCardHeight) {
          // Width constraint is more restrictive - use it
          cardHeight = idealHeightFromWidth;
        } else {
          // Height constraint is more restrictive - use it
          cardHeight = maxCardHeight;
          cardWidth = (cardHeight * 105) / 140;
        }

        // Ensure reasonable size limits based on layout mode
        if (isCompactMode) {
          // Compact mode: smaller cards to fit 3x2 layout
          if (cardWidth < 70) {
            cardWidth = 70;
            cardHeight = (cardWidth * 140) / 105;
          } else if (cardWidth > 100) {
            cardWidth = 100;
            cardHeight = (cardWidth * 140) / 105;
          }
        } else {
          // Normal mode: larger cards for 2x2 layout
          if (cardWidth < 90) {
            cardWidth = 90;
            cardHeight = (cardWidth * 140) / 105;
          } else if (cardWidth > 140) {
            cardWidth = 140;
            cardHeight = (cardWidth * 140) / 105;
          }
        }

        // Calculate total height needed including text space
        double totalCardHeight = cardHeight + textSpacePerCard;

        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(
              left: leftPadding,
              right: rightPadding,
              top: topBottomPadding,
              bottom: topBottomPadding,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: isCompactMode
                  ? _buildCompactLayout(pageSolutions, cardWidth, cardHeight,
                      totalCardHeight, verticalSpacing)
                  : _buildNormalLayout(pageSolutions, cardWidth, cardHeight,
                      totalCardHeight, verticalSpacing),
            ),
          ),
        );
      },
    );
  }

  // Normal 2x2 layout
  List<Widget> _buildNormalLayout(
      List<SolutionMobile> pageSolutions,
      double cardWidth,
      double cardHeight,
      double totalCardHeight,
      double verticalSpacing) {
    return [
      // First row
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (pageSolutions.isNotEmpty)
            _buildSolutionCard(pageSolutions[0], cardWidth, cardHeight)
          else
            SizedBox(width: cardWidth, height: totalCardHeight),
          if (pageSolutions.length > 1)
            _buildSolutionCard(pageSolutions[1], cardWidth, cardHeight)
          else
            SizedBox(width: cardWidth, height: totalCardHeight),
        ],
      ),
      SizedBox(height: verticalSpacing),
      // Second row
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (pageSolutions.length > 2)
            _buildSolutionCard(pageSolutions[2], cardWidth, cardHeight)
          else
            SizedBox(width: cardWidth, height: totalCardHeight),
          if (pageSolutions.length > 3)
            _buildSolutionCard(pageSolutions[3], cardWidth, cardHeight)
          else
            SizedBox(width: cardWidth, height: totalCardHeight),
        ],
      ),
    ];
  }

  // Compact 3x2 layout for keyboard mode
  List<Widget> _buildCompactLayout(
      List<SolutionMobile> pageSolutions,
      double cardWidth,
      double cardHeight,
      double totalCardHeight,
      double verticalSpacing) {
    return [
      // First row (3 solutions)
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (pageSolutions.isNotEmpty)
            _buildSolutionCard(pageSolutions[0], cardWidth, cardHeight)
          else
            SizedBox(width: cardWidth, height: totalCardHeight),
          if (pageSolutions.length > 1)
            _buildSolutionCard(pageSolutions[1], cardWidth, cardHeight)
          else
            SizedBox(width: cardWidth, height: totalCardHeight),
          if (pageSolutions.length > 2)
            _buildSolutionCard(pageSolutions[2], cardWidth, cardHeight)
          else
            SizedBox(width: cardWidth, height: totalCardHeight),
        ],
      ),
      SizedBox(height: verticalSpacing),
      // Second row (3 solutions)
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (pageSolutions.length > 3)
            _buildSolutionCard(pageSolutions[3], cardWidth, cardHeight)
          else
            SizedBox(width: cardWidth, height: totalCardHeight),
          if (pageSolutions.length > 4)
            _buildSolutionCard(pageSolutions[4], cardWidth, cardHeight)
          else
            SizedBox(width: cardWidth, height: totalCardHeight),
          if (pageSolutions.length > 5)
            _buildSolutionCard(pageSolutions[5], cardWidth, cardHeight)
          else
            SizedBox(width: cardWidth, height: totalCardHeight),
        ],
      ),
    ];
  }

  Widget _buildSolutionCard(
      SolutionMobile solution, double cardWidth, double cardHeight) {
    return AnimatedBuilder(
      animation: _loadingAnimationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: _MobileSolutionCard(
              onTap: () {
                // Handle solution tap
              },
              child: _buildMobileSolutionCard(
                title: solution.title,
                subtitle: solution.subtitle,
                imageUrl: solution.imageUrl,
                isDraft: solution.isDraft,
                imageWidth: cardWidth,
                imageHeight: cardHeight,
                versionNumber: solution.versionNumber,
                index: 0,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCustomPagination(int pageCount) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          pageCount,
          (index) => GestureDetector(
            onTap: () {
              _pageController.animateToPage(
                index,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: _currentPage == index ? 24 : 8,
              height: 8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: _currentPage == index
                    ? const Color(0xff0058FF)
                    : Colors.grey.shade300,
                boxShadow: _currentPage == index
                    ? [
                        BoxShadow(
                          color: const Color(0xff0058FF).withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMobileSolutionCard({
    required String title,
    required String subtitle,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 105.0,
    double imageHeight = 140.0,
    String versionNumber = "V00172",
    int index = 0,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Solution cover
        Stack(
          children: [
            Container(
              width: imageWidth,
              height: imageHeight,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(imageUrl),
                  fit: BoxFit.fill,
                ),
              ),
            ),
            if (isDraft)
              Positioned(
                top: imageHeight * 0.10,
                right: imageWidth * 0.14,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Draft',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: imageWidth,
          child: Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              height: 1.334,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          width: imageWidth,
          child: Text(
            'Version: 001',
            style: const TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 11,
              color: Colors.black,
              fontFamily: "TiemposText",
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

// Mobile Solution SVG Button Widget
class _MobileSolutionSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileSolutionSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileSolutionSvgButton> createState() =>
      _MobileSolutionSvgButtonState();
}

class _MobileSolutionSvgButtonState extends State<_MobileSolutionSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Solution Pagination Button Widget
class _MobileSolutionPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback onPressed;

  const _MobileSolutionPaginationButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  State<_MobileSolutionPaginationButton> createState() =>
      _MobileSolutionPaginationButtonState();
}

class _MobileSolutionPaginationButtonState
    extends State<_MobileSolutionPaginationButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.grey.shade300,
            width: 1.0,
          ),
          borderRadius: isPressed ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: Center(
          child: Icon(
            widget.icon.icon,
            color: isPressed ? const Color(0xff0058FF) : Colors.black,
            size: widget.icon.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Solution Card Widget
class _MobileSolutionCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _MobileSolutionCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_MobileSolutionCard> createState() => _MobileSolutionCardState();
}

class _MobileSolutionCardState extends State<_MobileSolutionCard> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: widget.child,
    );
  }
}
