import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/screens/new_design/my_library_mobile/solutions_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/objects_library_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/create_book_mobile.dart';
import 'package:nsl/screens/new_design/my_library_mobile/add_modules_mobileview.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:nsl/widgets/mobile/mobile_nav_item.dart';

class BookMobile {
  final String title;
  final String subtitle;
  final String imageUrl;
  final bool isDraft;
  final double imageWidth;
  final double imageHeight;

  BookMobile({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    this.isDraft = false,
    this.imageWidth = 101.0,
    this.imageHeight = 156.0,
  });

  factory BookMobile.fromJson(Map<String, dynamic> json) {
    return BookMobile(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      imageUrl: json['imageUrl'] as String,
      isDraft: json['isDraft'] as bool? ?? false,
      imageWidth: (json['imageWidth'] as num?)?.toDouble() ?? 101.0,
      imageHeight: (json['imageHeight'] as num?)?.toDouble() ?? 156.0,
    );
  }
}

class BooksLibraryMobile extends StatefulWidget {
  const BooksLibraryMobile({
    super.key,
    this.showNavigationBar = true,
  });

  final bool showNavigationBar;

  @override
  State<BooksLibraryMobile> createState() => _BooksLibraryMobileState();
}

class _BooksLibraryMobileState extends State<BooksLibraryMobile>
    with TickerProviderStateMixin {
  late List<BookMobile> books;
  bool isLoading = true;
  int selectedTabIndex = 0;

  // Horizontal slider variables
  int _currentIndex = 0;
  
  // Configuration for horizontal slider
  static const double _booksPerView = 2.5; // Show 2.5 books per view
  static const int _booksToScrollPerSwipe = 2; // Scroll 2 books at a time

  // PageView controller and search focus
  late PageController _pageController;
  final FocusNode _searchFocusNode = FocusNode();
  bool _isKeyboardVisible = false;

  // Animation controllers for loading animation
  late AnimationController _loadingAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // JSON data
  static const String booksJsonString = '''
{
  "books": [
    {
      "title": "Ecommerce Platform Solutions",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book_01.png",
      "isDraft": false
    },
    {
      "title": "Fashion & Apparel Store",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-02.png",
      "isDraft": false
    },
    {
      "title": "Financial Advisory Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-03.png",
      "isDraft": false
    },
    {
      "title": "Home Rentals App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": true
    },
    {
      "title": "Online Grocery Store",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-05.png",
      "isDraft": false
    },
    {
      "title": "Courier & Logistics",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-06.png",
      "isDraft": false
    },
    {
      "title": "Automotive Marketplace",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-07.png",
      "isDraft": true
    },
    {
      "title": "Fitness & Wellness App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-08.png",
      "isDraft": false
    },
    {
      "title": "Real Estate Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-09.png",
      "isDraft": false
    },
    {
      "title": "Restaurant & Cafe",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Healthcare Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Education Portal",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Travel Booking App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Music Streaming",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Social Media Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Gaming Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "News & Media App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Banking App",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Investment Platform",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Delivery Service",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Job Portal",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Event Management",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Video Streaming",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Smart Home IoT",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    },
    {
      "title": "Cryptocurrency Exchange",
      "subtitle": "(B2C)",
      "imageUrl": "assets/images/book-10.png",
      "isDraft": false
    }
  ]
}
''';

  @override
  void initState() {
    super.initState();
    _pageController = PageController(viewportFraction: 1.0 / _booksPerView);
    _searchFocusNode.addListener(_onSearchFocusChange);

    // Initialize loading animation
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _loadBooks();
  }

  void _onSearchFocusChange() {
    // Detect keyboard visibility by checking if search field is focused
    // and using MediaQuery in build method
    setState(() {
      // This will trigger a rebuild where we can check MediaQuery
    });
  }

  void _loadBooks() {
    try {
      final data = json.decode(booksJsonString);
      final List<BookMobile> loadedBooks = (data['books'] as List)
          .map((bookJson) => BookMobile.fromJson(bookJson))
          .toList();

      setState(() {
        books = loadedBooks;
        isLoading = false;
      });

      // Start loading animation
      _loadingAnimationController.forward();
    } catch (e) {
      setState(() {
        books = [];
        isLoading = false;
      });
      debugPrint('Error loading books: $e');
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _searchFocusNode.removeListener(_onSearchFocusChange);
    _searchFocusNode.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Detect keyboard visibility
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    // Update keyboard state if changed
    if (_isKeyboardVisible != isKeyboardVisible) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _isKeyboardVisible = isKeyboardVisible;
          _currentIndex = 0; // Reset to first index when layout changes
        });
      });
    }

    return _buildBooksLibraryView();
  }

  Widget _buildBooksLibraryView() {
    return Scaffold(
      backgroundColor:
          widget.showNavigationBar ? Color(0xfff6f6f6) : Colors.transparent,
      drawer: widget.showNavigationBar ? const CustomDrawer() : null,
      appBar: widget.showNavigationBar ? _buildAppBar() : null,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showNavigationBar) _buildTopNavigation(),
          if (widget.showNavigationBar) _buildSearchAndCreateSection(),
          _buildHorizontalSlider(),
        ],
      ),
      floatingActionButton: widget.showNavigationBar
          ? SizedBox(
              width: 46,
              height: 46,
              child: FloatingActionButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateBookMobile(),
                    ),
                  );
                },
                backgroundColor: const Color(0xff0058FF),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(Icons.add),
              ),
            )
          : null,
    );
  }

  Widget _buildHorizontalSlider() {
    return Expanded(
      child: NSLKnowledgeLoaderWrapper(
        isLoading: isLoading,
        child: books.isEmpty
            ? const Center(child: Text('No books found'))
            : Column(
                children: [
                  // Horizontal PageView showing 2.5 books
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: PageView.builder(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentIndex = index;
                          });
                        },
                        itemCount: books.length,
                        itemBuilder: (context, index) {
                          return _buildHorizontalBookCard(books[index], index);
                        },
                      ),
                    ),
                  ),
                  // Custom Dot Pagination
                  _buildHorizontalPagination(),
                ],
              ),
      ),
    );
  }

  Widget _buildHorizontalBookCard(BookMobile book, int index) {
    return AnimatedBuilder(
      animation: _loadingAnimationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              margin: const EdgeInsets.only(right: 16, top: 16, bottom: 16),
              child: _MobileBookCard(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AddModulesMobileView(),
                    ),
                  );
                },
                child: _buildMobileBookCard(
                  title: book.title,
                  subtitle: book.subtitle,
                  imageUrl: book.imageUrl,
                  isDraft: book.isDraft,
                  imageWidth: _isKeyboardVisible ? 80.0 : 101.0,
                  imageHeight: _isKeyboardVisible ? 124.0 : 156.0,
                  isHalfCard: false,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMobileBookCard({
    required String title,
    required String subtitle,
    required String imageUrl,
    bool isDraft = false,
    double imageWidth = 101.0,
    double imageHeight = 156.0,
    bool isHalfCard = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Book cover with custom animations
        Stack(
          clipBehavior: Clip.none,
          children: [
            // Background SVG shape (only for full cards)
            if (!isHalfCard)
              Positioned(
                right: -8,
                bottom: 0,
                child: SvgPicture.asset(
                  'assets/images/home-lib-shape.svg',
                  width: imageWidth * 0.925,
                  height: imageHeight * 0.95,
                  fit: BoxFit.contain,
                ),
              ),
            // Main book image with hover effect
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: imageWidth,
              height: imageHeight,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(24),
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
                image: DecorationImage(
                  image: AssetImage(imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            // Draft badge with animation
            if (isDraft)
              Positioned(
                top: imageHeight * 0.08,
                right: imageWidth * 0.14,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.amber.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Text(
                    'Draft',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      fontFamily: "TiemposText",
                    ),
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        // Title and subtitle (only for full cards)
        if (!isHalfCard) ...[
          SizedBox(
            width: imageWidth,
            child: Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
                height: 1.334,
                color: Colors.black,
                fontFamily: "TiemposText",
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 4),
          SizedBox(
            width: imageWidth,
            child: Text(
              subtitle,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 11,
                color: Colors.black,
                fontFamily: "TiemposText",
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildHorizontalPagination() {
    // Calculate total pages based on books per view and scroll amount
    int totalPages = ((books.length - _booksPerView) / _booksToScrollPerSwipe).ceil() + 1;
    int currentPage = (_currentIndex / _booksToScrollPerSwipe).floor();
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          totalPages,
          (index) => GestureDetector(
            onTap: () {
              int targetIndex = index * _booksToScrollPerSwipe;
              if (targetIndex < books.length) {
                _pageController.animateToPage(
                  targetIndex,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              }
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: currentPage == index ? 24 : 8,
              height: 8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: currentPage == index
                    ? const Color(0xff0058FF)
                    : Colors.grey.shade300,
                boxShadow: currentPage == index
                    ? [
                        BoxShadow(
                          color: const Color(0xff0058FF).withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Color(0xfff6f6f6),
      surfaceTintColor: Colors.transparent,
      foregroundColor: Colors.black,
      elevation: 0,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Row(
        children: [
          Builder(
            builder: (context) => IconButton(
              icon: const Icon(Icons.menu, color: Colors.black, size: 24),
              onPressed: () => Scaffold.of(context).openDrawer(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
            ),
          ),
          Expanded(
            child: Text(
              AppLocalizations.of(context).translate('library.pageTitle'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 56),
        ],
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          MobileNavItem(
            iconPath: 'assets/images/books-icon.svg',
            label: AppLocalizations.of(context).translate('library.books'),
            isActive: selectedTabIndex == 0,
            onTap: () {},
          ),
          MobileNavItem(
            iconPath: 'assets/images/square-box-uncheck.svg',
            label: AppLocalizations.of(context).translate('library.solutions'),
            isActive: selectedTabIndex == 1,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SolutionsLibraryMobile(),
                ),
              );
            },
          ),
          MobileNavItem(
            iconPath: 'assets/images/cube-box.svg',
            label: AppLocalizations.of(context).translate('library.objects'),
            isActive: selectedTabIndex == 2,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ObjectsLibraryMobile(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndCreateSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0),
                    child: TextField(
                      focusNode: _searchFocusNode,
                      decoration: InputDecoration(
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        hintText: 'Search',
                        border: InputBorder.none,
                        hintStyle:
                            TextStyle(fontSize: 14, color: Colors.grey[500]),
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ),
                ),
                _MobileSvgButton(
                  iconPath: 'assets/images/search.svg',
                  onPressed: () {},
                  size: 20,
                ),
                Container(
                  height: 24,
                  width: 1,
                  color: Colors.grey.shade200,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                ),
                _MobileSvgButton(
                  iconPath: 'assets/images/filter-icon.svg',
                  onPressed: () {},
                  size: 24,
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Mobile SVG Button Widget
class _MobileSvgButton extends StatefulWidget {
  final String iconPath;
  final VoidCallback onPressed;
  final double size;

  const _MobileSvgButton({
    required this.iconPath,
    required this.onPressed,
    this.size = 18,
  });

  @override
  State<_MobileSvgButton> createState() => _MobileSvgButtonState();
}

class _MobileSvgButtonState extends State<_MobileSvgButton> {
  bool isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => setState(() => isPressed = true),
      onTapUp: (_) => setState(() => isPressed = false),
      onTapCancel: () => setState(() => isPressed = false),
      onTap: widget.onPressed,
      child: Container(
        height: 32,
        width: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: isPressed ? const Color(0xff0058FF) : Colors.transparent,
            width: 1.0,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SvgPicture.asset(
            widget.iconPath,
            width: widget.size,
            height: widget.size,
          ),
        ),
      ),
    );
  }
}

// Mobile Book Card Widget
class _MobileBookCard extends StatefulWidget {
  final Widget child;
  final VoidCallback onTap;

  const _MobileBookCard({
    required this.child,
    required this.onTap,
  });

  @override
  State<_MobileBookCard> createState() => _MobileBookCardState();
}

class _MobileBookCardState extends State<_MobileBookCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _scaleController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _scaleController.reverse();
    widget.onTap();
  }

  void _handleTapCancel() {
    _scaleController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: widget.child,
          );
        },
      ),
    );
  }
}
