import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:convert';
import '../utils/callback_interpreter.dart';

/// A configurable email input widget that validates email addresses.
class EmailWidget extends StatefulWidget {
  // Basic properties
  final String initialValue;
  final bool isRequired;
  final bool allowMultiple;
  final String separator;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Label properties
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;

  // Icon properties
  final bool showPrefix;
  final IconData? prefixIcon;
  final bool showSuffix;
  final IconData? suffixIcon;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool autofocus;
  final bool hasAnimation;
  final bool autoValidate;
  final bool validateOnBlur;
  final int validationDebounceMs;
  final bool showClearButton;
  final bool showCopyButton;
  final bool showPasteButton;
  final bool showValidationIcon;

  // Advanced validation properties
  final List<String>? allowedDomains;
  final List<String>? blockedDomains;
  final bool blockDisposableEmails;
  final bool requireCorporateEmail;
  final bool allowInternationalChars;
  final int? minLength;
  final int? maxLength;
  final String? customValidationRegex;
  final String? customValidationErrorText;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callback
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final FocusNode? focusNode;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  const EmailWidget({
    super.key,
    this.initialValue = '',
    this.isRequired = false,
    this.allowMultiple = false,
    this.separator = ',',
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.borderColor = Colors.grey,
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.showPrefix = true,
    this.prefixIcon = Icons.email,
    this.showSuffix = false,
    this.suffixIcon,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.autofocus = false,
    this.hasAnimation = false,
    this.autoValidate = true,
    this.validateOnBlur = false,
    this.validationDebounceMs = 0,
    this.showClearButton = false,
    this.showCopyButton = false,
    this.showPasteButton = false,
    this.showValidationIcon = true,
    this.allowedDomains,
    this.blockedDomains,
    this.blockDisposableEmails = false,
    this.requireCorporateEmail = false,
    this.allowInternationalChars = true,
    this.minLength,
    this.maxLength,
    this.customValidationRegex,
    this.customValidationErrorText,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.onChanged,
    this.onSubmitted,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
  });

  /// Creates an EmailWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the EmailWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialValue": "<EMAIL>",
  ///   "isRequired": true,
  ///   "label": "Email Address",
  ///   "hint": "Enter your email",
  ///   "textColor": "blue",
  ///   "backgroundColor": "white",
  ///   "showValidationIcon": true
  /// }
  /// ```
  factory EmailWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        // Handle hex strings like "#FF0000"
        if (colorValue.startsWith('#')) {
          String hexColor = colorValue.substring(1);

          // Handle shorthand hex like #RGB
          if (hexColor.length == 3) {
            hexColor = hexColor.split('').map((c) => '$c$c').join('');
          }

          // Add alpha channel if missing
          if (hexColor.length == 6) {
            hexColor = 'FF$hexColor';
          }

          // Parse the hex value
          try {
            return Color(int.parse('0x$hexColor'));
          } catch (e) {
            // Silently handle the error and return null
            return null;
          }
        }

        // Handle named colors
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'amber':
            return Colors.amber;
          case 'cyan':
            return Colors.cyan;
          case 'indigo':
            return Colors.indigo;
          case 'lime':
            return Colors.lime;
          case 'teal':
            return Colors.teal;
          default:
            return null;
        }
      } else if (colorValue is int) {
        // Handle integer color values
        return Color(colorValue);
      }

      return null;
    }

    // Parse text alignment
    TextAlign parseTextAlign(dynamic alignValue) {
      if (alignValue == null) return TextAlign.start;

      if (alignValue is String) {
        switch (alignValue.toLowerCase()) {
          case 'center':
            return TextAlign.center;
          case 'end':
          case 'right':
            return TextAlign.end;
          case 'start':
          case 'left':
            return TextAlign.start;
          case 'justify':
            return TextAlign.justify;
          default:
            return TextAlign.start;
        }
      }

      return TextAlign.start;
    }

    // Parse font weight
    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.normal;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'light':
            return FontWeight.w300;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100:
            return FontWeight.w100;
          case 200:
            return FontWeight.w200;
          case 300:
            return FontWeight.w300;
          case 400:
            return FontWeight.w400;
          case 500:
            return FontWeight.w500;
          case 600:
            return FontWeight.w600;
          case 700:
            return FontWeight.w700;
          case 800:
            return FontWeight.w800;
          case 900:
            return FontWeight.w900;
          default:
            return FontWeight.normal;
        }
      } else if (weightValue is bool && weightValue) {
        return FontWeight.bold;
      }

      return FontWeight.normal;
    }

    // Parse icon data
    IconData? parseIconData(dynamic iconValue) {
      if (iconValue == null) return null;

      if (iconValue is String) {
        switch (iconValue.toLowerCase()) {
          case 'email':
            return Icons.email;
          case 'mail':
            return Icons.mail;
          case 'mail_outline':
            return Icons.mail_outline;
          case 'alternate_email':
            return Icons.alternate_email;
          case 'contact_mail':
            return Icons.contact_mail;
          case 'mark_email_read':
            return Icons.mark_email_read;
          case 'mark_email_unread':
            return Icons.mark_email_unread;
          case 'person':
            return Icons.person;
          case 'account_circle':
            return Icons.account_circle;
          case 'add':
            return Icons.add;
          case 'remove':
            return Icons.remove;
          case 'clear':
            return Icons.clear;
          case 'delete':
            return Icons.delete;
          case 'edit':
            return Icons.edit;
          case 'save':
            return Icons.save;
          case 'check':
            return Icons.check;
          case 'close':
            return Icons.close;
          case 'search':
            return Icons.search;
          case 'settings':
            return Icons.settings;
          case 'info':
            return Icons.info;
          case 'warning':
            return Icons.warning;
          case 'error':
            return Icons.error;
          case 'help':
            return Icons.help;
          case 'copy':
            return Icons.copy;
          case 'paste':
            return Icons.paste;
          default:
            return null;
        }
      }

      return null;
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic insetsValue) {
      if (insetsValue == null) {
        return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
      }

      if (insetsValue is Map<String, dynamic>) {
        final double left = (insetsValue['left'] as num?)?.toDouble() ?? 0.0;
        final double top = (insetsValue['top'] as num?)?.toDouble() ?? 0.0;
        final double right = (insetsValue['right'] as num?)?.toDouble() ?? 0.0;
        final double bottom =
            (insetsValue['bottom'] as num?)?.toDouble() ?? 0.0;

        if (insetsValue.containsKey('all')) {
          final double all = (insetsValue['all'] as num).toDouble();
          return EdgeInsets.all(all);
        } else if (insetsValue.containsKey('horizontal') ||
            insetsValue.containsKey('vertical')) {
          final double horizontal =
              (insetsValue['horizontal'] as num?)?.toDouble() ?? 0.0;
          final double vertical =
              (insetsValue['vertical'] as num?)?.toDouble() ?? 0.0;
          return EdgeInsets.symmetric(
            horizontal: horizontal,
            vertical: vertical,
          );
        } else {
          return EdgeInsets.fromLTRB(left, top, right, bottom);
        }
      } else if (insetsValue is num) {
        return EdgeInsets.all(insetsValue.toDouble());
      }

      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
    }

    // Parse string list
    List<String>? parseStringList(dynamic listValue) {
      if (listValue == null) return null;

      if (listValue is List) {
        return List<String>.from(listValue.map((e) => e.toString()));
      } else if (listValue is String) {
        // Handle comma-separated string
        return listValue.split(',').map((e) => e.trim()).toList();
      }

      return null;
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onChanged'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onChanged'] = json['onChanged'];
      useJsonCallbacks = true;
    }

    if (json['onSubmitted'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onSubmitted'] = json['onSubmitted'];
      useJsonCallbacks = true;
    }

    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    // Create the widget with all properties from JSON
    return EmailWidget(
      initialValue: json['initialValue'] as String? ?? '',
      isRequired: json['isRequired'] as bool? ?? false,
      allowMultiple: json['allowMultiple'] as bool? ?? false,
      separator: json['separator'] as String? ?? ',',
      textColor: parseColor(json['textColor']) ?? Colors.black,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      borderColor: parseColor(json['borderColor']) ?? Colors.grey,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      hasBorder: json['hasBorder'] as bool? ?? true,
      fontSize:
          json['fontSize'] != null
              ? (json['fontSize'] as num).toDouble()
              : 16.0,
      fontWeight: parseFontWeight(json['fontWeight']),
      isCompact: json['isCompact'] as bool? ?? false,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      textAlign: parseTextAlign(json['textAlign']),
      label: json['label'] as String?,
      hint: json['hint'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      showPrefix: json['showPrefix'] as bool? ?? true,
      prefixIcon: parseIconData(json['prefixIcon']) ?? Icons.email,
      showSuffix: json['showSuffix'] as bool? ?? false,
      suffixIcon: parseIconData(json['suffixIcon']),
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      autofocus: json['autofocus'] as bool? ?? false,
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      autoValidate: json['autoValidate'] as bool? ?? true,
      validateOnBlur: json['validateOnBlur'] as bool? ?? false,
      validationDebounceMs: json['validationDebounceMs'] as int? ?? 0,
      showClearButton: json['showClearButton'] as bool? ?? false,
      showCopyButton: json['showCopyButton'] as bool? ?? false,
      showPasteButton: json['showPasteButton'] as bool? ?? false,
      showValidationIcon: json['showValidationIcon'] as bool? ?? true,
      allowedDomains: parseStringList(json['allowedDomains']),
      blockedDomains: parseStringList(json['blockedDomains']),
      blockDisposableEmails: json['blockDisposableEmails'] as bool? ?? false,
      requireCorporateEmail: json['requireCorporateEmail'] as bool? ?? false,
      allowInternationalChars: json['allowInternationalChars'] as bool? ?? true,
      minLength: json['minLength'] as int?,
      maxLength: json['maxLength'] as int?,
      customValidationRegex: json['customValidationRegex'] as String?,
      customValidationErrorText: json['customValidationErrorText'] as String?,
      width:
          json['width'] != null
              ? (json['width'] as num).toDouble()
              : double.infinity,
      height: json['height'] != null ? (json['height'] as num).toDouble() : 0,
      padding: parseEdgeInsets(json['padding']),
      margin: parseEdgeInsets(json['margin']),
      // Advanced interaction properties
      hoverColor: parseColor(json['hoverColor']),
      focusColor: parseColor(json['focusColor']),
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      // JSON configuration properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState:
          json['callbackState'] != null
              ? Map<String, dynamic>.from(json['callbackState'] as Map)
              : {},
      jsonConfig: json,
      useJsonValidation: json['useJsonValidation'] as bool? ?? false,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
    );
  }

  /// Converts the widget configuration to a JSON map
  ///
  /// This method allows for serializing the widget's configuration to JSON,
  /// which can be useful for saving configurations or sending them to a server.
  Map<String, dynamic> toJson() {
    // Convert color to hex string
    String? colorToHex(Color? color) {
      if (color == null) return null;

      // Convert to hex format
      final r = (color.r * 255).round();
      final g = (color.g * 255).round();
      final b = (color.b * 255).round();
      return '#${r.toRadixString(16).padLeft(2, '0')}${g.toRadixString(16).padLeft(2, '0')}${b.toRadixString(16).padLeft(2, '0')}';
    }

    // Convert text alignment to string
    String textAlignToString(TextAlign align) {
      switch (align) {
        case TextAlign.center:
          return 'center';
        case TextAlign.end:
          return 'end';
        case TextAlign.left:
          return 'left';
        case TextAlign.right:
          return 'right';
        case TextAlign.justify:
          return 'justify';
        case TextAlign.start:
          return 'start';
        default:
          return 'start'; // Handle any future additions
      }
    }

    // Convert font weight to int
    int fontWeightToInt(FontWeight weight) {
      if (weight == FontWeight.w100) return 100;
      if (weight == FontWeight.w200) return 200;
      if (weight == FontWeight.w300) return 300;
      if (weight == FontWeight.w400 || weight == FontWeight.normal) return 400;
      if (weight == FontWeight.w500) return 500;
      if (weight == FontWeight.w600) return 600;
      if (weight == FontWeight.w700 || weight == FontWeight.bold) return 700;
      if (weight == FontWeight.w800) return 800;
      if (weight == FontWeight.w900) return 900;
      return 400;
    }

    // Convert icon data to string
    String? iconDataToString(IconData? icon) {
      if (icon == null) return null;

      if (icon == Icons.email) return 'email';
      if (icon == Icons.mail) return 'mail';
      if (icon == Icons.mail_outline) return 'mail_outline';
      if (icon == Icons.alternate_email) return 'alternate_email';
      if (icon == Icons.contact_mail) return 'contact_mail';
      if (icon == Icons.mark_email_read) return 'mark_email_read';
      if (icon == Icons.mark_email_unread) return 'mark_email_unread';
      if (icon == Icons.person) return 'person';
      if (icon == Icons.account_circle) return 'account_circle';
      if (icon == Icons.add) return 'add';
      if (icon == Icons.remove) return 'remove';
      if (icon == Icons.clear) return 'clear';
      if (icon == Icons.delete) return 'delete';
      if (icon == Icons.edit) return 'edit';
      if (icon == Icons.save) return 'save';
      if (icon == Icons.check) return 'check';
      if (icon == Icons.close) return 'close';
      if (icon == Icons.search) return 'search';
      if (icon == Icons.settings) return 'settings';
      if (icon == Icons.info) return 'info';
      if (icon == Icons.warning) return 'warning';
      if (icon == Icons.error) return 'error';
      if (icon == Icons.help) return 'help';
      if (icon == Icons.copy) return 'copy';
      if (icon == Icons.paste) return 'paste';

      return null;
    }

    // Convert edge insets to map
    Map<String, dynamic>? edgeInsetsToMap(EdgeInsetsGeometry insets) {
      if (insets is EdgeInsets) {
        if (insets.left == insets.top &&
            insets.left == insets.right &&
            insets.left == insets.bottom) {
          return {'all': insets.left};
        } else if (insets.left == insets.right && insets.top == insets.bottom) {
          return {'horizontal': insets.left, 'vertical': insets.top};
        } else {
          return {
            'left': insets.left,
            'top': insets.top,
            'right': insets.right,
            'bottom': insets.bottom,
          };
        }
      }
      return null;
    }

    // Create the JSON map
    final Map<String, dynamic> json = {
      'initialValue': initialValue,
      'isRequired': isRequired,
      'allowMultiple': allowMultiple,
      'separator': separator,
      'textColor': colorToHex(textColor),
      'backgroundColor': colorToHex(backgroundColor),
      'borderColor': colorToHex(borderColor),
      'borderWidth': borderWidth,
      'borderRadius': borderRadius,
      'hasBorder': hasBorder,
      'fontSize': fontSize,
      'fontWeight': fontWeightToInt(fontWeight),
      'isCompact': isCompact,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isDarkTheme': isDarkTheme,
      'textAlign': textAlignToString(textAlign),
      'label': label,
      'hint': hint,
      'helperText': helperText,
      'errorText': errorText,
      'showPrefix': showPrefix,
      'prefixIcon': iconDataToString(prefixIcon),
      'showSuffix': showSuffix,
      'suffixIcon': iconDataToString(suffixIcon),
      'isReadOnly': isReadOnly,
      'isDisabled': isDisabled,
      'autofocus': autofocus,
      'hasAnimation': hasAnimation,
      'autoValidate': autoValidate,
      'validateOnBlur': validateOnBlur,
      'validationDebounceMs': validationDebounceMs,
      'showClearButton': showClearButton,
      'showCopyButton': showCopyButton,
      'showPasteButton': showPasteButton,
      'showValidationIcon': showValidationIcon,
      'allowedDomains': allowedDomains,
      'blockedDomains': blockedDomains,
      'blockDisposableEmails': blockDisposableEmails,
      'requireCorporateEmail': requireCorporateEmail,
      'allowInternationalChars': allowInternationalChars,
      'minLength': minLength,
      'maxLength': maxLength,
      'customValidationRegex': customValidationRegex,
      'customValidationErrorText': customValidationErrorText,
      'width': width == double.infinity ? 'infinity' : width,
      'height': height,
      'padding': edgeInsetsToMap(padding),
      'margin': edgeInsetsToMap(margin),
      'hoverColor': colorToHex(hoverColor),
      'focusColor': colorToHex(focusColor),
      'enableFeedback': enableFeedback,
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonValidation': useJsonValidation,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
    };

    // Add callbacks if they exist
    if (jsonCallbacks != null && jsonCallbacks!.isNotEmpty) {
      json['callbacks'] = jsonCallbacks;
    }

    return json;
  }

  @override
  State<EmailWidget> createState() => _EmailWidgetState();
}

/// Class to hold validation results
class _ValidationResult {
  final bool isValid;
  final String errorMessage;

  _ValidationResult(this.isValid, this.errorMessage);
}

class _EmailWidgetState extends State<EmailWidget>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _animation;
  Timer? _debounceTimer;

  String? _errorText;
  bool _isValid = true;
  bool _isHovered = false; // Track hover state
  bool _isFocused = false; // Track focus state

  // Map to store dynamic state for callbacks
  Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;

  // List of common disposable email domains
  static const List<String> _disposableEmailDomains = [
    'mailinator.com',
    'tempmail.com',
    'temp-mail.org',
    'guerrillamail.com',
    'guerrillamail.net',
    'sharklasers.com',
    'yopmail.com',
    'throwawaymail.com',
    'trashmail.com',
    'mailnesia.com',
    'mailcatch.com',
    '10minutemail.com',
    'tempinbox.com',
    'dispostable.com',
    'mintemail.com',
    'mailnull.com',
    'spamgourmet.com',
    'fakeinbox.com',
    'getnada.com',
    'emailondeck.com',
  ];

  // List of common corporate email domains (not exhaustive)
  static const List<String> _nonCorporateEmailDomains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'aol.com',
    'icloud.com',
    'mail.com',
    'protonmail.com',
    'zoho.com',
    'gmx.com',
    'live.com',
    'yandex.com',
    'inbox.com',
    'fastmail.com',
  ];

  // Regular expression for validating email addresses
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    caseSensitive: false,
    multiLine: false,
  );

  @override
  void initState() {
    super.initState();

    // Initialize controller with initial value
    _controller = TextEditingController(text: widget.initialValue);

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();

    // Add focus listener to track focus changes
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
      if (widget.onFocus != null) {
        widget.onFocus!(_focusNode.hasFocus);
      }
    });

    // Initialize callback state
    _callbackState =
        widget.callbackState != null
            ? Map<String, dynamic>.from(widget.callbackState!)
            : {};

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Create animation
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Start animation if needed
    if (widget.hasAnimation) {
      _animationController.forward();
    }

    // Set initial error text
    _errorText = widget.errorText;

    // Validate initial value
    if (widget.autoValidate && widget.initialValue.isNotEmpty) {
      _validateEmail(widget.initialValue);
    }

    // Execute onInit callback if defined in JSON
    _executeJsonCallback('onInit');
  }

  /// Executes a callback defined in JSON
  ///
  /// This method interprets and executes a callback defined in the JSON configuration.
  /// It supports various callback types and provides access to the current state.
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      _callbackState['currentValue'] = _controller.text;
      _callbackState['isValid'] = _isValid;

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue = _controller.text;
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the current value
  ///
  /// This method applies validation rules defined in the JSON configuration.
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    final value = _controller.text;

    // Example: Apply validation rules
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply required validation
        if (rules.containsKey('required') && rules['required'] == true) {
          if (value.isEmpty) {
            _isValid = false;
            _errorText = 'Email address is required';
            return;
          }
        }

        // Apply regex validation
        if (rules.containsKey('regex') && rules['regex'] is String) {
          final regex = RegExp(rules['regex'] as String);
          if (!regex.hasMatch(value)) {
            _isValid = false;
            _errorText =
                rules['regexErrorMessage'] as String? ?? 'Invalid email format';
            return;
          }
        }

        // Apply allowed domains validation
        if (rules.containsKey('allowedDomains') &&
            rules['allowedDomains'] is List) {
          final allowedDomains = List<String>.from(
            (rules['allowedDomains'] as List).map((e) => e.toString()),
          );
          if (value.isNotEmpty && value.contains('@')) {
            final domain = value.split('@')[1].toLowerCase();
            if (!allowedDomains.any((d) => domain == d.toLowerCase())) {
              _isValid = false;
              _errorText =
                  'Domain not allowed. Use: ${allowedDomains.join(', ')}';
              return;
            }
          }
        }

        // Apply blocked domains validation
        if (rules.containsKey('blockedDomains') &&
            rules['blockedDomains'] is List) {
          final blockedDomains = List<String>.from(
            (rules['blockedDomains'] as List).map((e) => e.toString()),
          );
          if (value.isNotEmpty && value.contains('@')) {
            final domain = value.split('@')[1].toLowerCase();
            if (blockedDomains.any((d) => domain == d.toLowerCase())) {
              _isValid = false;
              _errorText = 'Domain not allowed: $domain';
              return;
            }
          }
        }

        // Apply custom validation
        if (rules.containsKey('custom') && rules['custom'] is String) {
          final customRule = rules['custom'] as String;

          // Example: Check if email contains a specific string
          if (customRule.startsWith('contains:')) {
            final substring = customRule.substring('contains:'.length);
            if (!value.contains(substring)) {
              _isValid = false;
              _errorText = 'Email must contain: $substring';
              return;
            }
          }

          // Example: Check if email starts with a specific prefix
          if (customRule.startsWith('startsWith:')) {
            final prefix = customRule.substring('startsWith:'.length);
            if (!value.startsWith(prefix)) {
              _isValid = false;
              _errorText = 'Email must start with: $prefix';
              return;
            }
          }
        }
      }
    }

    _isValid = true;
    _errorText = widget.errorText;
  }

  /// Applies JSON styling to the widget
  ///
  /// This method applies styling rules defined in the JSON configuration.
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current value
  ///
  /// This method applies formatting rules defined in the JSON configuration.
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  /// Validates the email address(es) and updates the error text
  bool _validateEmail(String value) {
    // Check if empty
    if (value.isEmpty) {
      if (widget.isRequired) {
        setState(() {
          _errorText = 'Email address is required';
          _isValid = false;
        });
        return false;
      } else {
        setState(() {
          _errorText = widget.errorText;
          _isValid = true;
        });
        return true;
      }
    }

    // Check length constraints if specified
    if (widget.minLength != null && value.length < widget.minLength!) {
      setState(() {
        _errorText = 'Email must be at least ${widget.minLength} characters';
        _isValid = false;
      });
      return false;
    }

    if (widget.maxLength != null && value.length > widget.maxLength!) {
      setState(() {
        _errorText = 'Email cannot exceed ${widget.maxLength} characters';
        _isValid = false;
      });
      return false;
    }

    // Use custom regex if provided
    final RegExp emailRegex =
        widget.customValidationRegex != null
            ? RegExp(widget.customValidationRegex!, caseSensitive: false)
            : widget.allowInternationalChars
            ? RegExp(
              r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
              caseSensitive: false,
              multiLine: false,
            )
            : _emailRegex;

    if (widget.allowMultiple) {
      // Split the input by the separator and validate each email
      final emails = value.split(widget.separator);
      for (final email in emails) {
        final trimmedEmail = email.trim();
        if (trimmedEmail.isEmpty) continue;

        // Basic format validation
        if (!emailRegex.hasMatch(trimmedEmail)) {
          setState(() {
            _errorText = 'Invalid email address: $trimmedEmail';
            _isValid = false;
          });
          return false;
        }

        // Additional validations for each email
        final validationResult = _performAdvancedValidation(trimmedEmail);
        if (!validationResult.isValid) {
          setState(() {
            _errorText = validationResult.errorMessage;
            _isValid = false;
          });
          return false;
        }
      }

      setState(() {
        _errorText = widget.errorText;
        _isValid = true;
      });
      return true;
    } else {
      // Validate a single email address
      // Basic format validation
      if (!emailRegex.hasMatch(value)) {
        setState(() {
          _errorText =
              widget.customValidationErrorText ?? 'Invalid email address';
          _isValid = false;
        });
        return false;
      }

      // Additional validations
      final validationResult = _performAdvancedValidation(value);
      if (!validationResult.isValid) {
        setState(() {
          _errorText = validationResult.errorMessage;
          _isValid = false;
        });
        return false;
      }

      setState(() {
        _errorText = widget.errorText;
        _isValid = true;
      });
      return true;
    }
  }

  /// Performs advanced validation on a single email address
  _ValidationResult _performAdvancedValidation(String email) {
    // Extract domain from email
    final parts = email.split('@');
    if (parts.length != 2) {
      return _ValidationResult(false, 'Invalid email format');
    }

    final domain = parts[1].toLowerCase();

    // Check allowed domains if specified
    if (widget.allowedDomains != null && widget.allowedDomains!.isNotEmpty) {
      if (!widget.allowedDomains!.any((d) => domain == d.toLowerCase())) {
        return _ValidationResult(
          false,
          'Domain not allowed. Use: ${widget.allowedDomains!.join(', ')}',
        );
      }
    }

    // Check blocked domains if specified
    if (widget.blockedDomains != null && widget.blockedDomains!.isNotEmpty) {
      if (widget.blockedDomains!.any((d) => domain == d.toLowerCase())) {
        return _ValidationResult(false, 'Domain not allowed: $domain');
      }
    }

    // Check for disposable email domains if enabled
    if (widget.blockDisposableEmails) {
      if (_disposableEmailDomains.any((d) => domain == d)) {
        return _ValidationResult(
          false,
          'Disposable email addresses are not allowed',
        );
      }
    }

    // Check for corporate email requirement if enabled
    if (widget.requireCorporateEmail) {
      if (_nonCorporateEmailDomains.any((d) => domain == d)) {
        return _ValidationResult(false, 'Please use a corporate email address');
      }
    }

    return _ValidationResult(true, '');
  }

  /// Handles changes to the email input
  void _handleValueChanged(String value) {
    // Execute onBeforeChange callback if defined in JSON
    _executeJsonCallback('onBeforeChange', value);

    // Call onChanged callback immediately
    if (widget.onChanged != null) {
      widget.onChanged!(value);
    }

    // Execute onChanged callback if defined in JSON
    _executeJsonCallback('onChanged', value);

    // Reset animation if needed
    if (widget.hasAnimation) {
      _animationController.reset();
      _animationController.forward();
    }

    // Handle validation with debounce if needed
    if (widget.autoValidate && !widget.validateOnBlur) {
      if (widget.validationDebounceMs > 0) {
        // Cancel previous timer if it exists
        _debounceTimer?.cancel();

        // Create new timer
        _debounceTimer = Timer(
          Duration(milliseconds: widget.validationDebounceMs),
          () {
            if (widget.useJsonValidation) {
              _applyJsonValidation();
            } else {
              _validateEmail(value);
            }

            // Execute onValidated callback if defined in JSON
            _executeJsonCallback('onValidated', _isValid);
          },
        );
      } else {
        // Validate immediately if no debounce
        if (widget.useJsonValidation) {
          _applyJsonValidation();
        } else {
          _validateEmail(value);
        }

        // Execute onValidated callback if defined in JSON
        _executeJsonCallback('onValidated', _isValid);
      }
    }
  }

  /// Clears the input field
  void _clearInput() {
    // Execute onBeforeClear callback if defined in JSON
    _executeJsonCallback('onBeforeClear');

    _controller.clear();
    _handleValueChanged('');

    // Execute onClear callback if defined in JSON
    _executeJsonCallback('onClear');
  }

  /// Copies the current value to the clipboard
  void _copyToClipboard() {
    // Execute onBeforeCopy callback if defined in JSON
    _executeJsonCallback('onBeforeCopy');

    Clipboard.setData(ClipboardData(text: _controller.text));
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Email copied to clipboard')));

    // Execute onCopy callback if defined in JSON
    _executeJsonCallback('onCopy', _controller.text);
  }

  /// Pastes text from the clipboard
  Future<void> _pasteFromClipboard() async {
    // Execute onBeforePaste callback if defined in JSON
    _executeJsonCallback('onBeforePaste');

    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData != null && clipboardData.text != null) {
      _controller.text = clipboardData.text!;
      _handleValueChanged(_controller.text);

      // Execute onPaste callback if defined in JSON
      _executeJsonCallback('onPaste', _controller.text);
    } else {
      // Execute onPasteError callback if defined in JSON
      _executeJsonCallback('onPasteError', 'No text found in clipboard');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;
    final effectiveBorderColor =
        widget.isDarkTheme ? Colors.grey.shade600 : Color(0xFFCCCCCC);

    // Determine icon color based on state (hover, focus, error)
    Color effectiveIconColor;
    if (!_isValid) {
      effectiveIconColor = Colors.red; // Error state
    } else if (_isFocused) {
      effectiveIconColor = const Color(0xFF0058FF); // Focus state
    } else if (_isHovered) {
      effectiveIconColor = const Color(0xFF0058FF); // Hover state
    } else {
      effectiveIconColor = const Color(0xFFCCCCCC); // Default state
    }

    // Create suffix icons based on configuration
    List<Widget> suffixIcons = [];

    if (widget.showClearButton && !widget.isReadOnly && !widget.isDisabled) {
      suffixIcons.add(
        IconButton(
          icon: const Icon(Icons.clear, size: 18),
          onPressed: _clearInput,
          tooltip: 'Clear',
        ),
      );
    }

    if (widget.showCopyButton) {
      suffixIcons.add(
        IconButton(
          icon: const Icon(Icons.copy, size: 18),
          onPressed: _copyToClipboard,
          tooltip: 'Copy',
        ),
      );
    }

    if (widget.showPasteButton && !widget.isReadOnly && !widget.isDisabled) {
      suffixIcons.add(
        IconButton(
          icon: const Icon(Icons.paste, size: 18),
          onPressed: _pasteFromClipboard,
          tooltip: 'Paste',
        ),
      );
    }

    if (widget.showValidationIcon) {
      suffixIcons.add(
        Icon(
          _isValid ? Icons.check_circle : Icons.error,
          color: _isValid ? Colors.green : Colors.red,
          size: 18,
        ),
      );
    }

    if (widget.showSuffix && widget.suffixIcon != null) {
      suffixIcons.add(Icon(widget.suffixIcon, color: effectiveIconColor));
    }

    // Create the suffix widget based on the number of icons
    Widget? suffixWidget;
    if (suffixIcons.isNotEmpty) {
      if (suffixIcons.length == 1) {
        suffixWidget = suffixIcons.first;
      } else {
        suffixWidget = Row(
          mainAxisSize: MainAxisSize.min,
          children: suffixIcons,
        );
      }
    }

    // Create the text field with all the specified properties
    Widget textField = TextField(
      controller: _controller,
      focusNode: _focusNode,
      keyboardType: TextInputType.emailAddress,
      textAlign: widget.textAlign,
      style: TextStyle(
        color: widget.isDisabled ? Colors.grey : effectiveTextColor,
        //fontSize: widget.fontSize,
        fontSize: _getResponsiveInputFontSize(context),
        fontWeight: widget.fontWeight,
      ),
      decoration: InputDecoration(
        //labelText: widget.label,
        labelStyle: TextStyle(fontSize: _getResponsiveFontSize(context)),
        hintText:
            widget.hint ??
            (widget.allowMultiple
                ? 'Enter email addresses separated by ${widget.separator}'
                : 'Enter email address'),
        helperText: widget.helperText,
        errorText: _errorText,
        filled: true,
        fillColor:
            widget.isDisabled ? Colors.grey.shade200 : effectiveBackgroundColor,
        hoverColor:
            widget.isDisabled
                ? Colors.grey.shade200
                : effectiveBackgroundColor, // Remove hover background change
        prefixIcon:
            widget.showPrefix && widget.prefixIcon != null
                ? Icon(widget.prefixIcon, color: effectiveIconColor)
                : null,
        suffixIcon: suffixWidget,
        contentPadding:
            widget.isCompact
                ? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0)
                : widget.padding,
        border:
            widget.hasBorder
                ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  borderSide: BorderSide(
                    color: effectiveBorderColor,
                    width: widget.borderWidth,
                  ),
                )
                : InputBorder.none,
        enabledBorder:
            widget.hasBorder
                ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  borderSide: BorderSide(
                    color:
                        _isHovered
                            ? const Color(0xFF0058FF)
                            : effectiveBorderColor,
                    width: widget.borderWidth,
                  ),
                )
                : InputBorder.none,
        focusedBorder:
            widget.hasBorder
                ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  borderSide: BorderSide(
                    //color: Theme.of(context).primaryColor,
                    //width: widget.borderWidth + 0.5,
                    color: Color(0xFF0058FF),
                  ),
                )
                : InputBorder.none,
        errorBorder:
            widget.hasBorder
                ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.borderRadius),
                  borderSide: BorderSide(
                    color: Colors.red,
                    width: widget.borderWidth,
                  ),
                )
                : InputBorder.none,
      ),
      enabled: !widget.isDisabled && !widget.isReadOnly,
      readOnly: widget.isReadOnly,
      autofocus: widget.autofocus,
      onChanged: _handleValueChanged,
      onSubmitted: (value) {
        // Execute onBeforeSubmit callback if defined in JSON
        _executeJsonCallback('onBeforeSubmit', value);

        // Call standard callback
        if (widget.onSubmitted != null) {
          widget.onSubmitted!(value);
        }

        // Execute onSubmitted callback if defined in JSON
        _executeJsonCallback('onSubmitted', value);
      },
      onTap: () {
        // Execute onBeforeTap callback if defined in JSON
        _executeJsonCallback('onBeforeTap');

        if (widget.isCompact) {
          // Select all text when tapped in compact mode
          _controller.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _controller.text.length,
          );
        }

        // Call standard callback
        if (widget.onTap != null) {
          widget.onTap!();
        }

        // Execute onTap callback if defined in JSON
        _executeJsonCallback('onTap');
      },
    );

    // Apply animation if needed
    final animatedWidget =
        widget.hasAnimation
            ? FadeTransition(opacity: _animation, child: textField)
            : textField;

    // Apply shadow if needed
    final shadowWidget =
        widget.hasShadow
            ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: animatedWidget,
            )
            : animatedWidget;

    // Create the final widget with the specified size
    final sizedWidget = Container(
      width: widget.width,
      //height: widget.height > 0 ? widget.height : null,
      height: _getResponsiveHeight(context),
      padding: _getResponsivePadding(context),
      margin: widget.margin,
      child: shadowWidget,
    );

    // Wrap with MouseRegion to handle hover events
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
        if (widget.onHover != null) {
          widget.onHover!(true);
        }
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
        if (widget.onHover != null) {
          widget.onHover!(false);
        }
      },
      child: sizedWidget,
    );
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return const EdgeInsets.symmetric(
        horizontal: 12.0,
        vertical: 4.0,
      ); // Extra Large
    } else if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(
        horizontal: 10.0,
        vertical: 3.0,
      ); // Large
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(horizontal: 0, vertical: 2.0); // Medium
    } else if (screenWidth >= 768) {
      return const EdgeInsets.symmetric(
        horizontal: 0.0,
        vertical: 1.0,
      ); // Small
    } else {
      return const EdgeInsets.symmetric(
        horizontal: 0.0,
        vertical: 1.0,
      ); // Default for very small screens
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 16.0; // Extra Large (>1920px) - Reduced for better fit
    } else if (screenWidth >= 1440) {
      return 15.0; // Large (1440-1920px) - Reduced for better fit
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium (1280-1366px) - Standard size
    } else if (screenWidth >= 768) {
      return 12.0; // Small (768-1024px) - Increased for readability
    } else {
      return 12.0; // Default for very small screens - Consistent
    }
  }

  double _getResponsiveInputFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 16.0; // Extra Large (>1920px) - Reduced for better fit
    } else if (screenWidth >= 1440) {
      return 15.0; // Large (1440-1920px) - Reduced for better fit
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium (1280-1366px) - Standard size
    } else if (screenWidth >= 768) {
      return 14.0; // Small (768-1024px) - Increased for readability
    } else {
      return 14.0; // Default for very small screens - Consistent
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Base height for input field
    double baseHeight;
    if (screenWidth > 1920) {
      baseHeight = 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      baseHeight = 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      baseHeight = 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      baseHeight = 32.0; // Small (768-1024px)
    } else {
      baseHeight = 32.0; // Default for very small screens
    }

    // Add extra height if there's an error message to prevent overlap
    if (_errorText != null && _errorText!.isNotEmpty) {
      baseHeight += 24.0; // Add space for error text
    }

    // Add extra height if there's helper text
    if (widget.helperText != null && widget.helperText!.isNotEmpty) {
      baseHeight += 20.0; // Add space for helper text
    }

    return baseHeight;
  }
}
