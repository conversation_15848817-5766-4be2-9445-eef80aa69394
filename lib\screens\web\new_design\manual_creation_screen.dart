import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/models/workflow/entity_manual_response_model.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/custom_expansion_tile_ellipsis.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import '../../../models/conversation_response.dart';
import '../../../models/entities_data.dart' as entities_model;
import '../../../providers/manual_creation_provider.dart';
import '../../../models/role_info.dart';
import '../../../widgets/resizable_panel.dart';
import 'widgets/chat_widgets/build_role_card.dart';
import 'widgets/role_details_panel.dart';
import 'widgets/workflow_lo_details_panel.dart';
import 'workflow_tree_builder.dart';

class ManualCreationScreen extends StatelessWidget {
  const ManualCreationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _ManualCreationScreenContent();
  }
}

class _ManualCreationScreenContent extends StatefulWidget {
  @override
  _ManualCreationScreenContentState createState() =>
      _ManualCreationScreenContentState();
}

class _ManualCreationScreenContentState
    extends State<_ManualCreationScreenContent> {
  String? _lastShownError;
  String? _lastShownResult;
  String? _activeIconView = "Agents"; // Track which icon view is active

  DateTime _lastClickTime = DateTime.now();
  int selectedSectionIndex = 0;

  Map<String, EntityElement> globalEntityElements = {};

  @override
  Widget build(BuildContext context) {
    Provider.of<ManualCreationProvider>(context, listen: false).intializeWidths(
        MediaQuery.of(context).size.width / 2,
        MediaQuery.of(context).size.width / 6,
        MediaQuery.of(context).size.width / 2);
    return Consumer<ManualCreationProvider>(
      builder: (context, provider, child) {
        // Show alert dialog for validation results
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // _showValidationAlertDialog(context, provider);
        });

        return _buildMainContent(context, provider);
      },
    );
  }

  Widget _buildMainContent(
      BuildContext context, ManualCreationProvider provider) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                (provider.showSidePanel || provider.showWorkflowLoSidePanel)
                    ? SizedBox(
                        width: MediaQuery.of(context).size.width >= 1900
                            ? AppSpacing.spaceBetweenMainContentAndSidePanel
                            : AppSpacing
                                .spaceBetweenMainContentAndSidePanelForSmallScreens,
                        child: rightSideTooltips(provider),
                      )
                    : Expanded(
                        child: rightSideTooltips(provider),
                      ),

                // Main content area
                Expanded(
                  flex: provider.showSidePanel ? 3 : 7,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: AppSpacing.sm),
                      // Header with back button
                      GestureDetector(
                        onTap: () {
                          Provider.of<WebHomeProvider>(context, listen: false)
                              .currentScreenIndex = ScreenConstants.home;
                        },
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                'assets/images/arrow-left.svg',
                                height: 12,
                                width: 12,
                                color: Colors.black,
                              ),
                              // IconButton(
                              //   icon: Icon(Icons.arrow_back, color: Colors.black),
                              //   onPressed: () {
                              //     Navigator.of(context).pop();
                              //   },
                              //),
                              SizedBox(width: 8),
                              Text(
                                'Previous page',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xff5D5D5D),
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'TiemposText',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      SizedBox(height: 24),

                      // Title
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            _getTitle(provider),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              fontFamily: 'TiemposText',
                              color: Colors.black,
                            ),
                          ),
                          MouseRegion(
                            cursor: SystemMouseCursors.click,
                            child: GestureDetector(
                              onTap: provider.clearText,
                              child: Text(
                                "Clear",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'TiemposText',
                                  color: Color(0xff0058FF),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 2),

                      // Main content area with icons and text field
                      Expanded(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Left side - Icons column

                            // SizedBox(width: 16),

                            // Right side - Large text field or table
                            Expanded(
                              child: _buildWorkflowContent(context, provider),
                            ),
                          ],
                        ),
                      ),

                      SizedBox(height: 8),

                      // Bottom buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // Cancel button
                          TextButton(
                            onPressed: () {
                              // Navigator.of(context).pop();
                            },
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 30, vertical: 10),
                              side: BorderSide(
                                  color: Color(0xffD0D0D0), width: 1),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                                fontFamily: 'TiemposText',
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),

                          SizedBox(width: 20),

                          // Previous button (show in entity, workflow, and workflowLO creation steps)
                          if (provider.currentStep ==
                                  WorkflowStep.entityCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowLoCreation)
                            ElevatedButton(
                              onPressed: provider.goToPreviousStep,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey.shade600,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 30, vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                              child: Text(
                                'Previous',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontFamily: 'TiemposText',
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),

                          if (provider.currentStep ==
                                  WorkflowStep.entityCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowCreation ||
                              provider.currentStep ==
                                  WorkflowStep.workflowLoCreation)
                            SizedBox(width: 20),

                          // Main action button
                          ElevatedButton(
                            onPressed: _isValidating(provider)
                                ? null
                                : () => _handleButtonPress(provider),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isValidating(provider)
                                  ? Colors.grey.shade400
                                  : Color(0xff0058FF),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 30, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                            child: _isValidating(provider)
                                ? Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Validating...',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                          fontFamily: 'TiemposText',
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ],
                                  )
                                : Text(
                                    _getButtonText(provider),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontFamily: 'TiemposText',
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                    ],
                  ),
                ),

                (provider.showSidePanel || provider.showWorkflowLoSidePanel)
                    ? Container(
                        width: MediaQuery.of(context).size.width >= 1900
                            ? AppSpacing.spaceBetweenMainContentAndSidePanel
                            : AppSpacing
                                .spaceBetweenMainContentAndSidePanelForSmallScreens)
                    : Expanded(child: SizedBox())
              ],
            ),
          ),
          // Side panel for role details or workflowLO details
          if (provider.showSidePanel)
            ResizablePanel(
              width: provider.sidePanelWidth,
              minWidth: provider.minSidePanelWidth,
              maxWidth: provider.maxSidePanelWidth,
              handlePosition: ResizeHandlePosition.left,
              onResize: (newWidth) {
                provider.updateSidePanelWidth(newWidth);
              },
              child: RoleDetailsPanel(
                role: provider.selectedRole!,
                onClose: () {
                  provider.hideSidePanel();
                },
                showLegacySections:
                    false, // Use new dedicated fields (CR/KPI/DA)
                users: provider
                    .extractedUsers, // Pass user data for role assignments
              ),
            )
          else if (provider.showWorkflowLoSidePanel)
            ResizablePanel(
              width: provider.sidePanelWidth,
              minWidth: provider.minSidePanelWidth,
              maxWidth: provider.maxSidePanelWidth,
              handlePosition: ResizeHandlePosition.left,
              onResize: (newWidth) {
                provider.updateSidePanelWidth(newWidth);
              },
              child: WorkflowLoDetailsPanel(
                workflowLoData: provider.selectedWorkflowLoData!,
                onClose: () {
                  provider.hideWorkflowLoSidePanel();
                },
              ),
            )
          else if (provider.showSideEntityPanel)
            ResizablePanel(
              width: provider.sidePanelWidth,
              minWidth: provider.minSidePanelWidth,
              maxWidth: provider.maxSidePanelWidth,
              handlePosition: ResizeHandlePosition.left,
              onResize: (newWidth) {
                provider.updateSidePanelWidth(newWidth);
              },
              child: _buildEntityDetailsPanel(
                provider.selectedEntity!,
                () => provider.hideEntitySidePanel(),
              ),
            )
        ],
      ),
    );
  }

  Widget _buildEntityDetailsPanel(
      entities_model.Entity entity, VoidCallback? onClose) {
    GlobalKey statefulBuilderKey = GlobalKey();
    double bottomSpacerHeight = 500;
    bool isProgrammaticScroll = false;
    bool hasUserScrolled = false;

    // Define the sections for navigation with their abbreviations
    final Map<String, String> sectionMap = {
      'attributes': 'ATT',
      'business_rules': 'BR',
      'relationships': 'REL',
      //'circuit_board': 'CB',
      'constants_validations': 'CV',
      // 'agents': 'AG',
      // 'workflow': 'WF',
    };

    // Create a scroll controller to handle scrolling to sections
    final ScrollController scrollController = ScrollController();

    // Keys for each section to enable scrolling to them
    final Map<String, GlobalKey> sectionKeys = {
      for (final key in sectionMap.keys) key: GlobalKey(),
    };
    scrollController.addListener(() {
      if (!isProgrammaticScroll && !hasUserScrolled) {
        statefulBuilderKey.currentState?.setState(() {
          hasUserScrolled = true;
          bottomSpacerHeight = 100;
        });
        // setState(() {
        //   hasUserScrolled = true;
        //   bottomSpacerHeight = 100; // Remove extra space when user scrolls
        // });
      }

      // isProgrammaticScroll = false;
    });

    // Function to scroll to a specific section
    void scrollToSection(String sectionId) {
      if (sectionKeys.containsKey(sectionId)) {
        final renderObject =
            sectionKeys[sectionId]?.currentContext?.findRenderObject();
        if (renderObject != null) {
          final sectionIndex = sectionMap.keys.toList().indexOf(sectionId);

          statefulBuilderKey.currentState?.setState(() {
            isProgrammaticScroll = true;
            hasUserScrolled = false;
            selectedSectionIndex = sectionIndex;

            // Calculate required bottom padding based on section position
            if (sectionIndex >= 4) {
              // Last 3 sections (CV=4, AG=5, WF=6)
              bottomSpacerHeight = MediaQuery.of(context).size.height - 100;
            } else {
              bottomSpacerHeight = 500;
            }
          });

          // Delay scroll to ensure padding is applied
          Future.delayed(Duration(milliseconds: 100), () {
            scrollController.position
                .ensureVisible(
              renderObject,
              alignment: 0.0,
              duration: Duration(milliseconds: 600),
              curve: Curves.easeInOut,
            )
                .then((_) {
              // Reset flag after animation completes
              isProgrammaticScroll = false;
            });
          });
        }
      }
    }

    return StatefulBuilder(
        key: statefulBuilderKey,
        builder: (context, innerSetState) {
          return Container(
            width: 400,
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with entity title and close button
                Container(
                  padding: const EdgeInsets.only(
                      left: AppSpacing.xxl,
                      right: AppSpacing.xxs,
                      bottom: AppSpacing.xxs,
                      top: AppSpacing.xxs),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            const CircleAvatar(
                              backgroundColor: Color(0xffE6F7FF),
                              radius: 10,
                              child: Icon(
                                Icons.person_outline,
                                color: Color(0xff1890FF),
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: AppSpacing.xs),
                            Expanded(
                              child: Text(
                                entity.title ?? 'Entity Details',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black,
                                  fontFamily: 'TimeposText',
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: onClose,
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),

                // Navigation and content
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left navigation
                      Container(
                        width: 40,
                        padding: const EdgeInsets.only(top: 3),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            for (int i = 0; i < sectionMap.length; i++)
                              Padding(
                                padding: EdgeInsets.only(
                                    left: 8, top: i == 0 ? 16 : 10),
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () => scrollToSection(
                                        sectionMap.keys.elementAt(i)),
                                    child: Text(
                                      sectionMap.values.elementAt(i),
                                      style: TextStyle(
                                        fontWeight: selectedSectionIndex == i
                                            ? FontWeight.bold
                                            : FontWeight.w500,
                                        fontSize: 10,
                                        color: selectedSectionIndex == i
                                            ? Colors.black
                                            : Colors.grey.shade700,
                                        decoration: TextDecoration.underline,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Right content area with scrollable sections
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return SingleChildScrollView(
                              controller: scrollController,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  for (final sectionId in sectionMap.keys)
                                    _buildEntitySectionWithKey(
                                      sectionId,
                                      entity,
                                      sectionKeys,
                                    ),

                                  // Smart bottom padding
                                  SizedBox(height: bottomSpacerHeight),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  Widget _buildEntitySectionWithKey(String sectionId,
      entities_model.Entity entity, Map<String, GlobalKey> sectionKeys) {
    return Container(
      key: sectionKeys[sectionId],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Container(
            padding: const EdgeInsets.only(
                left: AppSpacing.sm, right: AppSpacing.sm, top: AppSpacing.md),
            color: Colors.white,
            child: Text(
              _getSectionTitle(sectionId),
              style: const TextStyle(
                fontSize: 10,
                fontFamily: 'TiemposText',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.only(
                left: AppSpacing.md, right: AppSpacing.md),
            child: _buildContentForEntitySection(sectionId, entity),
          ),
        ],
      ),
    );
  }

  Widget _buildContentForEntitySection(
      String sectionId, entities_model.Entity entity) {
    // Special handling for attributes section - use entity's attributes data
    if (sectionId == 'attributes') {
      return _buildAttributesFromEntityData(entity);
    }

    if (sectionId == 'business_rules') {
      return _buildBusinessRulesFromEntityData(entity);
    }

    if (sectionId == 'relationships') {
      return _buildRelationFromEntityData(entity);
    }

    if (sectionId == 'constants_validations') {
      return _buildValidationsFromEntityData(entity);
    }

    // Try to get section data from API if available
    // EntitySection? apiSection = _getEntitySectionFromApi(sectionId, entity);
    //
    // if (apiSection != null) {
    //   return _buildApiBasedSectionContent(apiSection);
    // }

    // Only display content when it comes from the API - no fallback
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  Widget _buildApiBasedSectionContent(EntitySection section) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display section content
        if (section.content != null && section.content!.isNotEmpty)
          Text(
            section.content!,
            style: const TextStyle(fontSize: 14, fontFamily: 'TiemposText'),
          ),

        const SizedBox(height: AppSpacing.sm),

        // Display tabs if available
        if (section.tabs != null && section.tabs!.isNotEmpty)
          //   _buildTabsForSection(section)
          // Display entity data if available (for Data section)
          Container()
        else if (section.entityData != null && section.entityData!.isNotEmpty)
          _buildEntityDataSection(section)
        // Display section items if no tabs or entity data
        else if (section.items != null && section.items!.isNotEmpty)
          for (final item in section.items!)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: _buildSectionItem(item),
            ),
      ],
    );
  }

  Widget _buildEntityDataSection(EntitySection section) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (final dataGroup in section.entityData!)
          if (dataGroup.isNotEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                for (final dataItem in dataGroup)
                  Padding(
                    padding: const EdgeInsets.only(bottom: AppSpacing.xxs),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '• ',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                            fontFamily: 'TiemposText',
                          ),
                        ),
                        Expanded(
                          child: Text(
                            '${dataItem.name}: ${dataItem.value}',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                              fontFamily: 'TiemposText',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: AppSpacing.sm),
              ],
            ),
      ],
    );
  }
  // Build tab button

  // Build individual section item based on its type
  Widget _buildSectionItem(dynamic item) {
    // If item is a Map (structured data like business rules)
    if (item is Map<String, dynamic>) {
      return _buildStructuredItem(item);
    }

    // If item is a simple string, check if it already has a bullet point
    String itemText = item.toString();
    bool alreadyHasBullet = itemText.startsWith('• ');

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Only add bullet if the item doesn't already have one
        if (!alreadyHasBullet)
          const Text(
            '• ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        Expanded(
          child: Text(
            itemText,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        ),
      ],
    );
  }

  // Build structured item (like business rules with name, description, severity)
  Widget _buildStructuredItem(Map<String, dynamic> item) {
    // Build a simple text representation without card styling
    String displayText = '';

    // Handle Constants & Validations rule field
    if (item['rule'] != null) {
      displayText = item['rule'];
    }
    // Handle Business Rules fields
    else if (item['name'] != null || item['description'] != null) {
      if (item['name'] != null) {
        displayText += item['name'];
      }

      if (item['description'] != null) {
        if (displayText.isNotEmpty) displayText += ': ';
        displayText += item['description'];
      }

      if (item['severity'] != null) {
        if (displayText.isNotEmpty) displayText += ', ';
        displayText += 'Severity: ${item['severity']}';
      }
    }
    // Handle Workflow items with title and data
    else if (item['title'] != null) {
      displayText = item['title'];
      if (item['data'] != null && item['data'] is List) {
        displayText += ' (${(item['data'] as List).join(', ')})';
      }
    }

    // Add other fields if available
    if (item['entity'] != null) {
      if (displayText.isNotEmpty) displayText += ', ';
      displayText += 'Entity: ${item['entity']}';
    }

    if (item['field'] != null) {
      if (displayText.isNotEmpty) displayText += ', ';
      displayText += 'Field: ${item['field']}';
    }

    if (item['type'] != null) {
      if (displayText.isNotEmpty) displayText += ', ';
      displayText += 'Type: ${item['type']}';
    }

    if (item['via'] != null) {
      if (displayText.isNotEmpty) displayText += ', ';
      displayText += 'Via: ${item['via']}';
    }

    // Check if the text already has a bullet point
    bool alreadyHasBullet = displayText.startsWith('• ');

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Only add bullet if the text doesn't already have one
        if (!alreadyHasBullet)
          const Text(
            '• ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        Expanded(
          child: Text(
            displayText,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAttributesFromEntityData(entities_model.Entity entity) {
    // Look for the EntityElement in our global storage
    if (entity != null) {
      //  EntityElement entityElement = globalEntityElements[entity.id!]!;

      // Check if this entity has attributes data from API
      if (entity.attributeMetaDataList != null &&
          entity.attributeMetaDataList!.isNotEmpty) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            for (final attribute in entity.attributeMetaDataList!)
              Padding(
                padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // const Text(
                    //   '• ',
                    //   style: TextStyle(
                    //     fontSize: 14,
                    //     fontWeight: FontWeight.normal,
                    //     color: Colors.black,
                    //     fontFamily: 'TiemposText',
                    //   ),
                    // ),
                    // Expanded(
                    //   child: Text(
                    //     '${attribute.name}: ${attribute.description} (${attribute.type})',
                    //     style: const TextStyle(
                    //       fontSize: 14,
                    //       fontWeight: FontWeight.normal,
                    //       color: Colors.black,
                    //       fontFamily: 'TiemposText',
                    //     ),
                    //   ),
                    // ),

                    Expanded(child: generateAttributeSentence(attribute))
                  ],
                ),
              ),
          ],
        );
      }
    }

    // No attributes data available
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No attributes data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  Widget _buildBusinessRulesFromEntityData(entities_model.Entity entity) {
    // Look for the EntityElement in our global storage
    //  EntityElement entityElement = globalEntityElements[entity.id!]!;

    // Check if this entity has attributes data from API
    if (entity.relationshipProperties != null &&
        entity.relationshipProperties!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (final relationShipProp in entity.relationshipProperties!)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      '${relationShipProp.sourceEntity} and ${relationShipProp.targetEntity} have relationship. those are  ${relationShipProp.onDelete} and  ${relationShipProp.onUpdate}.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                  )
                ],
              ),
            ),
        ],
      );
    }

    // No attributes data available
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No attributes data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  Widget _buildRelationFromEntityData(entities_model.Entity entity) {
    // Look for the EntityElement in our global storage
    //  EntityElement entityElement = globalEntityElements[entity.id!]!;

    // Check if this entity has attributes data from API
    if (entity.relationships != null && entity.relationships!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (final relation in entity.relationships!)
            //n:
            //
            // -Loan has many-to-one relationship with Customer using Loan.customerId to Customer.customerId^PK
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      '${relation.sourceEntity} has ${relation.type} relationship with ${relation.targetEntity} using ${relation.joinCondition.replaceAll("=", "to")}.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                  )
                ],
              ),
            ),
        ],
      );
    }

    // No attributes data available
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No attributes data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  Widget _buildValidationsFromEntityData(entities_model.Entity entity) {
    // Look for the EntityElement in our global storage
    //  EntityElement entityElement = globalEntityElements[entity.id!]!;

    // Check if this entity has attributes data from API
    if (entity.validationsList != null && entity.validationsList!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          for (final validation in entity.validationsList!)
            //n:
            //
            // -Loan has many-to-one relationship with Customer using Loan.customerId to Customer.customerId^PK
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.sm),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      '${validation.attribute}  ${validation.constraintText}.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                  )
                ],
              ),
            ),
        ],
      );
    }

    // No attributes data available
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Text(
        'No attributes data available from API',
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey,
          fontFamily: 'TiemposText',
        ),
      ),
    );
  }

  // Get entity section from API data
  EntitySection? _getEntitySectionFromApi(
      String sectionId, entities_model.Entity entity) {
    // Look for the EntityElement in our global storage
    if (entity.id != null && globalEntityElements.containsKey(entity.id!)) {
      EntityElement entityElement = globalEntityElements[entity.id!]!;

      // Check if this entity has sections data from API
      if (entityElement.sections != null &&
          entityElement.sections!.isNotEmpty) {
        // Find the section by ID or abbreviation
        for (var section in entityElement.sections!) {
          // Match by section ID or abbreviation
          if (section.id == sectionId ||
              section.abbreviation ==
                  _getSectionAbbreviationFromId(sectionId)) {
            return section;
          }
        }
      }
    }

    // Return null if no API data is available - no fallback
    return null;
  }

  String _getSectionAbbreviationFromId(String sectionId) {
    switch (sectionId) {
      case 'attributes':
        return 'ATT';
      case 'business_rules':
        return 'BR';
      case 'relationships':
        return 'REL';
      case 'circuit_board':
        return 'CB';
      case 'constants_validations':
        return 'CV';
      case 'agents':
        return 'AG';
      case 'workflow':
        return 'WF';
      default:
        return sectionId.toUpperCase();
    }
  }

  String _getSectionTitle(String sectionId) {
    switch (sectionId) {
      case 'attributes':
        return 'Attributes';
      case 'business_rules':
        return 'Business Rules';
      case 'relationships':
        return 'Relationships';
      case 'circuit_board':
        return 'Circuit Board';
      case 'constants_validations':
        return 'Constants & Validations';
      case 'agents':
        return 'Agents';
      case 'workflow':
        return 'Workflows';
      default:
        return 'Section';
    }
  }

  Widget rightSideTooltips(ManualCreationProvider provider) {
    return Padding(
      padding: const EdgeInsets.only(right: 5.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // SizedBox(height: 20), // Align with text field

          // Agents icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/agent.svg',
            tooltipText: 'Agents',
            onTap: provider.handleAgentsTap,
          ),

          SizedBox(height: 10),

          // Entities icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/cube-box.svg',
            tooltipText: 'Entities',
            onTap: provider.handleDataSetsTap,
          ),

          SizedBox(height: 10),

          // Workflows icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/square-box-uncheck.svg',
            tooltipText: 'Workflows',
            onTap: provider.handleWorkflowsTap,
          ),
          SizedBox(height: 10),

          // WorkflowLOs icon
          _buildIconWithTooltip(
            provider: provider,
            iconPath: 'assets/images/workflow_lo.svg',
            tooltipText: 'WorkflowLOs',
            onTap: provider.handleWorkflowsloTap,
          ),
        ],
      ),
    );
  }

  // Show validation results in alert dialog
  void _showValidationAlertDialog(
      BuildContext context, ManualCreationProvider provider) {
    // Handle agent validation errors
    if (provider.validationError != null &&
        _lastShownError != provider.validationError) {
      _lastShownError = provider.validationError;
      _showErrorAlertDialog(
          context, 'Agent Validation Error', provider.validationError!);
    } else if (provider.validationResult != null &&
        provider.validationResult!.validationErrors != null &&
        provider.validationResult!.validationErrors!.isNotEmpty) {
      // Show agent validation errors in alert dialog
      final errors = provider.validationResult!.validationErrors!;
      final errorMessages =
          errors.map((e) => '• ${e.message ?? 'Validation error'}').toList();

      if (_lastShownResult != errorMessages.join('\n')) {
        _lastShownResult = errorMessages.join('\n');
        _showValidationErrorsAlertDialog(
            context, 'Agent Validation Errors', errorMessages);
      }
    } else if (provider.validationResult != null &&
        provider.showAgentTable &&
        _lastShownResult != 'agent_success') {
      // Show success message when agent table is shown
      _lastShownResult = 'agent_success';
      _showSuccessAlertDialog(context, 'Agent Validation Successful',
          'Agent data loaded successfully.');
    }

    // Handle entity validation errors
    else if (provider.entityValidationError != null &&
        _lastShownError != provider.entityValidationError) {
      _lastShownError = provider.entityValidationError;
      _showErrorAlertDialog(
          context, 'Entity Validation Error', provider.entityValidationError!);
    } else if (provider.entityValidationResult != null &&
        provider.showEntityTable &&
        _lastShownResult != 'entity_success') {
      // Show success message when entity table is shown
      _lastShownResult = 'entity_success';
      _showSuccessAlertDialog(context, 'Entity Validation Successful',
          'Entity data loaded successfully.');
    }

    // Handle workflow validation errors
    else if (provider.workflowValidationError != null &&
        _lastShownError != provider.workflowValidationError) {
      _lastShownError = provider.workflowValidationError;
      _showErrorAlertDialog(context, 'Workflow Validation Error',
          provider.workflowValidationError!);
    } else if (provider.workflowValidationResult != null &&
        provider.showWorkflowTable &&
        _lastShownResult != 'workflow_success') {
      // Show success message when workflow table is shown
      _lastShownResult = 'workflow_success';
      _showSuccessAlertDialog(context, 'Workflow Validation Successful',
          'Workflow data loaded successfully.');
    }

    // Handle workflowLO validation errors
    else if (provider.workflowLoValidationError != null &&
        _lastShownError != provider.workflowLoValidationError) {
      _lastShownError = provider.workflowLoValidationError;
      _showErrorAlertDialog(context, 'Workflow LO Validation Error',
          provider.workflowLoValidationError!);
    } else if (provider.workflowLoValidationResult != null &&
        provider.showWorkflowLoTable &&
        _lastShownResult != 'workflow_lo_success') {
      // Show success message when workflow LO table is shown
      _lastShownResult = 'workflow_lo_success';
      _showSuccessAlertDialog(context, 'Workflow LO Validation Successful',
          'Workflow LO data loaded successfully.');
    }
  }

  // Show error alert dialog
  void _showErrorAlertDialog(
      BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show validation errors alert dialog
  void _showValidationErrorsAlertDialog(
      BuildContext context, String title, List<String> messages) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.warning_outlined,
                        color: Colors.orange, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.orange,
                        ),
                      ),
                    ),
                  ],
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: messages
                        .map((message) => Padding(
                              padding: EdgeInsets.only(bottom: 8),
                              child: Text(
                                message,
                                style: TextStyle(
                                  fontSize: 14,
                                  fontFamily: 'TiemposText',
                                  color: Colors.black87,
                                ),
                              ),
                            ))
                        .toList(),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show success alert dialog
  void _showSuccessAlertDialog(
      BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.check_circle_outline,
                        color: Colors.green, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Helper methods for workflow
  String _getTitle(ManualCreationProvider provider) {
    // If an icon view is active, show that title
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          return provider.showAgentTable ? 'Agents' : 'Create Your Agents';
        case 'Entities':
          return provider.showEntityTable
              ? 'Entity List'
              : 'Create Your Entity';
        case 'Workflows':
          return provider.showWorkflowTable
              ? 'Workflow List'
              : 'Create Your Workflow';
        case 'WorkflowLOs':
          return provider.showWorkflowLoTable
              ? 'Workflow LO List'
              : 'Add your LO Details';
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable ? 'Agents' : 'Create Your Agents';
      case WorkflowStep.entityCreation:
        return provider.showEntityTable ? 'Entity List' : 'Create Your Entity';
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable
            ? 'Workflow List'
            : 'Create Your Workflow';
      case WorkflowStep.workflowLoCreation:
        return provider.showWorkflowLoTable
            ? 'Workflow LO List'
            : 'Add your LO Details';
    }
  }

  String _getButtonText(ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          return provider.showAgentTable ? 'Done' : 'Validate';
        case 'Entities':
          return provider.showEntityTable ? 'Done' : 'Validate';
        case 'Workflows':
          return provider.showWorkflowTable ? 'Done' : 'Validate';
        case 'WorkflowLOs':
          return provider.showWorkflowLoTable ? 'Finish' : 'Validate';
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable ? 'Next' : 'Validate';
      case WorkflowStep.entityCreation:
        return provider.showEntityTable ? 'Next' : 'Validate';
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable ? 'Next' : 'Validate';
      case WorkflowStep.workflowLoCreation:
        return provider.showWorkflowLoTable ? 'Finish' : 'Validate';
    }
  }

  bool _isValidating(ManualCreationProvider provider) {
    return provider.isValidating ||
        provider.isValidatingEntity ||
        provider.isValidatingWorkflow ||
        provider.isValidatingWorkflowLo;
  }

  void _handleButtonPress(ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          if (provider.showAgentTable) {
            // Handle next action for agents (could navigate or finish)
            _showSuccessAlertDialog(context, 'Agents Complete',
                'Agent data processed successfully.');
          } else {
            // Call agent validation API
            provider.handleValidate();
          }
          break;
        case 'Entities':
          if (provider.showEntityTable) {
            // Handle next action for entities (could navigate or finish)
            _showSuccessAlertDialog(context, 'Entities Complete',
                'Entity data processed successfully.');
          } else {
            // Call entity validation API
            provider.handleValidate();
          }
          break;
        case 'Workflows':
          if (provider.showWorkflowTable) {
            // Navigate to workflowLO step instead of finishing
            // provider.goToNextStep(); // This will go to workflowLoCreation
            _showSuccessAlertDialog(context, 'Workflows Complete',
                'Workflows data processed successfully.');
          } else {
            // Call entity validation API
            provider.handleValidate();
          }
          break;
        case 'WorkflowLOs':
          if (provider.showWorkflowLoTable) {
            // Handle finish action (navigate away or complete workflow)
            _handleFinishWorkflow(provider);
            _activeIconView =
                null; // Clear icon view to use workflow step logic
          } else {
            // Call workflow validation API
            provider.handleValidate();
          }
          break;
      }
      return;
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        if (provider.showAgentTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.entityCreation:
        if (provider.showEntityTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.workflowCreation:
        if (provider.showWorkflowTable) {
          provider.goToNextStep();
        } else {
          provider.handleValidate();
        }
        break;
      case WorkflowStep.workflowLoCreation:
        if (provider.showWorkflowLoTable) {
          // Handle finish action (navigate away or complete workflow)
          _handleFinishWorkflow(provider);
        } else {
          provider.handleValidate();
        }
        break;
    }
  }

  void _handleFinishWorkflow(ManualCreationProvider provider) {
    // Show success message and navigate back or reset
    _showSuccessAlertDialog(context, 'Workflow Complete',
        'Agent, entity, workflow, and workflow LO creation completed successfully.');
    // Optionally reset the workflow or navigate away
    // provider.resetWorkflow();
  }

  Widget _buildWorkflowContent(
      BuildContext context, ManualCreationProvider provider) {
    // Check if an icon view is active first
    if (_activeIconView != null) {
      switch (_activeIconView) {
        case 'Agents':
          // Show text field first, then table after validation
          return provider.showAgentTable
              ? _buildAgentTable(provider)
              : _buildTextField(context, provider);
        case 'Entities':
          // Show text field first, then table after validation
          return provider.showEntityTable
              ? _buildEntityTable(provider)
              : _buildTextField(context, provider);
        case 'Workflows':
          // Show text field first, then table after validation
          return provider.showWorkflowTable
              ? _buildWorkflowTable(provider)
              : _buildTextField(context, provider);
        case 'WorkflowLOs':
          // Show text field first, then table after validation
          return provider.showWorkflowLoTable
              ? _buildWorkflowLoWithWorkflowStructure(provider)
              : _buildTextField(context, provider);
      }
    }

    // Otherwise, use the existing workflow logic
    switch (provider.currentStep) {
      case WorkflowStep.agentCreation:
        return provider.showAgentTable
            ? _buildAgentTable(provider)
            : _buildTextField(context, provider);
      case WorkflowStep.entityCreation:
        return provider.showEntityTable
            ? _buildEntityTable(provider)
            : _buildTextField(context, provider);
      case WorkflowStep.workflowCreation:
        return provider.showWorkflowTable
            ? _buildWorkflowTable(provider)
            : _buildTextField(context, provider);
      case WorkflowStep.workflowLoCreation:
        return provider.showWorkflowLoTable
            ? _buildWorkflowLoWithWorkflowStructure(provider)
            : _buildTextField(context, provider);
    }
  }

  Widget _buildTextField(
      BuildContext context, ManualCreationProvider provider) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          focusColor: Colors.transparent,
        ),
        child: TextField(
          controller: provider.textController,
          maxLines: null,
          expands: true,
          textAlignVertical: TextAlignVertical.top,
          decoration: InputDecoration(
            hintStyle: TextStyle(
              color: Colors.grey.shade500,
              fontSize: 16,
              fontFamily: 'TiemposText',
            ),
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            contentPadding: EdgeInsets.all(16),
          ),
          style: TextStyle(
            fontSize: 16,
            fontFamily: 'TiemposText',
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildEntityTable(ManualCreationProvider provider) {
    if (provider.extractedEntityData == null ||
        provider.extractedEntityData!.entityGroups == null ||
        provider.extractedEntityData!.entityGroups!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No entity data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Entities and Relationships',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${provider.extractedEntityData!.entityGroups!.length} Entity Group${provider.extractedEntityData!.entityGroups!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.green.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Entity rows
          for (var group in provider.extractedEntityData!.entityGroups ?? [])
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Group header

                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: AppSpacing.xxs),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    // borderRadius: BorderRadius.only(
                    //   bottomLeft: Radius.circular(AppSpacing.sm),
                    //   bottomRight: Radius.circular(AppSpacing.sm),
                    // ),
                    color: Colors.white,
                  ),
                  child: Column(
                    children: (group.entities ?? []).map<Widget>((entity) {
                      // Create a non-nullable list for the _buildEntityCard method
                      final List<entities_model.Entity> entityList =
                          group.entities ?? [];
                      return _buildEntityCard(
                          entity, entityList, group, provider);
                    }).toList(),
                  ),
                ),

                // (provider.extractedEntityData!.entityGroups?.indexOf(group) ?? 0) <
                //     (provider.extractedEntityData!.entityGroups?.length ?? 1) - 1
                //     ? SizedBox(height: AppSpacing.xl)
                //     : SizedBox(height: 0),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildEntityCard(
      entities_model.Entity entity,
      List<entities_model.Entity> allEntities,
      entities_model.EntityGroup group,
      ManualCreationProvider provider) {
    // If this is a relationship entity, hide it
    if (entity.relationType != null) {
      return SizedBox.shrink();
    }

    // If this is the first entity and we're showing related entities, highlight it
    // (No need to show a snackbar here as it's already shown in the click handler)

    final bool isSelected = provider.selectedEntity != null &&
        provider.selectedEntity!.id == entity.id;

    // No tooltip highlighting needed

    // Create a unique global key for thprs entity card
    final GlobalKey entityCardKey =
        GlobalKey(debugLabel: 'entityCard_${entity.id}');

    // Check if the entire title row would be truncated
    bool wouldBeTruncated = false;

    double availableWidth = MediaQuery.of(context).size.width / 1.55;
    // availableWidth = availableWidth -
    //     (showSidePanel ? sidePanelWidth : 0) -
    //     (isChatHistoryExpanded ? MediaQuery.of(context).size.width / 6 : 0) -
    //     100;

    // Create a combined string of the entity title and attributes for truncation check
    String fullTitleText = entity.title ?? 'Untitled';
    if (entity.attributeString != null && entity.attributeString!.isNotEmpty) {
      fullTitleText += ' has ${entity.attributeString!}';
    }

    // Check if the combined text would be truncated
    wouldBeTruncated = _wouldTextBeTruncated(
        fullTitleText,
        TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
          color: Colors.black,
          fontFamily: 'TiemposText',
        ),
        availableWidth);

    // Removed verbose entity text logging

    // If there are related entities, we should use expansion tile regardless
    final bool shouldUseExpansionTile = wouldBeTruncated;

    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(0),
          decoration: BoxDecoration(
            color: isSelected ? Color(0xFFE3F2FD) : Colors.white,
            border: Border(
              top: BorderSide(color: Colors.grey.shade200),
              left: isSelected
                  ? BorderSide(color: Colors.blue.shade700, width: 4)
                  : BorderSide.none,
            ),
          ),
          child: Column(
            children: [
              //    shouldUseExpansionTile
              // ?
              CustomExpansionTileWithEllipsis(
                entity: entity,
                entityCardKey: entityCardKey,
                onExpansionChanged: (expanded) {
                  setState(() {
                    // Update the local entity for immediate UI response
                    entity.expanded = expanded;

                    // Update the entity's expanded state in the global data
                    provider.extractedEntityData
                        ?.updateEntityExpandedState(entity.id ?? '', expanded);
                  });
                },
                backgroundColor: Colors.transparent,
                children: [],
                onThreeDotsPressed: () {
                  // Handle three dots menu action for entity
                  print('Three dots pressed for entity: ${entity.title}');
                  // You can add your custom logic here, such as:
                  // - Show context menu
                  // - Open entity options dialog
                  // - Navigate to entity details
                  // - etc.
                },
                onTitleTap: () {
                  // Update last click time to prevent tooltip from showing
                  _lastClickTime = DateTime.now();

                  // Hide any existing tooltip when clicking
                  // hideProfileTooltip();

                  setState(() {
                    if (provider.selectedEntity != null &&
                        provider.selectedEntity!.id == entity.id) {
                      // Keep selected
                    } else {
                      provider.showEntityDetailsPanel(entity);
                    }
                    //  showSidePanel = true;
                    selectedSectionIndex = 0;
                  });
                },
                showThreeDots: false,
              )
            ],
          ),
        ),
      ],
    );
  }

  bool _wouldTextBeTruncated(String text, TextStyle style, double maxWidth) {
    // First, measure the actual width of the text without constraints
    final TextPainter measuringPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: double.infinity);

    // Get the actual width of the text
    final double actualTextWidth = measuringPainter.width;

    // Removed verbose text measurement logging

    // Return true if the actual width exceeds the maximum width
    return actualTextWidth > maxWidth;
  }

  Widget _buildWorkflowTable(ManualCreationProvider provider) {
    final workflowData = provider.getLinearWorkflowData();

    if (workflowData == null) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No workflow data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    print('🌳 Rendering linear workflow tree');

    // Use the new process flow diagram widget
    return _buildProcessFlowDiagram(workflowData);
  }

  Widget _buildWorkflowLoWithWorkflowStructure(
      ManualCreationProvider provider) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left side - Existing workflow structure
        Expanded(
          flex: 1,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Color(0xffD0D0D0), width: 1),
              borderRadius: BorderRadius.circular(AppSpacing.xxs),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade300, width: 1),
                    ),
                  ),
                  child: Text(
                    'Existing Workflow Structure',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'TiemposText',
                      color: Colors.black,
                    ),
                  ),
                ),
                // Workflow content
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: _buildWorkflowTable(provider),
                  ),
                ),
              ],
            ),
          ),
        ),

        // SizedBox(width: 16),

        // Right side - WorkflowLO table
      ],
    );
  }

  Widget _buildWorkflowLoTable(ManualCreationProvider provider) {
    if (provider.extractedWorkflowLoData == null ||
        provider.extractedWorkflowLoData!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No workflow LO data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Workflow Local Objectives',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${provider.extractedWorkflowLoData!.length} LO${provider.extractedWorkflowLoData!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // WorkflowLO rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: provider.extractedWorkflowLoData!.length,
              itemBuilder: (context, index) {
                final workflowLo = provider.extractedWorkflowLoData![index];

                return Container(
                  margin: EdgeInsets.only(bottom: 12),
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300, width: 1),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // WorkflowLO name
                      Text(
                        workflowLo['name'] ?? 'Unknown Workflow LO',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 8),

                      // WorkflowLO details
                      Row(
                        children: [
                          Text(
                            'Version: ${workflowLo['version'] ?? 'N/A'}',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: 'TiemposText',
                              color: Colors.grey.shade600,
                            ),
                          ),
                          SizedBox(width: 16),
                          Text(
                            'Status: ${workflowLo['status'] ?? 'N/A'}',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: 'TiemposText',
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Agent Type: ${workflowLo['agentType'] ?? 'N/A'}',
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'TiemposText',
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProcessFlowDiagram(Map<String, dynamic> workflowData) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Workflow content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Main workflow title
                  _buildWorkflowTitle(workflowData),
                  SizedBox(height: 20),

                  // Process flow diagram
                  _buildProcessFlowTree(workflowData),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkflowTitle(Map<String, dynamic> workflowData) {
    final title = workflowData['title'] ?? '';

    // Only show title if it's not empty
    if (title.isEmpty) {
      return Text(
        'No workflow title available from API response',
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          fontFamily: 'TiemposText',
          color: Colors.black,
          // fontStyle: FontStyle.italic,
        ),
      );
    }

    return Text(
      title,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w700,
        fontFamily: 'TiemposText',
        color: Colors.black,
        // fontStyle: FontStyle.italic,
      ),
    );
  }

  // Build process flow tree based on the provided process_flow data
  Widget _buildProcessFlowTree(Map<String, dynamic> workflowData) {
    // Get the extracted workflow data from the provider
    final provider =
        Provider.of<ManualCreationProvider>(context, listen: false);
    final extractedWorkflowData = provider.extractedWorkflowData;

    if (extractedWorkflowData == null || extractedWorkflowData.isEmpty) {
      return Text(
        'No process flow data available',
        style: TextStyle(
          fontSize: 12,
          fontFamily: 'TiemposText',
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    // Get the process flow from the first workflow
    final workFlowDetails = extractedWorkflowData.first['workFlowDetails'];
    final processFlow = workFlowDetails?.processFlow;

    if (processFlow == null || processFlow.isEmpty) {
      return Text(
        'No process flow nodes found',
        style: TextStyle(
          fontSize: 12,
          fontFamily: 'TiemposText',
          color: Colors.grey.shade600,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    // Build the tree structure based on your description
    return
        // _buildProcessedFlowTree(processFlow);

        _buildProcessFlowTreeStructure(processFlow);
  }

  // Widget _buildProcessedFlowTree(processFlow) {
  //   // Convert processFlow into required treeData format for WorkflowTreeBuilder
  //   List<dynamic> treeData =
  //       Provider.of<ManualCreationProvider>(listen: false, context)
  //           .convertProcessFlowToTreeData(processFlow);

  //   // Use WorkflowTreeBuilder to build the workflow tree
  //   return WorkflowTreeBuilder.buildWorkflowTree(treeData);
  // }

  // // Build children for a node based on its routing information
  // List<Map<String, dynamic>> _buildNodeChildren(
  //     Map<String, dynamic> node, Map<String, Map<String, dynamic>> nodeMap) {
  //   List<Map<String, dynamic>> children = [];

  //   try {
  //     String routeType = node['routeType'] ?? '';

  //     switch (routeType.toLowerCase()) {
  //       case 'alternate':
  //         // Handle conditional routing - create alternative paths
  //         List<dynamic> conditions = node['conditions'] ?? [];
  //         for (int i = 0; i < conditions.length; i++) {
  //           var condition = conditions[i];
  //           String routeTo = condition['routeTo'] ?? '';
  //           if (routeTo.isNotEmpty && nodeMap.containsKey(routeTo)) {
  //             Map<String, dynamic> childNode = Map.from(nodeMap[routeTo]!);
  //             childNode['conditionText'] = condition['condition'] ?? '';
  //             childNode['altIndex'] = i + 1;
  //             children.add(childNode);
  //           }
  //         }
  //         break;

  //       case 'sequential':
  //         // Handle sequential routing
  //         List<dynamic> routes = node['routes'] ?? [];
  //         for (String route in routes) {
  //           if (route.isNotEmpty && nodeMap.containsKey(route)) {
  //             children.add(Map.from(nodeMap[route]!));
  //           }
  //         }
  //         break;

  //       case 'parallel':
  //         // Handle parallel routing
  //         List<dynamic> parallelRoutes = node['parallelRoutes'] ?? [];
  //         for (int i = 0; i < parallelRoutes.length; i++) {
  //           var parallelRoute = parallelRoutes[i];
  //           String routeTo = parallelRoute['routeTo'] ?? '';
  //           if (routeTo.isNotEmpty && nodeMap.containsKey(routeTo)) {
  //             Map<String, dynamic> childNode = Map.from(nodeMap[routeTo]!);
  //             childNode['isParallel'] = true;
  //             childNode['parallelDescription'] =
  //                 parallelRoute['description'] ?? '';
  //             childNode['parallelIndex'] = i + 1;
  //             children.add(childNode);
  //           }
  //         }
  //         break;
  //     }
  //   } catch (e) {
  //     Logger.error('Error building node children: $e');
  //   }

  //   return children;
  // }

// Build the process flow tree structure based on the provided process_flow data
  Widget _buildProcessFlowTreeStructure(List<dynamic> processFlow) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Container(
          padding: EdgeInsets.all(20),
          child: _buildProcessColumnsWidget(processFlow),
        ),
      ),
    );
  }

  // Build widget from process columns
  Widget _buildProcessColumnsWidget(List<dynamic> processFlow) {
    final provider =
        Provider.of<ManualCreationProvider>(context, listen: false);
    final columns = provider.buildProcessColumns(processFlow);
    List<Color> colors = provider.pastelColors;

    // Create a mapping to track node connections and styling
    Map<String, Map<String, dynamic>> nodeConnections = {};

    // Analyze the process flow to determine connections
    provider.analyzeNodeConnections(processFlow, nodeConnections, colors);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: columns.asMap().entries.map((entry) {
        int columnIndex = entry.key;
        List<dynamic> column = entry.value;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: column.asMap().entries.expand((nodeEntry) {
            int nodeIndex = nodeEntry.key;
            dynamic node = nodeEntry.value;
            List<Widget> widgets = [];

            // Handle special nodes
            if (provider.isEmptyNode(node)) {
              widgets.add(SizedBox(height: node.height?.toDouble() ?? 30.0));
            } else if (provider.isTerminalNode(node)) {
              widgets.add(_buildTerminalIcon());
            } else if (provider.isPausedNode(node)) {
              // Handle paused nodes - show them with "Paused" route type
              String nodeName = node.loName ?? 'Unknown';
              String routeType = 'Paused';

              // Determine styling based on node connections analysis
              bool isLinkStart = false;
              bool isLinkEnd = false;
              Color? boxColor;

              // Check if this node has connection styling using column-specific key
              String nodeKey = '${nodeName}_$columnIndex';
              String? alternateTag;
              String? parallelTag;
              if (nodeConnections.containsKey(nodeKey)) {
                final connectionInfo = nodeConnections[nodeKey]!;
                isLinkStart = connectionInfo['isLinkStart'] ?? false;
                isLinkEnd = connectionInfo['isLinkEnd'] ?? false;
                boxColor = connectionInfo['boxColor'];
                alternateTag = connectionInfo['alternateTag'];
                parallelTag = connectionInfo['parallelTag'];
              }

              Widget nodeWidget = _buildNodeWidget(
                nodeName,
                routeType,
                isLinkStart: isLinkStart,
                isLinkEnd: isLinkEnd,
                boxColor: boxColor,
                alternateTag: alternateTag,
                parallelTag: parallelTag,
              );

              widgets.add(nodeWidget);
            } else {
              // Handle ProcessFlow objects only
              String nodeName = node.loName ?? 'Unknown';
              String routeType = node.routeType ?? '';

              // Determine styling based on node connections analysis
              bool isLinkStart = false;
              bool isLinkEnd = false;
              Color? boxColor;

              // Check if this node has connection styling using column-specific key
              String nodeKey = '${nodeName}_$columnIndex';
              String? alternateTag;
              String? parallelTag;
              if (nodeConnections.containsKey(nodeKey)) {
                final connectionInfo = nodeConnections[nodeKey]!;
                isLinkStart = connectionInfo['isLinkStart'] ?? false;
                isLinkEnd = connectionInfo['isLinkEnd'] ?? false;
                boxColor = connectionInfo['boxColor'];
                alternateTag = connectionInfo['alternateTag'];
                parallelTag = connectionInfo['parallelTag'];
              }

              Widget nodeWidget = _buildNodeWidget(
                nodeName,
                routeType,
                isLinkStart: isLinkStart,
                isLinkEnd: isLinkEnd,
                boxColor: boxColor,
                alternateTag: alternateTag,
                parallelTag: parallelTag,
              );

              // Add connections for alternate routing
              if (routeType == 'Alternate') {
                widgets.add(Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    nodeWidget,
                    _buildAlternateConnection(),
                  ],
                ));
              } else {
                widgets.add(nodeWidget);
              }
            }

            // Add spacing between widgets (except for the last one)
            if (nodeIndex < column.length - 1) {
              widgets.add(SizedBox(height: 50));
            }

            return widgets;
          }).toList(),
        );
      }).toList(),
    );
  }

// // Build the complete tree structure according to your specifications
//   Widget _buildTreeStructure(List<dynamic> processFlow) {
//     List colors = Provider.of<ManualCreationProvider>(context, listen: false)
//         .pastelColors;
//     return Row(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 _buildNodeWidget('SubmitLeaveRequest', 'Alternate'),
//                 _buildAlternateConnection(),
//               ],
//             ),
//           ],
//         ),
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             _buildNodeWidget(
//               'UploadDocumentation',
//               'Sequential',
//             ),
//             const SizedBox(
//               height: 50,
//             ),
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 _buildNodeWidget('ReviewLeaveRequest', 'Alternate',
//                     isLinkStart: true, boxColor: colors[0]),
//                 _buildAlternateConnection(),
//               ],
//             ),
//           ],
//         ),
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             _buildNodeWidget('ReviewLeaveRequest', '',
//                 isLinkEnd: true, boxColor: colors[0]),
//             const SizedBox(
//               height: 50,
//             ),
//             _buildNodeWidget('ApproveLeaveRequest', 'Sequential'),
//             const SizedBox(
//               height: 50,
//             ),
//             _buildNodeWidget('RejectLeaveRequest', 'Sequential',
//                 isLinkEnd: true, boxColor: colors[1]),
//           ],
//         ),
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const SizedBox(
//               height: 30,
//             ),
//             const SizedBox(
//               height: 50,
//             ),
//             _buildNodeWidget('NotifyEmployee', 'Parallel',
//                 isLinkStart: true, boxColor: colors[1]),
//           ],
//         ),
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const SizedBox(
//               height: 30,
//             ),
//             const SizedBox(
//               height: 50,
//             ),
//             Row(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 _buildNodeWidget('UpdateCalendar', 'Alternate'),
//                 _buildAlternateConnection(),
//               ],
//             ),
//             const SizedBox(
//               height: 50,
//             ),
//             _buildNodeWidget('LogAuditTrail', 'Sequential',
//                 isLinkEnd: true, boxColor: colors[3]),
//           ],
//         ),
//         Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const SizedBox(
//               height: 30,
//             ),
//             const SizedBox(
//               height: 50,
//             ),
//             _buildNodeWidget('UpdateLeaveBalance', 'Sequential',
//                 isLinkStart: true, boxColor: colors[3]),
//             const SizedBox(
//               height: 50,
//             ),
//             _buildTerminalIcon(),
//           ],
//         ),
//         Column(
//           children: [
//             const SizedBox(
//               height: 30,
//             ),
//             const SizedBox(
//               height: 50,
//             ),
//             _buildTerminalIcon(),
//           ],
//         ),
//       ],
//     );
//   }

// Build a simple node widget
  Widget _buildNodeWidget(String nodeName, String routeType,
      {bool isLinkStart = false,
      bool isLinkEnd = false,
      Color? boxColor,
      String? alternateTag,
      String? parallelTag}) {
    return Container(
      // color: Colors.red,
      child: Row(
        children: [
          CustomPaint(
            size: Size(40, 1),
            painter: HorizontalDottedLinePainter(
              color: Color(0xff797676),
              dashLength: 3,
              dashGap: 3,
            ),
          ),
          boxColor != null
              ? Row(
                  children: [
                    // Add alternate tag if present
                    if (alternateTag != null)
                      Text(
                        alternateTag,
                        style: TextStyle(
                          fontSize: 10,
                          fontFamily: 'TiemposText',
                          color: Color(0xff5D5D5D),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    // Add parallel tag if present
                    if (parallelTag != null)
                      Text(
                        parallelTag,
                        style: TextStyle(
                          fontSize: 10,
                          fontFamily: 'TiemposText',
                          color: Color(0xff5D5D5D),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    Container(
                      margin: EdgeInsets.only(
                          left: AppSpacing.xxs, right: AppSpacing.xs),
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border:
                            Border.all(color: Color(0xff797676), width: 0.5),
                        color: Colors.white,
                      ),
                    ),

                    isLinkStart
                        ? Padding(
                            padding:
                                const EdgeInsets.only(right: AppSpacing.xxs),
                            child: _buildLinkIcon(boxColor: boxColor),
                          )
                        : SizedBox(),
                    Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(AppSpacing.xxs),
                          color: boxColor),
                      padding: EdgeInsets.all(AppSpacing.xxs),
                      child: Text(
                        nodeName,
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'TiemposText',
                          color: Colors.black87,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    isLinkEnd
                        ? Padding(
                            padding:
                                const EdgeInsets.only(left: AppSpacing.xxs),
                            child: _buildLinkIcon(boxColor: boxColor),
                          )
                        : SizedBox(),
                    SizedBox(
                      width: AppSpacing.xs,
                    ),
                  ],
                )
              : Row(
                  children: [
                    // Add alternate tag if present
                    if (alternateTag != null)
                      Text(
                        alternateTag,
                        style: TextStyle(
                          fontSize: 10,
                          fontFamily: 'TiemposText',
                          color: Color(0xff5D5D5D),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    // Add parallel tag if present
                    if (parallelTag != null)
                      Text(
                        parallelTag,
                        style: TextStyle(
                          fontSize: 10,
                          fontFamily: 'TiemposText',
                          color: Color(0xff5D5D5D),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    Container(
                      margin: EdgeInsets.only(
                          left: AppSpacing.xxs, right: AppSpacing.xs),
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border:
                            Border.all(color: Color(0xff797676), width: 0.5),
                        color: Colors.white,
                      ),
                    ),

                    Container(
                      padding: EdgeInsets.all(AppSpacing.xxs),
                      child: Text(
                        nodeName,
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'TiemposText',
                          color: Colors.black87,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),

                    SizedBox(
                      width: AppSpacing.xs,
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  // Build terminal icon for workflow end
  Widget _buildTerminalIcon() {
    return Container(
      padding: EdgeInsets.all(AppSpacing.xxs),
      child: Row(
        children: [
          CustomPaint(
            size: Size(30, 1),
            painter: HorizontalDottedLinePainter(
              color: Color(0xff797676),
              dashLength: 3,
              dashGap: 3,
            ),
          ),
          SvgPicture.asset(
            'assets/images/workflow/terminal_icon.svg',
            width: 20,
            height: 20,
            // colorFilter: ColorFilter.mode(Colors.red.shade600, BlendMode.srcIn),
          ),
        ],
      ),
    );
  }

  // Build link icon for connections
  Widget _buildLinkIcon({Color? boxColor}) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppSpacing.xxs), color: boxColor),
      padding: EdgeInsets.all(AppSpacing.xxs),
      child: SvgPicture.asset(
        'assets/images/workflow/link_icon.svg',
        width: 20,
        height: 20,
        colorFilter: ColorFilter.mode(Colors.blue.shade600, BlendMode.srcIn),
      ),
    );
  }

  // Widget _buildRootConnection() {
  //   return CustomPaint(
  //     size: Size(1, 15),
  //     painter: DottedLinePainter(
  //       color: Color(0xff797676),
  //       dashLength: 3,
  //       dashGap: 3,
  //     ),
  //   );
  // }

  Widget _buildAlternateConnection() {
    return Padding(
      padding: const EdgeInsets.only(top: AppSpacing.sm, right: AppSpacing.xxs),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomPaint(
            size: Size(20, 1),
            painter: HorizontalDottedLinePainter(
              color: Color(0xff797676),
              dashLength: 3,
              dashGap: 3,
            ),
          ),
          const SizedBox(
            width: AppSpacing.xxs,
          ),
          CustomPaint(
            size: Size(1, 80),
            painter: DottedLinePainter(
              color: Color(0xff797676),
              dashLength: 3,
              dashGap: 3,
            ),
          ),
        ],
      ),
    );
  }

  // Build agent table similar to web_home_screen_chat.dart
  Widget _buildAgentTable(ManualCreationProvider provider) {
    if (provider.extractedAgentData == null ||
        provider.extractedAgentData!.agents == null ||
        provider.extractedAgentData!.agents!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          borderRadius: BorderRadius.circular(AppSpacing.xxs),
        ),
        child: Center(
          child: Text(
            'No agent data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffD0D0D0), width: 1),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      padding: EdgeInsets.all(AppSpacing.xs),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
                vertical: AppSpacing.xs, horizontal: AppSpacing.sm),
            decoration: BoxDecoration(
              // color: Colors.grey.shade50,
              border: Border.all(color: Color(0xffD0D0D0)),
            ),
            child: Row(
              children: [
                Text(
                  'Roles',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: AppSpacing.xs, vertical: AppSpacing.xxs),
                  decoration: BoxDecoration(
                    color: Color(0xffFFE5B4),
                    borderRadius: BorderRadius.circular(AppSpacing.xxs),
                  ),
                  child: Text(
                    '${provider.extractedAgentData!.agents!.length} Agent${provider.extractedAgentData!.agents!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Agent rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: provider.extractedAgentData!.agents!.length,
              itemBuilder: (context, index) {
                final agent = provider.extractedAgentData!.agents![index];

                // Convert AgentInfo to RoleInfo for BuildRoleCard
                final role = RoleInfo(
                  id: agent.id,
                  title: agent.title,
                  description: agent.description,
                  version: agent.version,
                  createdBy: agent.createdBy,
                  createdDate: _formatDate(agent.createdDate ?? DateTime.now()),
                  modifiedBy: agent.modifiedBy,
                  modifiedDate:
                      _formatDate(agent.modifiedDate ?? DateTime.now()),
                  // Extract use cases from agent sections
                  coreResponsibilities: agent.sections
                      .where((section) => section.title
                          .toLowerCase()
                          .contains('responsibilities'))
                      .expand((section) {
                    List<String> temp = [];
                    for (var item in section.items) {
                      temp.add(item.toString());
                    }
                    return temp;
                  }).toList(),
                  // Extract permissions from agent sections
                  kpis: agent.sections
                      .where((section) =>
                          section.title.toLowerCase().contains('performance'))
                      .expand((section) => section.items)
                      .toList(),
                  decisionAuthority: agent.sections
                      .where((section) =>
                          section.title.toLowerCase().contains('authority'))
                      .expand((section) {
                    List<String> temp = [];
                    for (var item in section.items) {
                      temp.add(item.toString());
                    }
                    return temp;
                  }).toList(),
                );

                return Container(
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      right: BorderSide(color: Color(0xffD0D0D0), width: 1),
                      bottom: BorderSide(color: Color(0xffD0D0D0), width: 1),
                    ),
                  ),
                  child: BuildRoleCard(
                    role: role,
                    isSelected: provider.selectedRole?.id == role.id,
                    isBorderRequired: false,
                    isHoverCardRequired: false,
                    selectedColor: Color(0xffF2F2F2),
                    onRoleTap: (selectedRole) {
                      provider.showRoleDetailsPanel(selectedRole);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Widget _buildIconWithTooltip({
    required ManualCreationProvider provider,
    required String iconPath,
    required String tooltipText,
    required VoidCallback onTap,
  }) {
    return _IconWithTooltip(
      iconPath: iconPath,
      tooltipText: tooltipText,
      isSelected:
          _activeIconView == tooltipText, // Check if this icon is selected
      onTap: () {
        // Set the active icon view
        setState(() {
          _activeIconView = tooltipText;
        });
        // Call the original onTap if needed
        onTap();
      },
    );
  }

  generateAttributeSentence(AttributeMetadata attribute) {
    return Text.rich(TextSpan(
        text: "${attribute.displayName} ",
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.black,
          fontFamily: 'TiemposText',
        ),
        children: <InlineSpan>[
          TextSpan(
            text:
                "is ${attribute.keyType} key and ${attribute.description}. Data type is ${attribute.dataType}${attribute.values != "N/A" ? " ( ${attribute.values} )" : ""}. Error message: \"${attribute.errorMessage}\". ${attribute.required} field.",
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Colors.black,
              fontFamily: 'TiemposText',
            ),
          )
        ]));
  }
}

class _IconWithTooltip extends StatefulWidget {
  final String iconPath;
  final String tooltipText;
  final VoidCallback onTap;
  final bool isSelected;

  const _IconWithTooltip({
    required this.iconPath,
    required this.tooltipText,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  State<_IconWithTooltip> createState() => _IconWithTooltipState();
}

// Custom painter for dotted vertical lines
class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  DottedLinePainter({
    required this.color,
    this.dashLength = 5.0,
    this.dashGap = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    double startY = 0;
    while (startY < size.height) {
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashLength),
        paint,
      );
      startY += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// Custom painter for dotted horizontal lines
class HorizontalDottedLinePainter extends CustomPainter {
  final Color color;
  final double dashLength;
  final double dashGap;

  HorizontalDottedLinePainter({
    required this.color,
    this.dashLength = 3.0,
    this.dashGap = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashLength, 0),
        paint,
      );
      startX += dashLength + dashGap;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class _IconWithTooltipState extends State<_IconWithTooltip> {
  bool isHovered = false;
  final GlobalKey _iconKey = GlobalKey();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _removeTooltip();
    super.dispose();
  }

  void _showTooltip() {
    try {
      _removeTooltip();

      // Get the position of the icon
      final RenderBox? renderBox =
          _iconKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final position = renderBox.localToGlobal(Offset.zero);
      final size = renderBox.size;

      _overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          left: position.dx + size.width + 8, // 8px gap from icon
          top: position.dy + (size.height / 2) - 18, // Center vertically
          child: Material(
            color: Colors.transparent,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(2),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xxs,
              ),
              child: Text(
                widget.tooltipText,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ),
        ),
      );

      // Check if the context is still valid before inserting the overlay
      if (!mounted) return;

      Overlay.of(context).insert(_overlayEntry!);
    } catch (e) {
      // Safely handle any errors that might occur when showing the tooltip
      print('Error showing tooltip: $e');
      _overlayEntry = null;
    }
  }

  void _removeTooltip() {
    try {
      if (_overlayEntry != null) {
        _overlayEntry!.remove();
        _overlayEntry = null;
      }
    } catch (e) {
      // Safely handle any errors that might occur when removing the overlay
      print('Error removing tooltip: $e');
      _overlayEntry = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine the icon color based on selected and hover states
    Color iconColor;
    if (widget.isSelected) {
      iconColor = Color(0xff0058FF); // Blue when selected
    } else if (isHovered) {
      iconColor = Color(0xff0058FF); // Blue when hovered
    } else {
      iconColor = Color(0xff797676); // Default gray
    }

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) {
        setState(() => isHovered = true);
        _showTooltip();
      },
      onExit: (_) {
        setState(() => isHovered = false);
        _removeTooltip();
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          key: _iconKey,
          padding: EdgeInsets.all(4), // Add padding for background
          decoration: BoxDecoration(
            color: widget.isSelected
                ? Color(0xff0058FF)
                    .withOpacity(0.1) // Light blue background when selected
                : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: SvgPicture.asset(
            widget.iconPath,
            width: 11,
            height: 11,
            color: iconColor,
            // Add stroke width for bold effect when selected
            colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
          ),
        ),
      ),
    );
  }
}

// Special node classes for empty and terminal nodes
class EmptyNode {
  final int height;

  EmptyNode({required this.height});
}

class TerminalNode {
  final String loName;

  TerminalNode({required this.loName});
}

class PausedNode {
  final String loName;

  PausedNode({required this.loName});
}
