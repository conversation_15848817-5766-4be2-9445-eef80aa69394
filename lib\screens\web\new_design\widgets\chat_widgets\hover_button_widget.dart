import 'package:flutter/material.dart';
import 'package:nsl/theme/spacing.dart';

class HoverButton extends StatefulWidget {
  final Widget icon;
  final VoidCallback? onPressed;

  const HoverButton({super.key, 
    required this.icon,
    this.onPressed,
  });

  @override
  State<HoverButton> createState() => HoverButtonState();
}

class HoverButtonState extends State<HoverButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    final bool isDisabled = widget.onPressed == null;

    return MouseRegion(
      onEnter: isDisabled ? null : (_) => setState(() => isHovered = true),
      onExit: isDisabled ? null : (_) => setState(() => isHovered = false),
      cursor:
          isDisabled ? SystemMouseCursors.forbidden : SystemMouseCursors.click,
      child: Container(
        height: 28,
        width: 28,
        margin: EdgeInsets.symmetric(
            horizontal: AppSpacing.xs, vertical: AppSpacing.sm),
        padding: EdgeInsets.zero,
        decoration: BoxDecoration(
            color: isDisabled
                ? Colors.grey.shade300
                : isHovered
                    ? Theme.of(context).colorScheme.primary
                    : Color(0xffE4EDFF),
            borderRadius: BorderRadius.circular(AppSpacing.lg)),
        child: IconButton(
          padding: EdgeInsets.zero,
          onPressed: widget.onPressed,
          icon: widget.icon,
          iconSize: 18,
          color: isDisabled
              ? Colors.grey.shade500
              : isHovered
                  ? Colors.white
                  : null,
        ),
      ),
    );
  }
}
