import 'dart:async';
import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

// Conditional import for web-specific functionality
import 'custom_audio_widget_stub.dart'
    if (dart.library.html) 'custom_audio_widget_web.dart';

/// A custom audio widget that matches specific design requirements
/// with hover states, three-dots menu, and full JSON configurability.
class CustomAudioWidget extends StatefulWidget {
  /// URL of the audio file to play
  final String? url;

  /// Asset path of the audio file to play
  final String? assetPath;

  /// File path of the audio file to play
  final String? filePath;

  /// List of audio URLs for playlist functionality
  final List<String>? playlist;

  /// Whether to autoplay the audio when the widget is loaded
  final bool autoplay;

  /// Whether to loop the audio
  final bool loop;

  /// Whether to show the three-dots menu
  final bool showOptionsMenu;

  /// Whether to show download option in menu
  final bool showDownloadOption;

  /// Whether to show playback speed option in menu
  final bool showPlaybackSpeedOption;

  /// Title of the audio
  final String? title;

  /// Initial volume (0.0 to 1.0)
  final double initialVolume;

  /// Default border color
  final Color? borderColor;

  /// Hover border color
  final Color? hoverBorderColor;

  /// Pressed/active border color
  final Color? activeBorderColor;

  /// Background color for the widget
  final Color? backgroundColor;

  /// Text color for the widget
  final Color? textColor;

  /// Icon color for the widget
  final Color? iconColor;

  /// Progress bar color
  final Color? progressBarColor;

  /// Buffer bar color
  final Color? bufferBarColor;

  /// Play button icon
  final IconData playIcon;

  /// Pause button icon
  final IconData pauseIcon;

  /// Options menu icon
  final IconData optionsIcon;

  /// Download icon
  final IconData downloadIcon;

  /// Playback speed icon
  final IconData playbackSpeedIcon;

  /// Border radius for the widget
  final double borderRadius;

  /// Border width
  final double borderWidth;

  /// Padding inside the widget
  final EdgeInsets padding;

  /// Available playback speeds
  final List<double> playbackSpeeds;

  /// Callback when playback starts
  final Function()? onPlay;

  /// Callback when playback pauses
  final Function()? onPause;

  /// Callback when download is requested
  final Function()? onDownload;

  /// Callback when playback speed changes
  final Function(double speed)? onPlaybackSpeedChange;

  /// Callback when an error occurs
  final Function(String error)? onError;

  /// Callback when seeking to a position
  final Function(Duration position)? onSeek;

  /// For testing purposes only - set an initial error message
  final String? testErrorMessage;

  /// For testing purposes only - set an initial loading state
  final bool testInitialLoading;

  const CustomAudioWidget({
    super.key,
    this.url,
    this.assetPath,
    this.filePath,
    this.playlist,
    this.autoplay = false,
    this.loop = false,
    this.showOptionsMenu = true,
    this.showDownloadOption = true,
    this.showPlaybackSpeedOption = true,
    this.title,
    this.initialVolume = 0.7,
    this.borderColor,
    this.hoverBorderColor,
    this.activeBorderColor,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.progressBarColor,
    this.bufferBarColor,
    this.playIcon = Icons.play_arrow,
    this.pauseIcon = Icons.pause,
    this.optionsIcon = Icons.more_vert,
    this.downloadIcon = Icons.download,
    this.playbackSpeedIcon = Icons.speed,
    this.borderRadius = 6.0,
    this.borderWidth = 1.0,
    this.padding = const EdgeInsets.all(0.0),
    this.playbackSpeeds = const [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
    this.onPlay,
    this.onPause,
    this.onDownload,
    this.onPlaybackSpeedChange,
    this.onError,
    this.onSeek,
    this.testErrorMessage,
    this.testInitialLoading = false,
  }) : assert(
         url != null ||
             assetPath != null ||
             filePath != null ||
             playlist != null,
         'At least one of url, assetPath, filePath, or playlist must be provided',
       );

  /// Creates a CustomAudioWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the CustomAudioWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "url": "https://example.com/audio.mp3",
  ///   "autoplay": false,
  ///   "loop": false,
  ///   "borderColor": "#e0e0e0",
  ///   "hoverBorderColor": "#2196f3",
  ///   "activeBorderColor": "#1976d2",
  ///   "showOptionsMenu": true,
  ///   "showDownloadOption": true,
  ///   "showPlaybackSpeedOption": true,
  ///   "playbackSpeeds": [0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
  /// }
  /// ```
  factory CustomAudioWidget.fromJson(Map<String, dynamic> json) {
    return CustomAudioWidget(
      // Audio sources
      url: json['url'],
      assetPath: json['assetPath'],
      filePath: json['filePath'],
      playlist:
          json['playlist'] != null ? List<String>.from(json['playlist']) : null,

      // Playback options
      autoplay: json['autoplay'] ?? false,
      loop: json['loop'] ?? false,
      initialVolume: (json['initialVolume'] ?? 0.7).toDouble(),

      // Menu options
      showOptionsMenu: json['showOptionsMenu'] ?? true,
      showDownloadOption: json['showDownloadOption'] ?? true,
      showPlaybackSpeedOption: json['showPlaybackSpeedOption'] ?? true,

      // Content properties
      title: json['title'],

      // Appearance properties
      borderColor: _colorFromJson(json['borderColor']),
      hoverBorderColor: _colorFromJson(json['hoverBorderColor']),
      activeBorderColor: _colorFromJson(json['activeBorderColor']),
      backgroundColor: _colorFromJson(json['backgroundColor']),
      textColor: _colorFromJson(json['textColor']),
      iconColor: _colorFromJson(json['iconColor']),
      progressBarColor: _colorFromJson(json['progressBarColor']),
      bufferBarColor: _colorFromJson(json['bufferBarColor']),

      // Icon properties
      playIcon: _iconFromJson(json['playIcon']) ?? Icons.play_arrow,
      pauseIcon: _iconFromJson(json['pauseIcon']) ?? Icons.pause,
      optionsIcon: _iconFromJson(json['optionsIcon']) ?? Icons.more_vert,
      downloadIcon: _iconFromJson(json['downloadIcon']) ?? Icons.download,
      playbackSpeedIcon:
          _iconFromJson(json['playbackSpeedIcon']) ?? Icons.speed,

      // Layout properties
      borderRadius: (json['borderRadius'] ?? 6.0).toDouble(),
      borderWidth: (json['borderWidth'] ?? 1.0).toDouble(),
      padding:
          json['padding'] != null
              ? EdgeInsets.all((json['padding'] as num).toDouble())
              : const EdgeInsets.all(12.0),

      // Playback speeds
      playbackSpeeds:
          json['playbackSpeeds'] != null
              ? List<double>.from(
                json['playbackSpeeds'].map((x) => x.toDouble()),
              )
              : const [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
    );
  }

  /// Converts a JSON color value to a Flutter Color
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        default:
          return null;
      }
    } else if (colorValue is int) {
      return Color(colorValue);
    }

    return null;
  }

  /// Converts a JSON icon name to a Flutter IconData
  static IconData? _iconFromJson(String? iconName) {
    if (iconName == null) return null;

    switch (iconName) {
      case 'play_arrow':
        return Icons.play_arrow;
      case 'pause':
        return Icons.pause;
      case 'more_vert':
        return Icons.more_vert;
      case 'download':
        return Icons.download;
      case 'speed':
        return Icons.speed;
      case 'more_horiz':
        return Icons.more_horiz;
      case 'settings':
        return Icons.settings;
      default:
        return null;
    }
  }

  /// Converts the CustomAudioWidget to a JSON map
  Map<String, dynamic> toJson() {
    return {
      // Audio sources
      if (url != null) 'url': url,
      if (assetPath != null) 'assetPath': assetPath,
      if (filePath != null) 'filePath': filePath,
      if (playlist != null) 'playlist': playlist,

      // Playback options
      'autoplay': autoplay,
      'loop': loop,
      'initialVolume': initialVolume,

      // Menu options
      'showOptionsMenu': showOptionsMenu,
      'showDownloadOption': showDownloadOption,
      'showPlaybackSpeedOption': showPlaybackSpeedOption,

      // Content properties
      if (title != null) 'title': title,

      // Appearance properties
      if (borderColor != null) 'borderColor': _colorToJson(borderColor!),
      if (hoverBorderColor != null)
        'hoverBorderColor': _colorToJson(hoverBorderColor!),
      if (activeBorderColor != null)
        'activeBorderColor': _colorToJson(activeBorderColor!),
      if (backgroundColor != null)
        'backgroundColor': _colorToJson(backgroundColor!),
      if (textColor != null) 'textColor': _colorToJson(textColor!),
      if (iconColor != null) 'iconColor': _colorToJson(iconColor!),
      if (progressBarColor != null)
        'progressBarColor': _colorToJson(progressBarColor!),
      if (bufferBarColor != null)
        'bufferBarColor': _colorToJson(bufferBarColor!),

      // Icon properties
      'playIcon': _iconToJson(playIcon),
      'pauseIcon': _iconToJson(pauseIcon),
      'optionsIcon': _iconToJson(optionsIcon),
      'downloadIcon': _iconToJson(downloadIcon),
      'playbackSpeedIcon': _iconToJson(playbackSpeedIcon),

      // Layout properties
      'borderRadius': borderRadius,
      'borderWidth': borderWidth,
      'padding': padding.top, // Simplified for symmetric padding
      // Playback speeds
      'playbackSpeeds': playbackSpeeds,
    };
  }

  static String _colorToJson(Color color) {
    final r = (color.r * 255).round().toRadixString(16).padLeft(2, '0');
    final g = (color.g * 255).round().toRadixString(16).padLeft(2, '0');
    final b = (color.b * 255).round().toRadixString(16).padLeft(2, '0');
    return '#$r$g$b';
  }

  static String _iconToJson(IconData icon) {
    if (icon == Icons.play_arrow) return 'play_arrow';
    if (icon == Icons.pause) return 'pause';
    if (icon == Icons.more_vert) return 'more_vert';
    if (icon == Icons.download) return 'download';
    if (icon == Icons.speed) return 'speed';
    return 'play_arrow';
  }

  @override
  State<CustomAudioWidget> createState() => _CustomAudioWidgetState();
}

class _CustomAudioWidgetState extends State<CustomAudioWidget> {
  AudioPlayer _audioPlayer = AudioPlayer();

  bool _isPlaying = false;
  bool _isLoading = false;
  bool _isHovering = false;
  double _volume = 0.7;
  double _playbackSpeed = 1.0;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  String? _errorMessage;
  int _retryCount = 0;
  static const int _maxRetries = 3;

  // For playlist functionality
  List<String> _playlist = [];
  int _currentTrackIndex = 0;

  @override
  void initState() {
    super.initState();

    // Set test error message if provided (for testing purposes)
    if (widget.testErrorMessage != null) {
      _errorMessage = widget.testErrorMessage;
    }

    // Set test loading state if provided (for testing purposes)
    _isLoading = widget.testInitialLoading;

    // Initialize volume and playback speed
    _volume = widget.initialVolume;
    _audioPlayer.setVolume(_volume);
    _audioPlayer.setPlaybackRate(_playbackSpeed);

    // Initialize playlist if provided
    if (widget.playlist != null && widget.playlist!.isNotEmpty) {
      _playlist = List.from(widget.playlist!);
    } else if (widget.url != null) {
      _playlist = [widget.url!];
    } else if (widget.assetPath != null) {
      _playlist = [widget.assetPath!];
    } else if (widget.filePath != null) {
      _playlist = [widget.filePath!];
    }

    // Set up audio player listeners
    _setupAudioPlayerListeners();

    // Load the first track if no error message is set
    if (_playlist.isNotEmpty && _errorMessage == null) {
      debugPrint(
        'CustomAudioWidget: Loading track from playlist: ${_playlist[_currentTrackIndex]}',
      );
      _loadTrack(_currentTrackIndex);
    } else {
      debugPrint(
        'CustomAudioWidget: No playlist or error present. Playlist: $_playlist, Error: $_errorMessage',
      );
    }

    // Autoplay if enabled and no error message is set
    if (widget.autoplay && _errorMessage == null) {
      _play();
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  void _setupAudioPlayerListeners() {
    // Listen for player state changes
    _audioPlayer.onPlayerStateChanged.listen((state) {
      setState(() {
        _isPlaying = state == PlayerState.playing;

        if (state == PlayerState.completed) {
          _handleTrackCompletion();
        }
      });
    });

    // Listen for position changes
    _audioPlayer.onPositionChanged.listen((position) {
      setState(() {
        _position = position;
      });
    });

    // Listen for duration changes
    _audioPlayer.onDurationChanged.listen((duration) {
      setState(() {
        _duration = duration;
      });
    });

    // Listen for errors
    _audioPlayer.onPlayerComplete.listen((_) {
      _handleTrackCompletion();
    });

    // Add error listener for better error handling
    _audioPlayer.onLog.listen((message) {
      debugPrint('CustomAudioWidget: AudioPlayer log: $message');
    });
  }

  void _loadTrack(int index, {bool isRetry = false}) async {
    if (index < 0 || index >= _playlist.length) {
      return;
    }

    if (!isRetry) {
      _retryCount = 0;
    }

    setState(() {
      _isLoading = true;
      _position = Duration.zero;
      _duration = Duration.zero;
      _currentTrackIndex = index;
      _errorMessage = null;
    });

    try {
      await _audioPlayer.stop();

      bool loaded = false;

      // Strategy 1: Try with MediaPlayer mode first (best for streaming)
      try {
        debugPrint('CustomAudioWidget: Trying MediaPlayer mode for loading');
        await _audioPlayer.setPlayerMode(PlayerMode.mediaPlayer);
        final source = _getAudioSource(_playlist[index]);
        await _audioPlayer
            .setSource(source)
            .timeout(
              const Duration(seconds: 30),
              onTimeout: () {
                throw TimeoutException(
                  'Audio loading timed out after 30 seconds',
                  const Duration(seconds: 30),
                );
              },
            );
        loaded = true;
        debugPrint(
          'CustomAudioWidget: Successfully loaded with MediaPlayer mode',
        );
      } catch (e1) {
        debugPrint('CustomAudioWidget: MediaPlayer mode loading failed: $e1');

        // Strategy 2: Try with LowLatency mode
        try {
          debugPrint('CustomAudioWidget: Trying LowLatency mode for loading');
          await _audioPlayer.setPlayerMode(PlayerMode.lowLatency);
          final source = _getAudioSource(_playlist[index]);
          await _audioPlayer
              .setSource(source)
              .timeout(
                const Duration(seconds: 30),
                onTimeout: () {
                  throw TimeoutException(
                    'Audio loading timed out after 30 seconds',
                    const Duration(seconds: 30),
                  );
                },
              );
          loaded = true;
          debugPrint(
            'CustomAudioWidget: Successfully loaded with LowLatency mode',
          );
        } catch (e2) {
          debugPrint('CustomAudioWidget: LowLatency mode loading failed: $e2');

          // Strategy 3: Force UrlSource for HTTP URLs
          if (_playlist[index].startsWith('http')) {
            try {
              debugPrint(
                'CustomAudioWidget: Forcing UrlSource for HTTP URL during loading',
              );
              await _audioPlayer.setPlayerMode(PlayerMode.mediaPlayer);
              final source = UrlSource(_playlist[index]);
              await _audioPlayer
                  .setSource(source)
                  .timeout(
                    const Duration(seconds: 30),
                    onTimeout: () {
                      throw TimeoutException(
                        'Audio loading timed out after 30 seconds',
                        const Duration(seconds: 30),
                      );
                    },
                  );
              loaded = true;
              debugPrint(
                'CustomAudioWidget: Successfully loaded with forced UrlSource',
              );
            } catch (e3) {
              debugPrint('CustomAudioWidget: Forced UrlSource failed: $e3');

              // Strategy 4: Create a completely new AudioPlayer instance
              try {
                debugPrint(
                  'CustomAudioWidget: Creating new AudioPlayer instance',
                );
                final newPlayer = AudioPlayer();
                await newPlayer.setPlayerMode(PlayerMode.mediaPlayer);
                await newPlayer
                    .setSource(UrlSource(_playlist[index]))
                    .timeout(
                      const Duration(seconds: 30),
                      onTimeout: () {
                        throw TimeoutException(
                          'Audio loading timed out after 30 seconds',
                          const Duration(seconds: 30),
                        );
                      },
                    );

                // If successful, replace the old player
                await _audioPlayer.dispose();
                _audioPlayer = newPlayer;
                _setupAudioPlayerListeners();
                loaded = true;
                debugPrint(
                  'CustomAudioWidget: Successfully loaded with new player instance',
                );
              } catch (e4) {
                debugPrint(
                  'CustomAudioWidget: New player instance failed: $e4',
                );

                // Strategy 5: Last resort - show UI anyway and let user try to play
                debugPrint(
                  'CustomAudioWidget: All strategies failed, showing UI anyway',
                );
                loaded = true; // Show the UI regardless
              }
            }
          } else {
            // For non-HTTP sources, show UI anyway
            debugPrint('CustomAudioWidget: Non-HTTP source, showing UI anyway');
            loaded = true;
          }
        }
      }

      if (loaded) {
        setState(() {
          _isLoading = false;
          _retryCount = 0; // Reset retry count on success
        });
      }
    } on TimeoutException catch (e) {
      _handleLoadError(
        'Audio loading timed out. Please check your connection.',
        e.toString(),
      );
    } catch (e) {
      // Debug logging for error analysis
      debugPrint('CustomAudioWidget: Error loading audio: $e');
      debugPrint('CustomAudioWidget: Error type: ${e.runtimeType}');

      // For any audio loading error, just show the UI anyway
      // This allows users to interact with the player even if the format isn't supported
      setState(() {
        _isLoading = false;
        _retryCount = 0;
        // Don't set error message - just show the player UI
      });

      // Still call the error callback for logging purposes
      if (widget.onError != null) {
        widget.onError!('Audio loading failed: $e');
      }
    }
  }

  void _handleLoadError(String userMessage, String technicalError) {
    if (_retryCount < _maxRetries) {
      // Auto-retry with exponential backoff
      _retryCount++;
      final delaySeconds = _retryCount * 2; // 2, 4, 6 seconds

      setState(() {
        _isLoading = false;
        _errorMessage =
            '$userMessage (Retrying in $delaySeconds seconds... $_retryCount/$_maxRetries)';
      });

      Timer(Duration(seconds: delaySeconds), () {
        if (mounted) {
          _loadTrack(_currentTrackIndex, isRetry: true);
        }
      });
    } else {
      setState(() {
        _isLoading = false;
        _errorMessage = '$userMessage (Max retries reached)';
      });

      if (widget.onError != null) {
        widget.onError!('$userMessage: $technicalError');
      }
    }
  }

  void _retryLoadTrack() {
    _retryCount = 0;
    _loadTrack(_currentTrackIndex);
  }

  String _sanitizeAudioUrl(String url) {
    // Clean up the URL to ensure it's properly formatted
    String cleanUrl = url.trim();

    // Ensure proper URL encoding for special characters
    if (cleanUrl.startsWith('http://') || cleanUrl.startsWith('https://')) {
      try {
        final uri = Uri.parse(cleanUrl);
        cleanUrl = uri.toString();
        debugPrint('CustomAudioWidget: Sanitized URL: $cleanUrl');
      } catch (e) {
        debugPrint(
          'CustomAudioWidget: URL parsing failed, using original: $cleanUrl',
        );
      }
    }

    return cleanUrl;
  }

  Source _getAudioSource(String path) {
    // Debug logging for audio source detection
    debugPrint('CustomAudioWidget: Loading audio from path: $path');

    final sanitizedPath = _sanitizeAudioUrl(path);

    if (sanitizedPath.startsWith('http://') ||
        sanitizedPath.startsWith('https://')) {
      debugPrint('CustomAudioWidget: Using UrlSource for: $sanitizedPath');
      return UrlSource(sanitizedPath);
    } else if (sanitizedPath.startsWith('asset:')) {
      final assetPath = sanitizedPath.replaceFirst('asset:', '');
      debugPrint('CustomAudioWidget: Using AssetSource for: $assetPath');
      return AssetSource(assetPath);
    } else if (sanitizedPath.startsWith('assets/')) {
      debugPrint('CustomAudioWidget: Using AssetSource for: $sanitizedPath');
      return AssetSource(sanitizedPath);
    } else if (sanitizedPath.startsWith('file://')) {
      final filePath = sanitizedPath.replaceFirst('file://', '');
      debugPrint('CustomAudioWidget: Using DeviceFileSource for: $filePath');
      return DeviceFileSource(filePath);
    } else {
      debugPrint(
        'CustomAudioWidget: Using DeviceFileSource for: $sanitizedPath',
      );
      return DeviceFileSource(sanitizedPath);
    }
  }

  void _play() async {
    if (_errorMessage != null) {
      _retryLoadTrack();
      return;
    }

    // Update UI immediately to show pause icon
    setState(() {
      _isPlaying = true;
      _isLoading = true;
    });

    try {
      // First try to resume if audio is already loaded
      await _audioPlayer.resume();

      setState(() {
        _isLoading = false;
      });

      if (widget.onPlay != null) {
        widget.onPlay!();
      }

      debugPrint('CustomAudioWidget: Successfully resumed playback');
    } catch (e) {
      debugPrint('CustomAudioWidget: Resume failed: $e');

      // Check if it's a NotSupportedError
      if (e.toString().contains('NotSupportedError') ||
          e.toString().contains('no supported sources')) {
        debugPrint(
          'CustomAudioWidget: Detected NotSupportedError - trying alternative approach',
        );

        // Try to create a new audio player with different settings
        try {
          final newPlayer = AudioPlayer();
          await newPlayer.setPlayerMode(PlayerMode.mediaPlayer);

          // Try with explicit audio source
          final source = UrlSource(_playlist[_currentTrackIndex]);
          await newPlayer.setSource(source);
          await newPlayer.resume();

          // If successful, replace the old player
          await _audioPlayer.dispose();
          _audioPlayer = newPlayer;
          _setupAudioPlayerListeners();

          setState(() {
            _isLoading = false;
          });

          if (widget.onPlay != null) {
            widget.onPlay!();
          }

          debugPrint(
            'CustomAudioWidget: Successfully played with new player instance',
          );
          return; // Exit early if successful
        } catch (e) {
          debugPrint('CustomAudioWidget: New player approach also failed: $e');
        }
      }

      // If resume fails, try to load and play from scratch
      try {
        debugPrint(
          'CustomAudioWidget: Attempting to load and play from scratch',
        );

        if (_playlist.isNotEmpty) {
          final source = _getAudioSource(_playlist[_currentTrackIndex]);
          await _audioPlayer.stop();
          await _audioPlayer.setSource(source);
          await _audioPlayer.resume();

          setState(() {
            _isLoading = false;
          });

          if (widget.onPlay != null) {
            widget.onPlay!();
          }

          debugPrint(
            'CustomAudioWidget: Successfully loaded and played from scratch',
          );
        }
      } catch (e2) {
        debugPrint(
          'CustomAudioWidget: Load and play from scratch also failed: $e2',
        );

        // If that also fails, try with different player modes
        try {
          debugPrint(
            'CustomAudioWidget: Trying with LowLatency mode for play (since it worked for loading)',
          );
          await _audioPlayer.setPlayerMode(PlayerMode.lowLatency);

          if (_playlist.isNotEmpty) {
            final source = _getAudioSource(_playlist[_currentTrackIndex]);
            await _audioPlayer.stop();
            await _audioPlayer.setSource(source);
            await _audioPlayer.resume();

            setState(() {
              _isLoading = false;
            });

            if (widget.onPlay != null) {
              widget.onPlay!();
            }

            debugPrint(
              'CustomAudioWidget: Successfully played with LowLatency mode',
            );
          }
        } catch (e3) {
          debugPrint('CustomAudioWidget: LowLatency mode play failed: $e3');

          // Try with MediaPlayer mode as final attempt
          try {
            debugPrint('CustomAudioWidget: Trying with MediaPlayer mode');
            await _audioPlayer.setPlayerMode(PlayerMode.mediaPlayer);

            if (_playlist.isNotEmpty) {
              final source = UrlSource(_playlist[_currentTrackIndex]);
              await _audioPlayer.stop();
              await _audioPlayer.setSource(source);
              await _audioPlayer.resume();

              setState(() {
                _isLoading = false;
              });

              if (widget.onPlay != null) {
                widget.onPlay!();
              }

              debugPrint(
                'CustomAudioWidget: Successfully played with MediaPlayer mode',
              );
            }
          } catch (e4) {
            debugPrint('CustomAudioWidget: All play attempts failed: $e4');

            // Keep the pause icon showing even if playback failed
            setState(() {
              _isLoading = false;
            });

            // Still don't show error UI, just log for debugging
            if (widget.onError != null) {
              widget.onError!(
                'Failed to play audio after multiple attempts: $e4',
              );
            }
          }
        }
      }
    }
  }

  void _pause() async {
    try {
      await _audioPlayer.pause();

      setState(() {
        _isPlaying = false;
      });

      if (widget.onPause != null) {
        widget.onPause!();
      }
    } catch (e) {
      // Don't show error UI, just log and update state
      debugPrint('CustomAudioWidget: Pause failed: $e');

      setState(() {
        _isPlaying = false;
      });

      if (widget.onError != null) {
        widget.onError!('Failed to pause audio: $e');
      }
    }
  }

  void _seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);

      if (widget.onSeek != null) {
        widget.onSeek!(position);
      }
    } catch (e) {
      // Don't show error UI, just log the error
      debugPrint('CustomAudioWidget: Seek failed: $e');

      if (widget.onError != null) {
        widget.onError!('Failed to seek: $e');
      }
    }
  }

  void _setPlaybackSpeed(double speed) async {
    try {
      debugPrint('CustomAudioWidget: Changing playback speed to ${speed}x');

      await _audioPlayer.setPlaybackRate(speed);
      setState(() {
        _playbackSpeed = speed;
      });

      // Show feedback to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playback speed set to ${speed}x'),
            duration: const Duration(seconds: 1),
            backgroundColor: Colors.blue,
          ),
        );
      }

      if (widget.onPlaybackSpeedChange != null) {
        widget.onPlaybackSpeedChange!(speed);
      }

      debugPrint(
        'CustomAudioWidget: Playback speed successfully changed to ${speed}x',
      );
    } catch (e) {
      debugPrint('CustomAudioWidget: Failed to change playback speed: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to change playback speed: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }

      if (widget.onError != null) {
        widget.onError!('Failed to set playback speed: $e');
      }
    }
  }

  void _handleTrackCompletion() {
    if (widget.loop) {
      _seek(Duration.zero);
      _play();
    } else if (_currentTrackIndex < _playlist.length - 1) {
      _loadTrack(_currentTrackIndex + 1);
      _play();
    } else {
      setState(() {
        _isPlaying = false;
        _position = Duration.zero;
      });
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  void _showOptionsMenu(BuildContext context) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;

    // Get the position of the three dots button
    final buttonPosition = button.localToGlobal(Offset.zero, ancestor: overlay);

    // Position the menu below the three dots button
    final RelativeRect position = RelativeRect.fromLTRB(
      buttonPosition.dx + 100, // Align with the right side of the audio player
      buttonPosition.dy + 30, // Below the three dots button
      buttonPosition.dx + 450, // Right boundary (200px width)
      buttonPosition.dy + 130, // Bottom boundary
    );

    final String? result = await showMenu<String>(
      context: context,
      position: position,
      color: Colors.white,
      constraints: const BoxConstraints(
        minWidth: 185,
        maxWidth: 185,
        minHeight: 96,
        maxHeight: 96,
      ),
      items: [
        if (widget.showDownloadOption)
          PopupMenuItem<String>(
            value: 'download',
            height: 36,
            child: Row(
              children: [
                Icon(widget.downloadIcon, size: 16),
                const SizedBox(width: 8),
                const Text(
                  'Download',
                  style: TextStyle(fontSize: 14, fontFamily: 'Inter'),
                ),
              ],
            ),
          ),
        if (widget.showPlaybackSpeedOption)
          PopupMenuItem<String>(
            value: 'playback_speed',
            height: 36,
            child: Row(
              children: [
                Icon(widget.playbackSpeedIcon, size: 16),
                const SizedBox(width: 8),
                Text(
                  'Playback Speed',
                  style: TextStyle(fontSize: 14, fontFamily: 'Inter'),
                ),
              ],
            ),
          ),
      ],
    );

    if (!mounted) return;

    if (result == 'download') {
      if (widget.onDownload != null) {
        widget.onDownload!();
      } else {
        // Default download implementation
        _downloadCurrentAudio();
      }
    } else if (result == 'playback_speed') {
      _showPlaybackSpeedDialog(this.context);
    }
  }

  void _showPlaybackSpeedDialog(BuildContext context) {
    showDialog<double>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Playback Speed',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children:
                widget.playbackSpeeds.map((speed) {
                  return RadioListTile<double>(
                    title: Text(
                      '${speed}x',
                      style: const TextStyle(fontSize: 16),
                    ),
                    subtitle: Text(
                      _getSpeedDescription(speed),
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    value: speed,
                    groupValue: _playbackSpeed,
                    onChanged: (double? value) {
                      if (value != null) {
                        Navigator.of(context).pop(value);
                      }
                    },
                    activeColor: Colors.blue,
                  );
                }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    ).then((double? speed) {
      if (speed != null && speed != _playbackSpeed) {
        _setPlaybackSpeed(speed);
      }
    });
  }

  String _getSpeedDescription(double speed) {
    switch (speed) {
      case 0.5:
        return 'Half speed';
      case 0.75:
        return 'Slower';
      case 1.0:
        return 'Normal';
      case 1.25:
        return 'Faster';
      case 1.5:
        return 'Much faster';
      case 2.0:
        return 'Double speed';
      default:
        return '${speed}x speed';
    }
  }

  /// Downloads the current audio file directly
  void _downloadCurrentAudio() async {
    if (_playlist.isEmpty) {
      debugPrint('CustomAudioWidget: No audio to download');
      return;
    }

    final audioUrl = _playlist[_currentTrackIndex];
    debugPrint('CustomAudioWidget: Attempting direct download: $audioUrl');

    try {
      if (kIsWeb) {
        // For web: Use direct download approach
        await _downloadFileWeb(audioUrl);
      } else {
        // For mobile/desktop: Use URL launcher as fallback
        final uri = Uri.parse(audioUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          if (mounted) {
            _showDownloadDialog(audioUrl);
          }
        }
      }
    } catch (e) {
      debugPrint('CustomAudioWidget: Download failed: $e');
      if (mounted) {
        _showDownloadDialog(audioUrl);
      }
    }
  }

  /// Downloads file directly on web platform
  Future<void> _downloadFileWeb(String url) async {
    try {
      debugPrint('CustomAudioWidget: Starting direct web download');

      // Use platform-specific download functionality
      downloadAudioFileFromUrl(url);

      // Extract filename from URL for display purposes
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      final filename =
          pathSegments.isNotEmpty
              ? pathSegments.last
              : 'audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      debugPrint('CustomAudioWidget: Direct download initiated for: $filename');

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download started: $filename'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('CustomAudioWidget: Direct download failed: $e');

      // Fallback to opening in new tab
      try {
        openUrlInNewTab(url);
        debugPrint('CustomAudioWidget: Opened in new tab as fallback');
      } catch (e2) {
        debugPrint('CustomAudioWidget: New tab fallback also failed: $e2');
        throw e; // Re-throw to trigger dialog fallback
      }
    }
  }

  /// Shows a dialog with download instructions when automatic download fails
  void _showDownloadDialog(String audioUrl) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Download Audio'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('To download this audio file, please:'),
              const SizedBox(height: 8),
              const Text('1. Right-click the link below'),
              const Text('2. Select "Save link as..." or "Download"'),
              const SizedBox(height: 12),
              SelectableText(
                audioUrl,
                style: const TextStyle(
                  color: Colors.blue,
                  decoration: TextDecoration.underline,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                // Try to open the URL in a new tab
                final uri = Uri.parse(audioUrl);
                if (await canLaunchUrl(uri)) {
                  await launchUrl(uri, mode: LaunchMode.externalApplication);
                }
              },
              child: const Text('Open Link'),
            ),
          ],
        );
      },
    );
  }

  Color _getCurrentBorderColor() {
    if (_isHovering) {
      return const Color(0xFF0058FF); // Light pink hover color (#0058FF)
    } else if (_isPlaying && widget.activeBorderColor != null) {
      return widget.activeBorderColor!;
    } else if (widget.borderColor != null) {
      return widget.borderColor!;
    } else {
      return Colors.grey.shade300;
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else if (screenWidth >= 768) {
      return 12.0; // Small
    } else {
      return 12.0; // Default for very small screens
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 32.0; // Small (768-1024px)
    } else {
      return 32.0; // Default for very small screens
    }
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return const EdgeInsets.symmetric(
        horizontal: 12.0,
        vertical: 4.0,
      ); // Extra Large
    } else if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(
        horizontal: 10.0,
        vertical: 3.0,
      ); // Large
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(
        horizontal: 8.0,
        vertical: 2.0,
      ); // Medium
    } else if (screenWidth >= 768) {
      return const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 1.0,
      ); // Small
    } else {
      return const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 1.0,
      ); // Default for very small screens
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_errorMessage != null) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? Colors.white,
          border: Border.all(
            color: Colors.red.shade300,
            width: widget.borderWidth,
          ),
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                _errorMessage!,
                style: TextStyle(color: Colors.red, fontSize: 14),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Add retry button if max retries reached
            if (_errorMessage!.contains('Max retries reached'))
              IconButton(
                icon: Icon(Icons.refresh, color: Colors.red, size: 20),
                onPressed: _retryLoadTrack,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
                tooltip: 'Retry loading audio',
              ),
          ],
        ),
      );
    }

    final effectiveTextColor = widget.textColor ?? Colors.grey.shade800;
    final effectiveIconColor = widget.iconColor ?? Colors.black;

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.title != null)
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                widget.title!,
                style: TextStyle(
                  fontSize: _getResponsiveFontSize(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Inter',
                  //   color: effectiveTextColor,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          SizedBox(height: 2),

          Container(
            width: 295,
            height: _getResponsiveHeight(context),
            padding: _getResponsivePadding(context),
            decoration: BoxDecoration(
              color:
                  _isHovering
                      ? Colors.white
                      : (widget.backgroundColor ?? Colors.white),
              border: Border.all(
                color: _getCurrentBorderColor(),
                width: widget.borderWidth,
              ),
              borderRadius: BorderRadius.circular(widget.borderRadius),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Play/Pause button
                GestureDetector(
                  onTap: _isLoading ? null : (_isPlaying ? _pause : _play),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color:
                          _isPlaying ? effectiveIconColor : Colors.transparent,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isLoading
                          ? Icons.hourglass_empty
                          : (_isPlaying ? widget.pauseIcon : widget.playIcon),
                      color: _isPlaying ? Colors.white : effectiveIconColor,
                      size: 20,
                    ),
                  ),
                ),

                const SizedBox(width: 5),

                // Progress bar
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Progress slider
                      SliderTheme(
                        data: SliderThemeData(
                          trackHeight: 2,
                          thumbShape: const RoundSliderThumbShape(
                            enabledThumbRadius: 4,
                          ),
                          overlayShape: const RoundSliderOverlayShape(
                            overlayRadius: 10,
                          ),
                          activeTrackColor:
                              widget.progressBarColor ??
                              widget.hoverBorderColor ??
                              Theme.of(context).primaryColor,
                          inactiveTrackColor:
                              widget.bufferBarColor ?? Colors.grey.shade300,
                          thumbColor:
                              widget.progressBarColor ??
                              widget.hoverBorderColor ??
                              Theme.of(context).primaryColor,
                        ),
                        child: Slider(
                          value: _position.inMilliseconds.toDouble(),
                          max:
                              _duration.inMilliseconds > 0
                                  ? _duration.inMilliseconds.toDouble()
                                  : 1.0,
                          onChanged: (value) {
                            _seek(Duration(milliseconds: value.toInt()));
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 5),

                // Duration text
                Text(
                  '${_formatDuration(_position)} / ${_formatDuration(_duration)}',
                  style: TextStyle(fontSize: 12, color: effectiveTextColor),
                ),

                const SizedBox(width: 5),

                // Playback speed indicator
                if (_playbackSpeed != 1.0)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 4,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${_playbackSpeed}x',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                if (_playbackSpeed != 1.0) const SizedBox(width: 5),

                // Speaker icon
                Icon(Icons.volume_up, color: effectiveIconColor, size: 16),

                const SizedBox(width: 5),

                // Divider
                Container(width: 1, height: 16, color: Colors.grey.shade400),

                //  const SizedBox(width: 5),

                // Options menu
                if (widget.showOptionsMenu)
                  GestureDetector(
                    onTap: () => _showOptionsMenu(context),
                    child: Icon(
                      widget.optionsIcon,
                      color: effectiveIconColor,
                      size: 20,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
