import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

// Import your app files (adjust paths as needed)

class HoverNavItem extends StatefulWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTap;
  final bool isActive;

  const HoverNavItem({
    super.key,
    required this.iconPath,
    required this.label,
    required this.onTap,
    this.isActive = false,
  });

  @override
  State<HoverNavItem> createState() => _HoverNavItemState();
}

class _HoverNavItemState extends State<HoverNavItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            // color: widget.isActive 
            //     ? Colors.white 
            //     : (isHovered ? Colors.white : Colors.transparent),
            border: Border.all(
              color: widget.isActive 
                  ? Colors.transparent 
                  : (isHovered ? const Color(0xff0058FF) : Colors.transparent),
              width: 1.0,
            ),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                widget.iconPath,
                width: 12,
                height: 12,
                colorFilter: ColorFilter.mode(
                  (isHovered || widget.isActive)
                      ? Colors.black
                      : Colors.black54,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                widget.label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.black,
                  fontFamily: "TiemposText",
                  fontWeight:
                      widget.isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class HoverNavItems extends StatelessWidget {
  const HoverNavItems({super.key});

  @override
  Widget build(BuildContext context) {
    final currentIndex =
        Provider.of<WebHomeProvider>(context).currentScreenIndex;

    // Make Books active by default if no other specific screen is selected
    final isBooksActive = currentIndex == ScreenConstants.webMyLibrary ||
        (currentIndex != ScreenConstants.webMySolution &&
         currentIndex != ScreenConstants.webMyObject &&
         currentIndex != ScreenConstants.webAgentScreen);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 420,
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            children: [
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    HoverNavItem(
                      iconPath: 'assets/images/books-icon.svg',
                      label: AppLocalizations.of(context)
                          .translate('library.books'),
                      isActive: isBooksActive,
                      onTap: () {
                        Provider.of<WebHomeProvider>(context, listen: false)
                            .currentScreenIndex = ScreenConstants.webMyLibrary;
                      },
                    ),
                    HoverNavItem(
                      iconPath: 'assets/images/square-box-uncheck.svg',
                      label: AppLocalizations.of(context)
                          .translate('library.solutions'),
                      isActive: currentIndex == ScreenConstants.webMySolution,
                      onTap: () {
                        Provider.of<WebHomeProvider>(context, listen: false)
                            .currentScreenIndex = ScreenConstants.webMySolution;
                      },
                    ),
                    HoverNavItem(
                      iconPath: 'assets/images/cube-box.svg',
                      label: AppLocalizations.of(context)
                          .translate('library.objects'),
                      isActive: currentIndex == ScreenConstants.webMyObject,
                      onTap: () {
                        Provider.of<WebHomeProvider>(context, listen: false)
                            .currentScreenIndex = ScreenConstants.webMyObject;
                      },
                    ),
                    HoverNavItem(
                      iconPath: 'assets/images/agent-icon.svg',
                      label: AppLocalizations.of(context)
                          .translate('library.agents'),
                      isActive: currentIndex == ScreenConstants.webAgentScreen,
                      onTap: () {
                        Provider.of<WebHomeProvider>(context, listen: false)
                                .currentScreenIndex =
                            ScreenConstants.webAgentScreen;
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
